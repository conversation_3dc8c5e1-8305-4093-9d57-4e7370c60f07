{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "coconutcraig/laravel-postmark": "^3.2", "doctrine/dbal": "^4.2", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/slack-notification-channel": "^3.5", "laravel/socialite": "^5.15", "laravel/tinker": "^2.9", "socialiteproviders/tiktok": "^5.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.1", "fakerphp/faker": "^1.23", "laravel/pint": "^1.17", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "ide-helpers": ["@php artisan ide-helper:generate", "@php artisan ide-helper:meta", "@php artisan ide-helper:models --nowrite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}