# Project description

## Installation

### Configuring git hooks
This project uses git hooks to ensure that the code is formatted correctly before a commit is made. To configure the git hooks, run the following command:
- `git config core.hooksPath ./back-end/api/.githooks/`

## Sources

### Google
This project requires a Google Cloud project to be set up. Basic OAuth 2.0 credentials are required to work and the consent screen must be configured.

Consent screen requirements:
- Set to `external`

Enabled API's:
- Google Analytics Admin API
- Google Analytics Data API

### Facebook
This project requires a Facebook App to be set up. This can be done from within the [Facebook Developers](https://developers.facebook.com) portal.

#### Facebook login for Business
This project requires a `Facebook Login for Business` to be set up with a configuration as following:
- Settings
   - Client OAuth login on `true`
   - Web OAuth login on `true`
   - Enforce HTTPS on `true`
   - Use Strict Mode for redirect URIs on `true`
   - `Valid OAuth Redirect URIs` should be set to the result of `route("sources.facebook.callback")`
- Configuration
  - `Name`, can be anything.
  - `Login variation`, should be set to `General`
  - `Choose access token`, should be set to `User access token`
  - `Permissions` should be set to
     - `ads_read`
     - `business_management`
     - `instagram_basic`
     - `instagram_manage_insights`
     - `pages_show_list`
     - `read_insights`
     - `pages_read_engagement`
     - `email`
     - `ads_management`

#### Mapping of environment variables:

`OAUTH_FACEBOOK_CLIENT_ID` - Found under `App Settings` -> `Basic` -> `App ID`

`OAUTH_FACEBOOK_CLIENT_SECRET` -  Found under `App Settings` -> `Basic` -> `App Secret`

`OAUTH_FACEBOOK_CONFIG_ID` - ID from configuration above

## Data source
A data source is the connection point for external sources to a dashboard. It has a `sourceable` relation with an external source and it can contain `options` to further configure the connection point.

### Options: Google Analytics Property
For the `GoogleAnalyticsProperty` model the `options` on the `DataSource` structure is as following:

```php
'types' => [ 
    'BOOKING' => [ // Key should be an value from the ReportType enum
        'mapped' // Value is a string representing the event name from the Google Analytics property
    ]
]
```

