<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Settings\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Settings\Support\Mails\HelpdeskRequestMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class HelpdeskControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_store_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.helpdesk.store');

        $response = $this->json('POST', $route);
        $response->assertStatus(401);
    }

    public function test_store_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.helpdesk.store');

        $response = $this->json('POST', $route);
        $response->assertStatus(403);
    }

    public function test_store_validates_request(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $route = route('settings.helpdesk.store');

        $response = $this->json('POST', $route, []);

        $response->assertStatus(422);

        $response->assertJsonValidationErrorFor('subject');
        self::assertContains('The subject field is required.', $response->json('errors.subject'));

        $response->assertJsonValidationErrorFor('message');
        self::assertContains('The message field is required.', $response->json('errors.message'));
    }

    public function test_store_returns_a_200(): void
    {
        Mail::fake();

        $user = User::factory()->withRole(Role::ADMIN)->create();

        Sanctum::actingAs($user);

        $route = route('settings.helpdesk.store');

        $response = $this->json('POST', $route, [
            'subject' => 'test subject',
            'message' => 'test message',
        ]);

        $response->assertSuccessful();

        Mail::assertQueued(HelpdeskRequestMail::class, function (HelpdeskRequestMail $mail) use ($user) {
            return $mail->user->id === $user->id && $mail->messageSubject === 'test subject' && $mail->messageContent === 'test message';
        });
    }
}
