<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Settings\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Settings\Support\Mails\MailchimpAccountDeleteRequestMail;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MailchimpConnectionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.mailchimp.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.mailchimp.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        MailchimpAccount::factory()->count(5);

        $route = route('settings.connections.mailchimp.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'region',
                ],
            ],
            'meta' => [
                'current_page',
                'from',
                'last_page',
                'links' => [
                    '*' => [
                        'url',
                        'label',
                        'active',
                    ],
                ],
                'path',
                'per_page',
                'to',
                'total',
            ],
        ]);
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.mailchimp.destroy', [
            'mailchimpAccount' => MailchimpAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.mailchimp.destroy', [
            'mailchimpAccount' => MailchimpAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(403);
    }

    public function test_destroy_returns_a_200(): void
    {
        Mail::fake();

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $route = route('settings.connections.mailchimp.destroy', [
            'mailchimpAccount' => $mailchimpAccount,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertSuccessful();

        Mail::assertQueued(MailchimpAccountDeleteRequestMail::class, function (MailchimpAccountDeleteRequestMail $mail) use ($mailchimpAccount) {
            return $mail->mailchimpAccountId === $mailchimpAccount->id;
        });
    }
}
