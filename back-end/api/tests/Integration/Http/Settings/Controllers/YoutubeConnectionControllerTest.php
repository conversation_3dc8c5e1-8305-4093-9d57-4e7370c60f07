<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Settings\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Settings\Actions\GetConnectedDataForYoutubeGoogleAccount;
use App\Domains\Settings\Support\Mails\YoutubeGoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class YoutubeConnectionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.youtube.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.youtube.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        YoutubeGoogleAccount::factory()->count(5);

        $route = route('settings.connections.youtube.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                ],
            ],
            'meta' => [
                'current_page',
                'from',
                'last_page',
                'links' => [
                    '*' => [
                        'url',
                        'label',
                        'active',
                    ],
                ],
                'path',
                'per_page',
                'to',
                'total',
            ],
        ]);
    }

    public function test_show_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.youtube.show', [
            'youtubeGoogleAccount' => YoutubeGoogleAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_show_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.youtube.show', [
            'youtubeGoogleAccount' => YoutubeGoogleAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_show_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()->create();

        $route = route('settings.connections.youtube.show', [
            'youtubeGoogleAccount' => $youtubeGoogleAccount,
        ]);

        $this->mock(GetConnectedDataForYoutubeGoogleAccount::class)
            ->shouldReceive('execute')
            ->withArgs(fn (YoutubeGoogleAccount $argsYoutubeGoogleAccount) => $youtubeGoogleAccount->id === $argsYoutubeGoogleAccount->id)
            ->once()
            ->andReturn([
                'youtube_channels' => [
                    [
                        'name' => 'Trantow-Weber',
                        'connection_type' => 'Google Account',
                        'connection_name' => 'Dr. Haven Botsford DDS',
                        'last_synced_at' => '2024-12-02 06:28:39',
                    ],
                ],
            ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
            ],
            'metadata' => [
                'connected_data' => [
                    'youtube_channels' => [
                        '*' => [
                            'name',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                ],
            ],
        ]);

        self::assertEquals($youtubeGoogleAccount->id, $response->json('data.id'));
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.youtube.destroy', [
            'youtubeGoogleAccount' => YoutubeGoogleAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.youtube.destroy', [
            'youtubeGoogleAccount' => YoutubeGoogleAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(403);
    }

    public function test_destroy_returns_a_200(): void
    {
        Mail::fake();

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()->create();

        $route = route('settings.connections.youtube.destroy', [
            'youtubeGoogleAccount' => $youtubeGoogleAccount,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertSuccessful();

        Mail::assertQueued(YoutubeGoogleAccountDeleteRequestMail::class, function (YoutubeGoogleAccountDeleteRequestMail $mail) use ($youtubeGoogleAccount) {
            return $mail->youtubeGoogleAccountId === $youtubeGoogleAccount->id;
        });
    }
}
