<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Settings\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Settings\Actions\GetConnectedDataForGoogleAccount;
use App\Domains\Settings\Support\Mails\GoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class GoogleConnectionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.google.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.google.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        GoogleAccount::factory()->count(5);

        $route = route('settings.connections.google.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                ],
            ],
            'meta' => [
                'current_page',
                'from',
                'last_page',
                'links' => [
                    '*' => [
                        'url',
                        'label',
                        'active',
                    ],
                ],
                'path',
                'per_page',
                'to',
                'total',
            ],
        ]);
    }

    public function test_show_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.google.show', [
            'googleAccount' => GoogleAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_show_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.google.show', [
            'googleAccount' => GoogleAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_show_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $googleAccount = GoogleAccount::factory()->create();

        $route = route('settings.connections.google.show', [
            'googleAccount' => $googleAccount,
        ]);

        $this->mock(GetConnectedDataForGoogleAccount::class)
            ->shouldReceive('execute')
            ->withArgs(fn (GoogleAccount $argsGoogleAccount) => $googleAccount->id === $argsGoogleAccount->id)
            ->once()
            ->andReturn([
                'google_ad_accounts' => [
                    [
                        'name' => 'Trantow-Weber',
                        'connection_type' => 'Google Account',
                        'connection_name' => 'Dr. Haven Botsford DDS',
                        'last_synced_at' => '2024-12-02 06:28:39',
                    ],
                ],
                'google_analytics_properties' => [
                    [
                        'name' => 'Schultz Inc',
                        'connection_type' => 'Google Analytics Account',
                        'connection_name' => 'Goldner-Hegmann',
                        'last_synced_at' => '2024-12-02 06:29:04',
                    ],
                ],
            ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
            ],
            'metadata' => [
                'connected_data' => [
                    'google_ad_accounts' => [
                        '*' => [
                            'name',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                    'google_analytics_properties' => [
                        '*' => [
                            'name',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                ],
            ],
        ]);

        self::assertEquals($googleAccount->id, $response->json('data.id'));
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.google.destroy', [
            'googleAccount' => GoogleAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.google.destroy', [
            'googleAccount' => GoogleAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(403);
    }

    public function test_destroy_returns_a_200(): void
    {
        Mail::fake();

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $googleAccount = GoogleAccount::factory()->create();

        $route = route('settings.connections.google.destroy', [
            'googleAccount' => $googleAccount,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertSuccessful();

        Mail::assertQueued(GoogleAccountDeleteRequestMail::class, function (GoogleAccountDeleteRequestMail $mail) use ($googleAccount) {
            return $mail->googleAccountId === $googleAccount->id;
        });
    }
}
