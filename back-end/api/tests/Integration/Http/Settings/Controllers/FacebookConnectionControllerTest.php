<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Settings\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Settings\Actions\GetConnectedDataForFacebookAccount;
use App\Domains\Settings\Support\Mails\FacebookAccountDeleteRequestMail;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class FacebookConnectionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.facebook.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.facebook.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        FacebookAccount::factory()->count(5);

        $route = route('settings.connections.facebook.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                ],
            ],
            'meta' => [
                'current_page',
                'from',
                'last_page',
                'links' => [
                    '*' => [
                        'url',
                        'label',
                        'active',
                    ],
                ],
                'path',
                'per_page',
                'to',
                'total',
            ],
        ]);
    }

    public function test_show_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.facebook.show', [
            'facebookAccount' => FacebookAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_show_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.facebook.show', [
            'facebookAccount' => FacebookAccount::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_show_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $facebookAccount = FacebookAccount::factory()->create();

        $route = route('settings.connections.facebook.show', [
            'facebookAccount' => $facebookAccount,
        ]);

        $this->mock(GetConnectedDataForFacebookAccount::class)
            ->shouldReceive('execute')
            ->withArgs(fn (FacebookAccount $argsFacebookAccount) => $facebookAccount->id === $argsFacebookAccount->id)
            ->once()
            ->andReturn([
                'facebook_ad_accounts' => [
                    [
                        'name' => 'Earline Paucek',
                        'connection_type' => 'Facebook Business',
                        'connection_name' => 'Zola Hahn',
                        'last_synced_at' => '2024-12-02 06:44:23',
                    ],
                ],
                'facebook_pages' => [
                    [
                        'name' => 'Stacy Moore III',
                        'connection_type' => 'Facebook Business',
                        'connection_name' => 'Dr. Edwardo Nikolaus Jr.',
                        'last_synced_at' => '2024-12-02 06:44:45',
                    ],
                ],
                'instagram_accounts' => [
                    [
                        'username' => null,
                        'connection_type' => 'Facebook Page',
                        'connection_name' => 'Lolita Wyman MD',
                        'last_synced_at' => '2024-12-02 06:45:02',
                    ],
                ],
            ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
            ],
            'metadata' => [
                'connected_data' => [
                    'facebook_ad_accounts' => [
                        '*' => [
                            'name',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                    'facebook_pages' => [
                        '*' => [
                            'name',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                    'instagram_accounts' => [
                        '*' => [
                            'username',
                            'connection_type',
                            'connection_name',
                            'last_synced_at',
                        ],
                    ],
                ],
            ],
        ]);

        self::assertEquals($facebookAccount->id, $response->json('data.id'));
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $route = route('settings.connections.facebook.destroy', [
            'facebookAccount' => FacebookAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('settings.connections.facebook.destroy', [
            'facebookAccount' => FacebookAccount::factory()->create(),
        ]);

        $response = $this->json('DELETE', $route);
        $response->assertStatus(403);
    }

    public function test_destroy_returns_a_200(): void
    {
        Mail::fake();

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $facebookAccount = FacebookAccount::factory()->create();

        $route = route('settings.connections.facebook.destroy', [
            'facebookAccount' => $facebookAccount,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertSuccessful();

        Mail::assertQueued(FacebookAccountDeleteRequestMail::class, function (FacebookAccountDeleteRequestMail $mail) use ($facebookAccount) {
            return $mail->facebookAccountId === $facebookAccount->id;
        });
    }
}
