<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Dashboard\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Actions\Widgets\GetStatisticsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use Carbon\CarbonInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class WidgetControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_statistics_returns_a_401_when_not_logged_in(): void
    {
        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.statistics', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertStatus(401);
    }

    public function test_statistics_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->create());

        $this->mock(GetStatisticsForWidget::class)
            ->shouldReceive('single')
            ->andReturn($this->getDefaultWidgetAnalytics())
            ->once();

        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.statistics', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'values' => [
                    '0' => [
                        'date',
                        'value',
                        'valueFormatted',
                    ],
                ],
                'total',
                'totalFormatted',
                'comparison',
                'comparisonFormatted',
                'accuracy',
            ],
        ]);
    }

    public function test_statistics_validates_request(): void
    {
        self::statistics_validates_request($this, 'dashboard.widget.statistics');
    }

    public static function statistics_validates_request(TestCase $testCase, string $routeName, ?WidgetDetailScope $widgetDetailScope = null): void
    {
        $widget = Widget::factory()->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$widget->section->dashboard_id],
        ]));

        $routeAttributes = [
            'widgetDetailScope' => $widgetDetailScope?->value,
            'widget' => $widget->id,
            'start_date' => 'invalid',
            'end_date' => 'invalid',
            'channels' => ['test'],
            'regions' => ['test'],
            'business_units' => ['test'],
        ];
        $route = route($routeName, $routeAttributes);

        $response = $testCase->json('GET', $route);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'start_date',
            'end_date',
            'channels',
            'regions',
            'business_units',
        ]);

        $response->assertJsonFragment([
            'The start date field must match the format Y-m-d.',
        ]);
        $response->assertJsonFragment([
            'The end date field must match the format Y-m-d.',
        ]);
        $response->assertJsonFragment([
            'The channels field must be a string.',
        ]);
        $response->assertJsonFragment([
            'The regions field must be a string.',
        ]);
        $response->assertJsonFragment([
            'The business units field must be a string.',
        ]);

        $routeAttributes = [
            'widgetDetailScope' => $widgetDetailScope?->value,
            'widget' => $widget->id,
            'start_date' => null,
            'end_date' => null,
            'channels' => null,
            'regions' => null,
            'business_units' => null,
        ];
        $route = route($routeName, $routeAttributes);

        $response = $testCase->json('GET', $route);
        $response->assertStatus(200);
    }

    public function test_statistics_sets_filter(): void
    {
        self::statistics_sets_filter($this, 'dashboard.widget.statistics', 'single');
    }

    public static function statistics_sets_filter(TestCase $testCase, string $routeName, string $method, ?WidgetDetailScope $widgetDetailScope = null): void
    {
        Sanctum::actingAs(User::factory()->create());

        $testCase->mock(GetStatisticsForWidget::class)
            ->shouldReceive($method)
            ->andReturn($widgetDetailScope ? ['Test' => self::getDefaultWidgetAnalytics()] : self::getDefaultWidgetAnalytics())
            ->withArgs(function (Widget $widget, WidgetAnalyticsFilter $filter) use ($testCase) {
                $testCase->assertInstanceOf(CarbonInterface::class, $filter->getStartDate());
                $testCase->assertSame('2021-01-01', $filter->getStartDate()->startOfDay()->toDateString());

                $testCase->assertInstanceOf(CarbonInterface::class, $filter->getEndDate());
                $testCase->assertSame('2022-01-01', $filter->getEndDate()->endOfDay()->toDateString());

                $testCase->assertIsArray($filter->getChannels());
                $testCase->assertContains('channel', $filter->getChannels());

                $testCase->assertIsArray($filter->getRegions());
                $testCase->assertContains('region', $filter->getRegions());

                $testCase->assertIsArray($filter->getBusinessUnits());
                $testCase->assertContains('business_unit', $filter->getBusinessUnits());

                return true;
            })
            ->once();

        $widget = Widget::factory()->create();

        $routeAttributes = [
            'widgetDetailScope' => $widgetDetailScope?->value,
            'widget' => $widget->id,
            'start_date' => '2021-01-01',
            'end_date' => '2022-01-01',
            'channels' => 'channel',
            'regions' => 'region',
            'business_units' => 'business_unit',
        ];
        $route = route($routeName, $routeAttributes);

        $response = $testCase->json('GET', $route);
        $response->assertSuccessful();
    }

    public static function getDefaultWidgetAnalytics(): WidgetAnalytics
    {
        $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
        $widgetAnalyticsDataPoint->setDate(now());
        $widgetAnalyticsDataPoint->setValue(1);
        $widgetAnalyticsDataPoint->setValueFormatted('1');

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData([$widgetAnalyticsDataPoint]);
        $widgetAnalytics->setTotal(2);
        $widgetAnalytics->setTotalFormatted('2');
        $widgetAnalytics->setComparison(3);
        $widgetAnalytics->setComparisonFormatted('3');
        $widgetAnalytics->setWidgetAccuracy(WidgetAccuracy::DAY);

        return $widgetAnalytics;
    }
}
