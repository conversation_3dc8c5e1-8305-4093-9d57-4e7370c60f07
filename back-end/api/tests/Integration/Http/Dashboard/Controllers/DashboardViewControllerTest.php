<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Dashboard\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Actions\Dashboard\StoreDashboardViewForDashboard;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DashboardViewControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_store_returns_a_401_when_not_logged_in(): void
    {
        $dashboard = Dashboard::factory()->create();
        $route = route('dashboard.view.store', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('POST', $route);

        $response->assertStatus(401);
    }

    public function test_store_calls_action(): void
    {
        $dashboard = Dashboard::factory()->create();
        $route = route('dashboard.view.store', [
            'dashboard' => $dashboard->id,
        ]);

        $this->actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $this->mock(StoreDashboardViewForDashboard::class)
            ->shouldReceive('execute')
            ->withArgs(function ($dashboard, $name, $widgetAnalyticsFilter) {
                $this->assertSame('Name', $name);
                $this->assertTrue($dashboard instanceof Dashboard);
                $this->assertTrue($widgetAnalyticsFilter instanceof WidgetAnalyticsFilter);

                return true;
            })
            ->once()
            ->andReturn(DashboardView::factory()->create());

        $response = $this->json('POST', $route, [
            'name' => 'Name',
        ]);

        $response->assertStatus(201);
    }

    public function test_store_returns_resource(): void
    {
        $dashboard = Dashboard::factory()->create();
        $route = route('dashboard.view.store', [
            'dashboard' => $dashboard->id,
        ]);

        $this->actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $response = $this->json('POST', $route, [
            'name' => 'Name',
            'start_date' => now()->subDay()->toDateString(),
            'end_date' => now()->toDateString(),
            'channels' => implode(',', ['channel']),
            'regions' => implode(',', ['BE']),
            'business_units' => implode(',', ['business_unit']),
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'start_date',
                'end_date',
                'channels',
                'regions',
                'business_units',
            ],
        ]);
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $dashboardView = DashboardView::factory()->create();

        $route = route('dashboard.view.destroy', [
            'dashboard' => $dashboardView->dashboard_id,
            'dashboard_view' => $dashboardView->id,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertStatus(401);
    }

    public function test_destroy_deletes_model(): void
    {
        $dashboardView = DashboardView::factory()->create();

        $route = route('dashboard.view.destroy', [
            'dashboard' => $dashboardView->dashboard_id,
            'dashboard_view' => $dashboardView->id,
        ]);

        $this->actingAs(User::factory()->create([
            'dashboards' => [$dashboardView->dashboard_id],
        ]));

        $response = $this->json('DELETE', $route);
        $response->assertStatus(204);

        $this->assertModelMissing(
            $dashboardView
        );
    }

    public function test_destroy_does_not_delete_non_related_model(): void
    {
        $relatedDashboardView = DashboardView::factory()->create();
        $nonRelatedDashboardView = DashboardView::factory()->create();

        $route = route('dashboard.view.destroy', [
            'dashboard' => $nonRelatedDashboardView->dashboard_id,
            'dashboard_view' => $relatedDashboardView->id,
        ]);

        $this->actingAs(User::factory()->create([
            'dashboards' => [
                $relatedDashboardView->dashboard_id,
                $nonRelatedDashboardView->dashboard_id,
            ],
        ]));

        $response = $this->json('DELETE', $route);
        $response->assertStatus(403);
    }
}
