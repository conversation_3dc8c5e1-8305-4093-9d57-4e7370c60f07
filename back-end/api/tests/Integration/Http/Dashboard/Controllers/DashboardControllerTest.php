<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Dashboard\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('dashboard.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->create());

        Dashboard::factory()->count(5)->create();

        $route = route('dashboard.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'title',
                ],
            ],
        ]);
    }

    public function test_show_returns_a_401_when_not_logged_in(): void
    {
        $dashboard = Dashboard::factory()->create();
        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertStatus(401);
    }

    public function test_show_returns_a_200(): void
    {
        $dashboard = Dashboard::factory()->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'title',
            ],
        ]);
    }

    public function test_show_loads_data_sources_relation(): void
    {
        $dashboard = Dashboard::factory()
            ->has(DataSource::factory())
            ->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'data_sources' => [
                    '0' => [
                        'id',
                    ],
                ],
            ],
        ]);
    }

    public function test_show_loads_data_sources_meta_data(): void
    {
        $dashboard = Dashboard::factory()
            ->has(DataSource::factory([
                'business_unit' => 'Global',
                'channel' => 'Generic',
                'region' => 'NL',
            ]))
            ->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $dashboardView = DashboardView::factory()->create([
            'dashboard_id' => $dashboard->id,
            'name' => 'Test',
            'regions' => ['BE'],
            'channels' => ['channel'],
            'business_units' => ['business_unit'],
            'start_date' => '2021-01-01',
            'end_date' => '2021-01-01',
        ]);

        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonFragment([
            'metadata' => [
                'business_units' => [
                    'Global',
                ],
                'channels' => [
                    'Generic',
                ],
                'regions' => [
                    'NL',
                ],
                'views' => [
                    [
                        'id' => $dashboardView->id,
                        'dashboard_id' => $dashboard->id,
                        'name' => 'Test',
                        'regions' => ['BE'],
                        'channels' => ['channel'],
                        'business_units' => ['business_unit'],
                        'start_date' => '2021-01-01',
                        'end_date' => '2021-01-01',
                    ],
                ],
            ],
        ]);
    }

    public function test_show_loads_sections_relation(): void
    {
        $dashboard = Dashboard::factory()
            ->has(Section::factory())
            ->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'sections' => [
                    '0' => [
                        'id',
                        'title',
                    ],
                ],
            ],
        ]);
    }

    public function test_show_loads_widgets_relation(): void
    {
        $section = Section::factory()->has(Widget::factory());
        $dashboard = Dashboard::factory()
            ->has($section)
            ->create();

        Sanctum::actingAs(User::factory()->create([
            'dashboards' => [$dashboard->id],
        ]));

        $route = route('dashboard.show', [
            'dashboard' => $dashboard->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'sections' => [
                    '0' => [
                        'widgets' => [
                            '0' => [
                                'id',
                                'title',
                                'type',
                                'data_type',
                                'color',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }
}
