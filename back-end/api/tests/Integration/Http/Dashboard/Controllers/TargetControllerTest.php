<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Dashboard\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Models\Widget;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class TargetControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_update_returns_a_401_when_not_logged_in(): void
    {
        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.target.update', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('PATCH', $route);

        $response->assertStatus(401);
    }

    public function test_update_returns_a_200_and_updates_target(): void
    {
        Sanctum::actingAs(User::factory()->create());

        $widget = Widget::factory()->create(['target' => null]);
        $route = route('dashboard.widget.target.update', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('PATCH', $route, [
            'target' => 100.5,
        ]);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'title',
                'data_type',
                'type',
                'color',
                'target',
            ],
        ]);
        $response->assertJsonPath('data.target', 100.5);

        $this->assertDatabaseHas('widgets', [
            'id' => $widget->id,
            'target' => 100.5,
        ]);
    }

    public function test_update_validates_request(): void
    {
        $widget = Widget::factory()->create();

        Sanctum::actingAs(User::factory()->create());

        $route = route('dashboard.widget.target.update', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('PATCH', $route, [
            'target' => 'invalid',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'target',
        ]);

        $response = $this->json('PATCH', $route, []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'target',
        ]);

        $response = $this->json('PATCH', $route, [
            'target' => 100.5,
        ]);

        $response->assertStatus(200);
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.target.destroy', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_200_and_clears_target(): void
    {
        Sanctum::actingAs(User::factory()->create());

        $widget = Widget::factory()->create(['target' => 100.5]);
        $route = route('dashboard.widget.target.destroy', [
            'widget' => $widget->id,
        ]);

        $response = $this->json('DELETE', $route);

        $response->assertSuccessful();
        $response->assertJsonPath('message', 'Target value cleared successfully');

        $this->assertDatabaseHas('widgets', [
            'id' => $widget->id,
            'target' => null,
        ]);
    }
}
