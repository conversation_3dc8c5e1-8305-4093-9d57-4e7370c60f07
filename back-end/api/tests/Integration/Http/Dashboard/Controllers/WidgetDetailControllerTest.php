<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Dashboard\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Actions\Widgets\GetStatisticsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class WidgetDetailControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_statistics_returns_a_401_when_not_logged_in(): void
    {
        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.detail.statistics', [
            'widgetDetailScope' => WidgetDetailScope::DATASOURCE,
            'widget' => $widget->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertStatus(401);
    }

    public function test_statistics_returns_a_404_for_missing_enum_type(): void
    {
        Sanctum::actingAs(User::factory()->create());

        $widget = Widget::factory()->create();
        $route = route('dashboard.widget.detail.statistics', [
            'widgetDetailScope' => 'NON_EXISTING',
            'widget' => $widget->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertStatus(404);
    }

    public function test_statistics_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->create());

        $widget = Widget::factory()->create();

        $this->mock(GetStatisticsForWidget::class)
            ->shouldReceive('detail')
            ->withArgs(function (Widget $routeWidget, WidgetAnalyticsFilter $widgetAnalyticsFilter, WidgetDetailScope $widgetDetailScope) use ($widget) {
                self::assertEquals($routeWidget->id, $widget->id);
                self::assertEquals(WidgetDetailScope::DATASOURCE, $widgetDetailScope);

                return true;
            })
            ->andReturn(['Test' => WidgetControllerTest::getDefaultWidgetAnalytics()])
            ->once();

        $route = route('dashboard.widget.detail.statistics', [
            'widgetDetailScope' => WidgetDetailScope::DATASOURCE,
            'widget' => $widget->id,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'Test' => [
                    'values' => [
                        '0' => [
                            'date',
                            'value',
                            'valueFormatted',
                        ],
                    ],
                    'total',
                    'totalFormatted',
                    'comparison',
                    'comparisonFormatted',
                    'accuracy',
                ],
            ],
        ]);
    }

    public function test_statistics_validates_request(): void
    {
        WidgetControllerTest::statistics_validates_request($this, 'dashboard.widget.detail.statistics', WidgetDetailScope::DATASOURCE);
    }

    public function test_statistics_sets_filter(): void
    {
        WidgetControllerTest::statistics_sets_filter($this, 'dashboard.widget.detail.statistics', 'detail', WidgetDetailScope::DATASOURCE);
    }
}
