<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Sources\Facebook\Controllers;

use App\Domains\Sources\Meta\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\Meta\Actions\Authentication\UpdateFacebookAccountBySocialiteUser;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class AuthenticationControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_connect_returns_a_200(): void
    {
        $returnUrl = 'https://example.com/oauth/callback';

        $this->mock(GenerateOAuthRedirectLink::class)
            ->shouldReceive('execute')
            ->with(route('sources.facebook.callback'))
            ->andReturn($returnUrl);

        $route = route('sources.facebook.connect');

        $response = $this
            ->json('GET', $route);

        $response->assertSuccessful();
        $response->assertExactJson([
            'data' => [
                'url' => $returnUrl,
            ],
        ]);
    }

    public function test_callback_returns_a_200(): void
    {
        $this->mock(UpdateFacebookAccountBySocialiteUser::class)
            ->shouldReceive('execute')
            ->andReturn(FacebookAccount::factory()->create());

        $user = Mockery::mock('Laravel\Socialite\Two\User');

        $provider = Mockery::mock('Laravel\Socialite\Two\FacebookProvider');
        $provider->shouldReceive('stateless');
        $provider->shouldReceive('user')
            ->andReturn($user);

        Socialite::shouldReceive('driver')
            ->with('facebook')
            ->andReturn($provider);

        $route = route('sources.facebook.callback', [
            'code' => 'test',
        ]);

        $response = $this->json('GET', $route);
        $response->assertRedirect(config('app.frontend_url').'/connect?provider=facebook&state=success');
    }
}
