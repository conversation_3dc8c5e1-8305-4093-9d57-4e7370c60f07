<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Sources\Youtube\Controllers;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\Youtube\Actions\Authentication\UpdateYoutubeGoogleAccountBySocialiteUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class AuthenticationControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_connect_returns_a_200(): void
    {
        $returnUrl = 'https://example.com/oauth/callback';

        $this->mock(GenerateOAuthRedirectLink::class)
            ->shouldReceive('execute')
            ->with(route('sources.youtube.callback'))
            ->andReturn($returnUrl);

        $route = route('sources.youtube.connect');

        $response = $this
            ->json('GET', $route);

        $response->assertSuccessful();
        $response->assertExactJson([
            'data' => [
                'url' => $returnUrl,
            ],
        ]);
    }

    public function test_callback_returns_a_200(): void
    {
        $this->mock(UpdateYoutubeGoogleAccountBySocialiteUser::class)
            ->shouldReceive('execute')
            ->andReturn(YoutubeGoogleAccount::factory()->create());

        $user = Mockery::mock('Laravel\Socialite\Two\User');

        $provider = Mockery::mock('Laravel\Socialite\Two\GoogleProvider');
        $provider->shouldReceive('stateless');
        $provider->shouldReceive('user')
            ->andReturn($user);

        Socialite::shouldReceive('buildProvider')
            ->andReturn($provider);

        $route = route('sources.youtube.callback', [
            'authuser' => 'test',
            'scope' => implode(' ', [
                'https://www.googleapis.com/auth/youtube.readonly',
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
            ]),
        ]);

        $response = $this->json('GET', $route);
        $response->assertRedirect(config('app.frontend_url').'/connect?provider=youtube&state=success');
    }

    public function test_callback_returns_a_redirect_for_missing_scopes(): void
    {
        $this->mock(UpdateYoutubeGoogleAccountBySocialiteUser::class)
            ->shouldReceive('execute')
            ->andReturn(YoutubeGoogleAccount::factory()->create());

        $user = Mockery::mock('Laravel\Socialite\Two\User');

        $provider = Mockery::mock('Laravel\Socialite\Two\GoogleProvider');
        $provider->shouldReceive('stateless');
        $provider->shouldReceive('user')
            ->andReturn($user);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn($provider);

        $requiredScopes = [
            'https://www.googleapis.com/auth/youtube.readonly',
            'https://www.googleapis.com/auth/userinfo.profile',
            'https://www.googleapis.com/auth/userinfo.email',
        ];

        foreach ($requiredScopes as $requiredScope) {
            $currentScopes = array_diff($requiredScopes, [$requiredScope]);

            $route = route('sources.youtube.callback', [
                'authuser' => 'test',
                'scope' => implode(' ', $currentScopes),
            ]);

            $response = $this->json('GET', $route);
            $response->assertRedirect(config('app.frontend_url').'/connect?provider=youtube&state=missing_scopes');
        }
    }
}
