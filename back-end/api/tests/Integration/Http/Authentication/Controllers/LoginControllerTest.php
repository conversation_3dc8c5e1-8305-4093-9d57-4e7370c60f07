<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Authentication\Controllers;

use App\Domains\Authentication\Actions\GetUserFromCredentials;
use App\Domains\Authentication\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class LoginControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_login_returns_a_422(): void
    {
        $route = route('auth.login');

        $response = $this->json('POST', $route);

        $response->assertStatus(422);
        $response->assertJsonValidationErrorFor('email');
        $response->assertJsonValidationErrorFor('password');
    }

    public function test_login_returns_a_401_for_missing_user(): void
    {
        $route = route('auth.login');

        $this->mock(GetUserFromCredentials::class)
            ->shouldReceive('execute')
            ->withArgs(['<EMAIL>', 'password'])
            ->andReturn(null)
            ->once();

        $data = [
            'email' => '<EMAIL>',
            'password' => 'password',
        ];

        $response = $this->json('POST', $route, $data);

        $response->assertStatus(401);
    }

    public function test_login_returns_a_200(): void
    {
        $user = User::factory()->create();

        $route = route('auth.login');

        $this->mock(GetUserFromCredentials::class)
            ->shouldReceive('execute')
            ->withArgs(['<EMAIL>', 'password'])
            ->andReturn($user)
            ->once();

        $data = [
            'email' => '<EMAIL>',
            'password' => 'password',
        ];

        $response = $this
            ->json('POST', $route, $data);

        $response->assertSuccessful();
    }

    public function test_logout_returns_a_401_when_not_logged_in(): void
    {
        $route = route('auth.logout');

        $response = $this->json('GET', $route);

        $response->assertStatus(401);
    }

    public function test_logout_returns_a_200(): void
    {
        $user = User::factory()->create();

        Sanctum::actingAs($user);

        $route = route('auth.logout');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
    }

    public function test_me_returns_a_401_when_not_logged_in(): void
    {
        $route = route('auth.me');

        $response = $this->json('GET', $route);

        $response->assertStatus(401);
    }

    public function test_me_returns_a_200(): void
    {
        $user = User::factory()->create();

        Sanctum::actingAs($user);

        $route = route('auth.me');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure(['data' => ['id']]);
    }
}
