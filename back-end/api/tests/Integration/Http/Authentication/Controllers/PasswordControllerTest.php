<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Authentication\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Mails\PasswordResetMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class PasswordControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_password_reset_link_can_be_requested(): void
    {
        Mail::fake();

        $user = User::factory()->create();

        $response = $this->json('POST', route('auth.password.request'), [
            'email' => $user->email,
        ]);

        $response->assertStatus(200);
        Mail::assertQueued(PasswordResetMail::class, function (PasswordResetMail $mail) use ($user) {
            return $mail->email === $user->email;
        });
    }

    public function test_password_reset_link_request_validates_email(): void
    {
        $response = $this->json('POST', route('auth.password.request'), [
            'email' => 'not-an-email',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['email']);
    }

    public function test_password_can_be_reset(): void
    {
        $user = User::factory()->create();
        $token = Password::createToken($user);

        $response = $this->json('POST', route('auth.password.reset'), [
            'token' => $token,
            'email' => $user->email,
            'password' => 'new-password',
            'password_confirmation' => 'new-password',
        ]);

        $response->assertStatus(200);
        $this->assertTrue(Hash::check('new-password', $user->fresh()->password));
    }

    public function test_password_reset_validates_input(): void
    {
        $response = $this->json('POST', route('auth.password.reset'), [
            'token' => '',
            'email' => 'not-an-email',
            'password' => 'short',
            'password_confirmation' => 'different',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['token', 'email', 'password']);
    }
}
