<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Authentication\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use App\Domains\Authentication\Support\Mails\AccountCreatedMail;
use App\Domains\Dashboard\Models\Dashboard;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_a_401_when_not_logged_in(): void
    {
        $route = route('user.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_index_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('user.index');

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_index_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        User::factory()->count(5)->create();

        $route = route('user.index');

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'role',
                    'email',
                ],
            ],
            'meta' => [
                'current_page',
                'from',
                'last_page',
                'links' => [
                    '*' => [
                        'url',
                        'label',
                        'active',
                    ],
                ],
                'path',
                'per_page',
                'to',
                'total',
            ],
        ]);
    }

    public function test_show_returns_a_401_when_not_logged_in(): void
    {
        $route = route('user.show', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(401);
    }

    public function test_show_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('user.show', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('GET', $route);
        $response->assertStatus(403);
    }

    public function test_show_returns_a_200(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $user = User::factory()->create();
        $dashboard = Dashboard::factory()->create();

        $route = route('user.show', [
            'user' => $user,
        ]);

        $response = $this->json('GET', $route);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'role',
                'email',
            ],
            'metadata' => [
                'dashboards' => [
                    '*' => [
                        'id',
                        'title',
                    ],
                ],
            ],
        ]);

        self::assertEquals($user->id, $response->json('data.id'));
    }

    public function test_store_returns_a_401_when_not_logged_in(): void
    {
        $route = route('user.store');

        $response = $this->json('POST', $route);
        $response->assertStatus(401);
    }

    public function test_store_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('user.store');

        $response = $this->json('POST', $route);
        $response->assertStatus(403);
    }

    public function test_store_creates_user_model(): void
    {
        Mail::fake();

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $route = route('user.store');

        $response = $this->json('POST', $route, [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => Role::ADMIN,
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'role',
                'email',
            ],
        ]);

        self::assertDatabaseHas(
            'users',
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'role' => Role::ADMIN->value,
            ]
        );

        Mail::assertQueued(AccountCreatedMail::class, function (AccountCreatedMail $mail) {
            return $mail->email === '<EMAIL>';
        });
    }

    public function test_store_validates_request(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $route = route('user.store');

        $response = $this->json('POST', $route);

        $response->assertStatus(422);

        $response->assertJsonValidationErrorFor('name');
        self::assertContains('The name field is required.', $response->json('errors.name'));

        $response->assertJsonValidationErrorFor('email');
        self::assertContains('The email field is required.', $response->json('errors.email'));

        $response->assertJsonValidationErrorFor('role');
        self::assertContains('The role field is required.', $response->json('errors.role'));

        User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->json('POST', $route, [
            'email' => '<EMAIL>',
            'role' => 'not_a_enum',
        ]);

        $response->assertStatus(422);

        $response->assertJsonValidationErrorFor('email');
        self::assertContains('The email has already been taken.', $response->json('errors.email'));

        $response->assertJsonValidationErrorFor('role');
        self::assertContains('The selected role is invalid.', $response->json('errors.role'));
    }

    public function test_update_returns_a_401_when_not_logged_in(): void
    {
        $route = route('user.update', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('PATCH', $route);
        $response->assertStatus(401);
    }

    public function test_update_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('user.update', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('PATCH', $route);
        $response->assertStatus(403);
    }

    public function test_update_patches_user_model(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $user = User::factory()->create();
        $route = route('user.update', [
            'user' => $user,
        ]);

        $dashboard = Dashboard::factory()->create();

        self::assertNotEquals('Changed name', $user->name);
        self::assertNotEquals('<EMAIL>', $user->email);
        self::assertNotEquals(Role::ADMIN, $user->role);

        $response = $this->json('PATCH', $route, [
            'name' => 'Changed name',
            'email' => '<EMAIL>',
            'role' => Role::ADMIN,
            'dashboards' => [$dashboard->id],
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'role',
                'email',
                'dashboards',
            ],
        ]);

        $user->refresh();

        self::assertEquals('Changed name', $user->name);
        self::assertEquals('<EMAIL>', $user->email);
        self::assertEquals(Role::ADMIN, $user->role);

        self::assertIsArray($response->json('data.dashboards'));
        self::assertContains($dashboard->id, $response->json('data.dashboards'));
    }

    public function test_update_validates_request(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $user = User::factory()->create();
        $route = route('user.update', [
            'user' => $user,
        ]);

        $response = $this->json('PATCH', $route, [
            'email' => $user->email,
            'dashboards' => 'not_an_array',
        ]);

        $response->assertStatus(422);

        $response->assertJsonValidationErrorFor('name');
        self::assertContains('The name field is required.', $response->json('errors.name'));

        $response->assertJsonValidationErrorFor('role');
        self::assertContains('The role field is required.', $response->json('errors.role'));

        $response->assertJsonValidationErrorFor('dashboards');
        self::assertContains('The dashboards field must be an array.', $response->json('errors.dashboards'));

        $response->assertJsonMissingValidationErrors('email');
    }

    public function test_destroy_returns_a_401_when_not_logged_in(): void
    {
        $route = route('user.destroy', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('PATCH', $route);
        $response->assertStatus(401);
    }

    public function test_destroy_returns_a_403_when_having_wrong_role(): void
    {
        Sanctum::actingAs(User::factory()->withRole(Role::USER)->create());

        $route = route('user.destroy', [
            'user' => User::factory()->create(),
        ]);

        $response = $this->json('PATCH', $route);
        $response->assertStatus(403);
    }

    public function test_destroy_deletes_model(): void
    {
        $user = User::factory()->create();

        $route = route('user.destroy', [
            'user' => $user,
        ]);

        Sanctum::actingAs(User::factory()->withRole(Role::ADMIN)->create());

        $response = $this->json('DELETE', $route);
        $response->assertStatus(204);

        $this->assertModelMissing($user);
    }
}
