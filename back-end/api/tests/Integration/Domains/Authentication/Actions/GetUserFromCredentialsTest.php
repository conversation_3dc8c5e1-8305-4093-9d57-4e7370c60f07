<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Authentication\Actions;

use App\Domains\Authentication\Actions\GetUserFromCredentials;
use App\Domains\Authentication\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetUserFromCredentialsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_gets_user_for_correct_credentials(): void
    {
        $user = User::factory()->create();

        $returnValue = app(GetUserFromCredentials::class)->execute(
            email: $user->email,
            password: 'password'
        );

        $this->assertNotNull($returnValue);
        $this->assertSame($returnValue->id, $user->id);
    }

    public function test_it_gets_null_for_incorrect_existing_email(): void
    {
        User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $returnValue = app(GetUserFromCredentials::class)->execute('<EMAIL>', 'password');

        $this->assertNull($returnValue);
    }

    public function test_it_gets_null_for_incorrect_existing_password(): void
    {
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '',
        ]);

        $returnValue = app(GetUserFromCredentials::class)->execute('<EMAIL>', 'password');

        $this->assertNull($returnValue);
    }
}
