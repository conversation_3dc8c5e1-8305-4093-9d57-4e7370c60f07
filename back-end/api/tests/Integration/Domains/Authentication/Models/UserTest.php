<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Authentication\Models;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_casts_dashboards(): void
    {
        $user = User::factory()->create([
            'dashboards' => [1, 2, 3],
        ]);

        self::assertIsArray($user->dashboards);
        self::assertCount(3, $user->dashboards);
    }

    public function test_it_checks_role(): void
    {
        $user = User::factory()->create([
            'role' => Role::USER,
        ]);

        self::assertInstanceOf(Role::class, $user->role);
        self::assertEquals(Role::USER, $user->role);
        self::assertTrue($user->hasRole(Role::USER));
    }
}
