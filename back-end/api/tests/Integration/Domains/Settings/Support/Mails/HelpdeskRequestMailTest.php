<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Support\Mails;

use App\Domains\Authentication\Models\User;
use App\Domains\Settings\Support\Mails\HelpdeskRequestMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class HelpdeskRequestMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $user = User::factory()->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new HelpdeskRequestMail(
            user: $user,
            messageSubject: 'test subject',
            messageContent: 'test content',
        );

        $mail->assertHasTo('<EMAIL>');
        $mail->assertHasReplyTo($user->email);

        $mail->assertHasSubject(sprintf('Helpdesk request: %s', 'test subject'));

        $mail->assertSeeInHtml($user->name);
        $mail->assertSeeInHtml($user->email);
        $mail->assertSeeInHtml('test subject');
        $mail->assertSeeInHtml('test content');
    }
}
