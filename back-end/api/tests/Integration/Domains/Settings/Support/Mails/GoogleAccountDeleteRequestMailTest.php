<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Support\Mails;

use App\Domains\Settings\Support\Mails\GoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class GoogleAccountDeleteRequestMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new GoogleAccountDeleteRequestMail(
            googleAccountId: $googleAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Google Account #%s deletion request', $googleAccount->id));

        $mail->assertSeeInHtml($googleAccount->id);
        $mail->assertSeeInHtml($googleAccount->name);
    }
}
