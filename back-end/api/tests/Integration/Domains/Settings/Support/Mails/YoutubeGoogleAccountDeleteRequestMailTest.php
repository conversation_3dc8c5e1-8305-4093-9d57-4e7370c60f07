<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Support\Mails;

use App\Domains\Settings\Support\Mails\YoutubeGoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class YoutubeGoogleAccountDeleteRequestMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $youtubeGoogleAccount = GoogleAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new YoutubeGoogleAccountDeleteRequestMail(
            youtubeGoogleAccountId: $youtubeGoogleAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Youtube Google Account #%s deletion request', $youtubeGoogleAccount->id));

        $mail->assertSeeInHtml($youtubeGoogleAccount->id);
        $mail->assertSeeInHtml($youtubeGoogleAccount->name);
    }
}
