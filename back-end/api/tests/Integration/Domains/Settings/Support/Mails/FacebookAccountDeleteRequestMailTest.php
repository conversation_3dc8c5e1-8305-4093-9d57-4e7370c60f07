<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Support\Mails;

use App\Domains\Settings\Support\Mails\FacebookAccountDeleteRequestMail;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FacebookAccountDeleteRequestMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $facebookAccount = FacebookAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new FacebookAccountDeleteRequestMail(
            facebookAccountId: $facebookAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Facebook Account #%s deletion request', $facebookAccount->id));

        $mail->assertSeeInHtml($facebookAccount->id);
        $mail->assertSeeInHtml($facebookAccount->name);
    }
}
