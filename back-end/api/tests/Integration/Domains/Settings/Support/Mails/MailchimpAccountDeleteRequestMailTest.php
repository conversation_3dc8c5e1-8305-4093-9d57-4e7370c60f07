<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Support\Mails;

use App\Domains\Settings\Support\Mails\MailchimpAccountDeleteRequestMail;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class MailchimpAccountDeleteRequestMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $mailchimpAccount = MailchimpAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new MailchimpAccountDeleteRequestMail(
            mailchimpAccountId: $mailchimpAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Mailchimp Account #%s deletion request', $mailchimpAccount->id));

        $mail->assertSeeInHtml($mailchimpAccount->id);
    }
}
