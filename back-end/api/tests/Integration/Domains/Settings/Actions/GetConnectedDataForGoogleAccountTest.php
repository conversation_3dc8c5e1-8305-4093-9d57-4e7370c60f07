<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Actions;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Settings\Actions\GetConnectedDataForGoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetConnectedDataForGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        GoogleAccount::preventLazyLoading();
        GoogleAnalyticsAccount::preventLazyLoading();
        GoogleAnalyticsProperty::preventLazyLoading();
    }

    protected function tearDown(): void
    {
        GoogleAccount::preventLazyLoading(false);
        GoogleAnalyticsAccount::preventLazyLoading(false);
        GoogleAnalyticsProperty::preventLazyLoading(false);

        parent::tearDown();
    }

    public function test_it_lists_google_ad_accounts(): void
    {
        /** @var GoogleAccount $googleAccount */
        $googleAccount = GoogleAccount::factory()->create();

        /** @var GoogleAdAccount $connectedGoogleAdAccount */
        $connectedGoogleAdAccount = GoogleAdAccount::factory()
            ->for($googleAccount)
            ->has(DataSource::factory())
            ->create();

        GoogleAdAccount::factory()
            ->for($googleAccount)
            ->create();

        $results = app(GetConnectedDataForGoogleAccount::class)->execute($googleAccount);

        self::assertIsArray($results['google_ad_accounts']);

        $accounts = $results['google_ad_accounts'];

        self::assertCount(1, $accounts);

        $account = $results['google_ad_accounts'][0];
        self::assertSame($connectedGoogleAdAccount->name, $account['name']);
        self::assertSame('Google Account', $account['connection_type']);
        self::assertSame($googleAccount->name, $account['connection_name']);
        self::assertSame($connectedGoogleAdAccount->last_synced_at->toDateTimeString(), $account['last_synced_at']);
    }

    public function test_it_lists_google_analytics_properties(): void
    {
        /** @var GoogleAccount $googleAccount */
        $googleAccount = GoogleAccount::factory()->create();

        /** @var GoogleAnalyticsProperty $connectedGoogleAnalyticsProperty */
        $connectedGoogleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->for(GoogleAnalyticsAccount::factory()->for($googleAccount))
            ->create();

        GoogleAnalyticsProperty::factory()
            ->for(GoogleAnalyticsAccount::factory()->for($googleAccount))
            ->create();

        $results = app(GetConnectedDataForGoogleAccount::class)->execute($googleAccount);

        self::assertIsArray($results['google_analytics_properties']);

        $accounts = $results['google_analytics_properties'];

        self::assertCount(1, $accounts);

        $account = $results['google_analytics_properties'][0];
        self::assertSame($connectedGoogleAnalyticsProperty->name, $account['name']);
        self::assertSame('Google Analytics Account', $account['connection_type']);
        self::assertSame($connectedGoogleAnalyticsProperty->googleAnalyticsAccount->name, $account['connection_name']);
        self::assertSame($connectedGoogleAnalyticsProperty->last_synced_at->toDateTimeString(), $account['last_synced_at']);
    }
}
