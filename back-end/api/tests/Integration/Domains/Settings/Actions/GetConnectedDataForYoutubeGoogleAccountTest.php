<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Actions;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Settings\Actions\GetConnectedDataForYoutubeGoogleAccount;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetConnectedDataForYoutubeGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        YoutubeGoogleAccount::preventLazyLoading();
    }

    protected function tearDown(): void
    {
        YoutubeGoogleAccount::preventLazyLoading(false);

        parent::tearDown();
    }

    public function test_it_lists_youtube_channels(): void
    {
        /** @var YoutubeGoogleAccount $youtubeGoogleAccount */
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()->create();

        /** @var YoutubeChannel $connectedYoutubeChannel */
        $connectedYoutubeChannel = YoutubeChannel::factory()
            ->for($youtubeGoogleAccount)
            ->has(DataSource::factory())
            ->create();

        YoutubeChannel::factory()
            ->has(DataSource::factory())
            ->create();

        $results = app(GetConnectedDataForYoutubeGoogleAccount::class)->execute($youtubeGoogleAccount);

        self::assertIsArray($results['youtube_channels']);

        $accounts = $results['youtube_channels'];

        self::assertCount(1, $accounts);

        $account = $results['youtube_channels'][0];
        self::assertSame($connectedYoutubeChannel->name, $account['name']);
        self::assertSame('Google Account', $account['connection_type']);
        self::assertSame($youtubeGoogleAccount->name, $account['connection_name']);
        self::assertSame($connectedYoutubeChannel->last_synced_at->toDateTimeString(), $account['last_synced_at']);
    }
}
