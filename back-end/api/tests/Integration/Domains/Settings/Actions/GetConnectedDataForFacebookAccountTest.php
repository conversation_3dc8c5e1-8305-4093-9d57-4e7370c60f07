<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Settings\Actions;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Settings\Actions\GetConnectedDataForFacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetConnectedDataForFacebookAccountTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        FacebookAccount::preventLazyLoading();
        FacebookBusiness::preventLazyLoading();
    }

    protected function tearDown(): void
    {
        FacebookAccount::preventLazyLoading(false);
        FacebookBusiness::preventLazyLoading(false);

        parent::tearDown();
    }

    public function test_it_lists_facebook_ad_accounts()
    {
        /** @var FacebookAccount $facebookAccount */
        $facebookAccount = FacebookAccount::factory()->create();

        $facebookBusiness = FacebookBusiness::factory()
            ->for($facebookAccount)
            ->create();

        /** @var FacebookAdAccount $connectedFacebookAdAccountWithBusiness */
        $connectedFacebookAdAccountWithBusiness = FacebookAdAccount::factory()
            ->for($facebookBusiness)
            ->has(DataSource::factory())
            ->create([
                'facebook_account_id' => null,
            ]);

        /** @var FacebookAdAccount $connectedFacebookAdAccountWithAccount */
        $connectedFacebookAdAccountWithAccount = FacebookAdAccount::factory()
            ->for($facebookAccount)
            ->has(DataSource::factory())
            ->create([
                'facebook_business_id' => null,
            ]);

        FacebookAdAccount::factory()
            ->for($facebookAccount)
            ->create();

        $results = app(GetConnectedDataForFacebookAccount::class)->execute($facebookAccount);

        self::assertIsArray($results['facebook_ad_accounts']);

        $accounts = $results['facebook_ad_accounts'];

        self::assertCount(2, $accounts);

        $businessAccount = $results['facebook_ad_accounts'][0];
        self::assertSame($connectedFacebookAdAccountWithBusiness->name, $businessAccount['name']);
        self::assertSame('Facebook Business', $businessAccount['connection_type']);
        self::assertSame($connectedFacebookAdAccountWithBusiness->facebookBusiness->name, $businessAccount['connection_name']);
        self::assertSame($connectedFacebookAdAccountWithBusiness->last_synced_at->toDateTimeString(), $businessAccount['last_synced_at']);

        $personalAccount = $results['facebook_ad_accounts'][1];
        self::assertSame($connectedFacebookAdAccountWithAccount->name, $personalAccount['name']);
        self::assertSame('Facebook Account', $personalAccount['connection_type']);
        self::assertSame($connectedFacebookAdAccountWithAccount->facebookAccount->name, $personalAccount['connection_name']);
        self::assertSame($connectedFacebookAdAccountWithAccount->last_synced_at->toDateTimeString(), $personalAccount['last_synced_at']);
    }

    public function test_it_lists_facebook_pages()
    {
        /** @var FacebookAccount $facebookAccount */
        $facebookAccount = FacebookAccount::factory()->create();

        $facebookBusiness = FacebookBusiness::factory()
            ->for($facebookAccount)
            ->create();

        /** @var FacebookPage $connectedFacebookPageWithBusiness */
        $connectedFacebookPageWithBusiness = FacebookPage::factory()
            ->for($facebookBusiness)
            ->has(DataSource::factory())
            ->create([
                'facebook_account_id' => null,
            ]);

        /** @var FacebookPage $connectedFacebookPageWithAccount */
        $connectedFacebookPageWithAccount = FacebookPage::factory()
            ->for($facebookAccount)
            ->has(DataSource::factory())
            ->create([
                'facebook_business_id' => null,
            ]);

        FacebookPage::factory()
            ->for($facebookAccount)
            ->create();

        $results = app(GetConnectedDataForFacebookAccount::class)->execute($facebookAccount);

        self::assertIsArray($results['facebook_pages']);

        $accounts = $results['facebook_pages'];

        self::assertCount(2, $accounts);

        $businessAccount = $results['facebook_pages'][0];
        self::assertSame($connectedFacebookPageWithBusiness->name, $businessAccount['name']);
        self::assertSame('Facebook Business', $businessAccount['connection_type']);
        self::assertSame($connectedFacebookPageWithBusiness->facebookBusiness->name, $businessAccount['connection_name']);
        self::assertSame($connectedFacebookPageWithBusiness->last_synced_at->toDateTimeString(), $businessAccount['last_synced_at']);

        $personalAccount = $results['facebook_pages'][1];
        self::assertSame($connectedFacebookPageWithAccount->name, $personalAccount['name']);
        self::assertSame('Facebook Account', $personalAccount['connection_type']);
        self::assertSame($connectedFacebookPageWithAccount->facebookAccount->name, $personalAccount['connection_name']);
        self::assertSame($connectedFacebookPageWithAccount->last_synced_at->toDateTimeString(), $personalAccount['last_synced_at']);
    }

    public function test_it_lists_instagram_accounts()
    {
        /** @var FacebookAccount $facebookAccount */
        $facebookAccount = FacebookAccount::factory()->create();

        $facebookBusiness = FacebookBusiness::factory()
            ->for($facebookAccount)
            ->create();

        /** @var FacebookPage $connectedFacebookPageWithBusiness */
        $connectedFacebookPageWithBusiness = FacebookPage::factory()
            ->for($facebookBusiness)
            ->has(InstagramAccount::factory()->has(DataSource::factory()))
            ->create([
                'facebook_account_id' => null,
            ]);

        /** @var FacebookPage $connectedFacebookPageWithAccount */
        $connectedFacebookPageWithAccount = FacebookPage::factory()
            ->for($facebookAccount)
            ->has(InstagramAccount::factory()->has(DataSource::factory()))
            ->create([
                'facebook_business_id' => null,
            ]);

        FacebookPage::factory()
            ->for($facebookAccount)
            ->has(InstagramAccount::factory())
            ->create([
                'facebook_business_id' => null,
            ]);

        $results = app(GetConnectedDataForFacebookAccount::class)->execute($facebookAccount);

        self::assertIsArray($results['instagram_accounts']);

        $accounts = $results['instagram_accounts'];

        self::assertCount(2, $accounts);

        $businessAccount = $results['instagram_accounts'][0];
        self::assertSame($connectedFacebookPageWithBusiness->instagramAccount->username, $businessAccount['username']);
        self::assertSame('Facebook Page', $businessAccount['connection_type']);
        self::assertSame($connectedFacebookPageWithBusiness->instagramAccount->facebookPage->name, $businessAccount['connection_name']);
        self::assertSame($connectedFacebookPageWithBusiness->instagramAccount->facebookPage->last_synced_at->toDateTimeString(), $businessAccount['last_synced_at']);

        $personalAccount = $results['instagram_accounts'][1];
        self::assertSame($connectedFacebookPageWithAccount->instagramAccount->username, $personalAccount['username']);
        self::assertSame('Facebook Page', $personalAccount['connection_type']);
        self::assertSame($connectedFacebookPageWithAccount->instagramAccount->facebookPage->name, $personalAccount['connection_name']);
        self::assertSame($connectedFacebookPageWithAccount->instagramAccount->facebookPage->last_synced_at->toDateTimeString(), $personalAccount['last_synced_at']);
    }
}
