<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Campaigns\GetCampaigns;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpCampaigns;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpReport;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncMailchimpCampaignsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_redispatches_self_for_more_page(): void
    {
        Queue::fake();

        $this->mock(GetCampaigns::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'current_page' => 1,
                'total_pages' => 2,
                'items' => [],
            ]);

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $job = new SyncMailchimpCampaigns($mailchimpAccount->id);
        $job->handle();

        Queue::assertPushed(SyncMailchimpCampaigns::class, 1);
    }

    public function test_it_stores_new_campaigns(): void
    {
        Queue::fake();

        $this->mock(GetCampaigns::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'current_page' => 1,
                'total_pages' => 2,
                'items' => [
                    [
                        'id' => 'test_1',
                        'status' => 'sending',
                        'send_time' => now()->toIso8601String(),
                        'title' => 'Title',
                        'recipients' => [
                            'list_id' => 1,
                            'list_name' => 'test',
                            'list_is_active' => true,
                        ],
                    ],
                ],
            ]);

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $job = new SyncMailchimpCampaigns($mailchimpAccount->id);
        $job->handle();

        $this->assertDatabaseHas('mailchimp_campaigns', [
            'external_id' => 'test_1',
            'status' => 'sending',
        ]);

        Queue::assertPushed(SyncMailchimpReport::class);
    }

    public function test_it_updates_new_campaigns(): void
    {
        Queue::fake();

        $this->mock(GetCampaigns::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'current_page' => 1,
                'total_pages' => 1,
                'items' => [
                    [
                        'id' => 'updated_1',
                        'status' => 'sending',
                        'send_time' => now()->toIso8601String(),
                        'title' => 'Title',
                        'recipients' => [
                            'list_id' => 1,
                            'list_name' => 'test',
                            'list_is_active' => true,
                        ],
                    ],
                ],
            ]);

        $mailchimpAccount = MailchimpAccount::factory()->create();

        MailchimpCampaign::factory()->for($mailchimpAccount)->create([
            'external_id' => 'updated_1',
            'status' => 'save',
        ]);

        $this->assertDatabaseCount('mailchimp_campaigns', 1);

        $job = new SyncMailchimpCampaigns($mailchimpAccount->id);
        $job->handle();

        $this->assertDatabaseCount('mailchimp_campaigns', 1);

        $this->assertDatabaseHas('mailchimp_campaigns', [
            'external_id' => 'updated_1',
            'status' => 'sending',
        ]);

        Queue::assertPushed(SyncMailchimpReport::class);
    }
}
