<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccount;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccounts;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncMailchimpAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $mailchimpAccount = MailchimpAccount::factory()
            ->create();

        $deletedMailchimpAccount = MailchimpAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncMailchimpAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncMailchimpAccount::class,
            fn (SyncMailchimpAccount $job) => $job->mailchimpAccountId === $mailchimpAccount->id
        );
        Queue::assertNotPushed(
            SyncMailchimpAccount::class,
            fn (SyncMailchimpAccount $job) => $job->mailchimpAccountId === $deletedMailchimpAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncMailchimpAccounts::class, $scheduledEvents);
    }
}
