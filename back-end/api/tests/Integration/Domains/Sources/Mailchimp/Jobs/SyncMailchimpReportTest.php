<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Reports\GetReport;
use App\Domains\Sources\Mailchimp\Actions\Reports\GetReportTotal;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpReport;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SyncMailchimpReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_stores_new_report(): void
    {
        $this->mock(GetReport::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'emails_sent' => 1,
                'clicks_total' => 2,
                'unique_clicks' => 3,
                'click_rate' => 0.851,
                'opens_total' => 4,
                'unique_opens' => 5,
                'open_rate' => 0.955,
            ]);

        $this->mock(GetReportTotal::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(null);

        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        $job = new SyncMailchimpReport($mailchimpCampaign->id);
        $job->handle();

        $this->assertDatabaseHas('mailchimp_reports', [
            'mailchimp_campaign_id' => $mailchimpCampaign->id,
            'date' => now()->format('Y-m-d'),
            'emails_sent' => 1,
            'clicks_total' => 2,
            'unique_clicks' => 3,
            'click_rate' => '85.10',
            'opens_total' => 4,
            'unique_opens' => 5,
            'open_rate' => '95.50',
        ]);
    }

    public function test_it_stores_with_corrected_totals(): void
    {
        $this->mock(GetReport::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'emails_sent' => 3,
                'clicks_total' => 6,
                'unique_clicks' => 4,
                'click_rate' => 0.851,
                'opens_total' => 7,
                'unique_opens' => 9,
                'open_rate' => 0.955,
            ]);

        $this->mock(GetReportTotal::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([
                'emails_sent' => 1,
                'clicks_total' => 2,
                'unique_clicks' => 3,
                'opens_total' => 4,
                'unique_opens' => 5,
            ]);

        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        $job = new SyncMailchimpReport($mailchimpCampaign->id);
        $job->handle();

        $this->assertDatabaseHas('mailchimp_reports', [
            'mailchimp_campaign_id' => $mailchimpCampaign->id,
            'date' => now()->format('Y-m-d'),
            'emails_sent' => 2,
            'clicks_total' => 4,
            'unique_clicks' => 1,
            'click_rate' => '85.10',
            'opens_total' => 3,
            'unique_opens' => 4,
            'open_rate' => '95.50',
        ]);
    }
}
