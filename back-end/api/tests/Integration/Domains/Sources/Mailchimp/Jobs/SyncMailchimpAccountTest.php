<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Ping;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccount;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpCampaigns;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpAccountException;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncMailchimpAccountTest extends TestCase
{
    /**
     * @throws CannotSyncMailchimpAccountException
     */
    public function test_it_syncs_account(): void
    {
        Queue::fake();

        $this->mock(Ping::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $mailchimpAccount = MailchimpAccount::factory()
            ->create([
                'last_pinged_at' => null,
            ]);

        $job = new SyncMailchimpAccount($mailchimpAccount->id);
        $job->handle();

        $mailchimpAccount->refresh();

        self::assertNotNull($mailchimpAccount->last_pinged_at);

        Queue::assertPushed(fn (SyncMailchimpCampaigns $job) => $job->mailchimpAccountId === $mailchimpAccount->id && $job->page === 1);
    }

    /**
     * @throws CannotSyncMailchimpAccountException
     */
    public function test_it_throws_exception_if_ping_fails(): void
    {
        $this->mock(Ping::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(false);

        $mailchimpAccount = MailchimpAccount::factory()
            ->create();

        $this->expectExceptionMessage(
            CannotSyncMailchimpAccountException::becauseOfFailingPing($mailchimpAccount)->getMessage()
        );

        $job = new SyncMailchimpAccount($mailchimpAccount->id);
        $job->handle();
    }
}
