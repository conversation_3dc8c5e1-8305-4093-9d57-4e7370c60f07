<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Mailchimp\Jobs\VerifyUnconnectedMailchimpAudiences;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Support\Mails\MailchimpAudienceNotConnectedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnconnectedMailchimpAudiencesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_sends_mail_with_unconnected_audiences(): void
    {
        Mail::fake();

        // Create two unconnected audiences
        $unconnectedAudiences = MailchimpAudience::factory()->count(2)->create();

        // Create one connected audience
        $connectedAudience = MailchimpAudience::factory()->create();
        DataSource::factory()->create([
            'sourceable_id' => $connectedAudience->id,
            'sourceable_type' => get_class($connectedAudience),
        ]);

        // Run the job
        $job = new VerifyUnconnectedMailchimpAudiences;
        $job->handle();

        // Assert mail was sent with unconnected audience IDs
        Mail::assertQueued(function (MailchimpAudienceNotConnectedMail $mail) use ($unconnectedAudiences) {
            return count($mail->audienceIds) === 2 &&
                   in_array($unconnectedAudiences[0]->id, $mail->audienceIds) &&
                   in_array($unconnectedAudiences[1]->id, $mail->audienceIds);
        });
    }

    public function test_it_does_not_send_mail_when_all_audiences_are_connected(): void
    {
        Mail::fake();

        // Create a connected audience
        $connectedAudience = MailchimpAudience::factory()->create();
        DataSource::factory()->create([
            'sourceable_id' => $connectedAudience->id,
            'sourceable_type' => get_class($connectedAudience),
        ]);

        // Run the job
        $job = new VerifyUnconnectedMailchimpAudiences;
        $job->handle();

        // Assert no mail was sent
        Mail::assertNotQueued(MailchimpAudienceNotConnectedMail::class);
    }
}
