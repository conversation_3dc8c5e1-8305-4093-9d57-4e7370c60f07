<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Support\Mails;

use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Support\Mails\MailchimpAudienceNotConnectedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class MailchimpAudienceNotConnectedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties_for_multiple_audiences(): void
    {
        $audiences = MailchimpAudience::factory()
            ->count(2)
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $audienceIds = $audiences->pluck('id')->toArray();
        $mail = new MailchimpAudienceNotConnectedMail($audienceIds);

        $mail->assertHasTo('<EMAIL>');
        $mail->assertHasSubject('2 Mailchimp Audiences are not connected to a data source');

        foreach ($audiences as $audience) {
            $mail->assertSeeInHtml($audience->id);
            $mail->assertSeeInHtml($audience->name);
        }
    }

    public function test_it_has_properties_for_single_audience(): void
    {
        $audience = MailchimpAudience::factory()->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new MailchimpAudienceNotConnectedMail([$audience->id]);

        $mail->assertHasTo('<EMAIL>');
        $mail->assertHasSubject('1 Mailchimp Audience is not connected to a data source');

        $mail->assertSeeInHtml($audience->id);
        $mail->assertSeeInHtml($audience->name);
    }
}
