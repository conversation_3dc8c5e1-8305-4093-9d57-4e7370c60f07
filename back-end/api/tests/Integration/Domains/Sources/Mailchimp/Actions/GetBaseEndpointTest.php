<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Actions;

use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetBaseEndpointTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_formats_endpoint_for_model()
    {
        $mailchimpAccount = MailchimpAccount::factory()->create([
            'region' => 'test_region',
        ]);

        $endpoint = app(GetBaseEndpoint::class)->execute($mailchimpAccount);

        self::assertEquals('https://test_region.api.mailchimp.com/3.0/%s', $endpoint);
    }
}
