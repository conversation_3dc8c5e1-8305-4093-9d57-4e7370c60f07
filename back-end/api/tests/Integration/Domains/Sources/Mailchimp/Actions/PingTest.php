<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Actions;

use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Actions\Ping;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class PingTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_returns_true_for_accepting_endpoint()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/ping' => Http::response([], 200),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $returnValue = app(Ping::class)->execute($mailchimpAccount);
        self::assertTrue($returnValue);

        Http::assertSent(function (Request $request) {
            $authorizationHeader = $request->header('Authorization')[0] ?? null;

            return $authorizationHeader !== null && Str::contains($authorizationHeader, 'Basic');
        });
    }

    public function test_it_returns_false_for_forbidden_status()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/ping' => Http::response([], 403),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $returnValue = app(Ping::class)->execute($mailchimpAccount);
        self::assertFalse($returnValue);
    }
}
