<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Actions\Reports;

use App\Domains\Sources\Mailchimp\Actions\Reports\GetReportTotal;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetReportTotalTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_totals_but_ignores_today()
    {
        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        MailchimpReport::factory()
            ->for($mailchimpCampaign)
            ->create([
                'date' => now()->subDay()->format('Y-m-d'),
                'mailchimp_campaign_id' => $mailchimpCampaign->id,
                'emails_sent' => 10,
                'clicks_total' => 3,
                'unique_clicks' => 4,
                'opens_total' => 1,
                'unique_opens' => 2,
            ]);

        MailchimpReport::factory()
            ->for($mailchimpCampaign)
            ->create([
                'date' => now()->subDay()->format('Y-m-d'),
                'mailchimp_campaign_id' => $mailchimpCampaign->id,
                'emails_sent' => 3,
                'clicks_total' => 6,
                'unique_clicks' => 1,
                'opens_total' => 7,
                'unique_opens' => 9,
            ]);

        MailchimpReport::factory()
            ->for($mailchimpCampaign)
            ->create([
                'date' => now()->format('Y-m-d'),
                'mailchimp_campaign_id' => $mailchimpCampaign->id,
                'emails_sent' => 3,
                'clicks_total' => 6,
                'unique_clicks' => 1,
                'opens_total' => 7,
                'unique_opens' => 9,
            ]);

        $result = app(GetReportTotal::class)->execute($mailchimpCampaign);

        self::assertEquals([
            'emails_sent' => 13,
            'clicks_total' => 9,
            'unique_clicks' => 5,
            'opens_total' => 8,
            'unique_opens' => 11,
        ], $result);
    }

    public function test_it_returns_null_for_no_reports()
    {
        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        $result = app(GetReportTotal::class)->execute($mailchimpCampaign);

        self::assertNull($result);
    }
}
