<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Actions\Campaigns;

use App\Domains\Sources\Mailchimp\Actions\Campaigns\GetCampaigns;
use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpCampaignException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetCampaignsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_returns_campaigns()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/campaigns*' => Http::response([
                'campaigns' => [
                    ['id' => 'test_1', 'status' => 'save', 'send_time' => now()->toIso8601String(), 'settings' => ['title' => 'Title'], 'recipients' => ['list_id' => 1, 'list_name' => 'test', 'list_is_active' => true]],
                    ['id' => 'test_2', 'status' => 'save', 'send_time' => now()->toIso8601String(), 'settings' => ['title' => 'Title'], 'recipients' => ['list_id' => 1, 'list_name' => 'test', 'list_is_active' => true]],
                    ['id' => 'test_3', 'status' => 'save', 'send_time' => now()->toIso8601String(), 'settings' => ['title' => 'Title'], 'recipients' => ['list_id' => 1, 'list_name' => 'test', 'list_is_active' => true]],
                ],
                'total_items' => 3,
            ], 200),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount);

        Http::assertSent(function (Request $request) {
            $authorizationHeader = $request->header('Authorization')[0] ?? null;

            return $authorizationHeader !== null && Str::contains($authorizationHeader, 'Basic');
        });

        self::assertArrayHasKey('current_page', $returnValue);
        self::assertEquals(1, $returnValue['current_page']);

        self::assertArrayHasKey('total_pages', $returnValue);
        self::assertEquals(1, $returnValue['total_pages']);

        self::assertArrayHasKey('items', $returnValue);
        self::assertCount(3, $returnValue['items']);

        self::assertEquals('test_1', $returnValue['items'][0]['id']);
        self::assertEquals('test_2', $returnValue['items'][1]['id']);
        self::assertEquals('test_3', $returnValue['items'][2]['id']);
    }

    public function test_it_returns_pagination()
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::sequence()
                ->push([
                    'campaigns' => [],
                    'total_items' => 0,
                ], 200) // Sequence: 0 results
                ->push([
                    'campaigns' => [],
                    'total_items' => 3,
                ], 200) // Sequence: below page limit
                ->push([
                    'campaigns' => [],
                    'total_items' => 1000,
                ], 200) // Sequence: equal to page limit
                ->push([
                    'campaigns' => [],
                    'total_items' => 1001,
                ], 200), // Sequence: above page limit
        ]);

        $mailchimpAccount = MailchimpAccount::factory()->create();

        // Sequence: 0 results
        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount);

        self::assertArrayHasKey('total_pages', $returnValue);
        self::assertEquals(1, $returnValue['total_pages']);

        // Sequence: below page limit
        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount);

        self::assertArrayHasKey('total_pages', $returnValue);
        self::assertEquals(1, $returnValue['total_pages']);

        // Sequence: equal to page limit
        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount);

        self::assertArrayHasKey('total_pages', $returnValue);
        self::assertEquals(1, $returnValue['total_pages']);

        // Sequence: above page limit
        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount);

        self::assertArrayHasKey('total_pages', $returnValue);
        self::assertEquals(2, $returnValue['total_pages']);
    }

    public function test_it_calls_with_offset()
    {
        Http::preventStrayRequests();

        Http::fake([
            '*/campaigns?offset=1000&count=1000*' => Http::response([
                'campaigns' => [],
                'total_items' => 28,
            ], 200),
        ]);

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $returnValue = app(GetCampaigns::class)->execute($mailchimpAccount, 2);

        self::assertArrayHasKey('current_page', $returnValue);
        self::assertEquals(2, $returnValue['current_page']);
    }

    public function test_it_throws_exception()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/campaigns*' => Http::response([], 500),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpAccount = MailchimpAccount::factory()->create();

        $this->expectExceptionMessage(
            CannotSyncMailchimpCampaignException::becauseOfHttpErrorWithStatusCode(
                $mailchimpAccount,
                500,
                ''
            )->getMessage()
        );

        app(GetCampaigns::class)->execute($mailchimpAccount);
    }
}
