<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Actions\Reports;

use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Actions\Reports\GetReport;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpReportException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_returns_report()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/reports*' => Http::response([
                'emails_sent' => 10,
                'opens' => [
                    'opens_total' => 1,
                    'unique_opens' => 2,
                    'open_rate' => 0.5,
                ],
                'clicks' => [
                    'clicks_total' => 3,
                    'unique_clicks' => 4,
                    'click_rate' => 0.6,
                ],
            ], 200),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        $returnValue = app(GetReport::class)->execute($mailchimpCampaign);

        Http::assertSent(function (Request $request) {
            $authorizationHeader = $request->header('Authorization')[0] ?? null;

            return $authorizationHeader !== null && Str::contains($authorizationHeader, 'Basic');
        });

        self::assertArrayHasKey('emails_sent', $returnValue);
        self::assertEquals(10, $returnValue['emails_sent']);

        self::assertArrayHasKey('clicks_total', $returnValue);
        self::assertEquals(3, $returnValue['clicks_total']);

        self::assertArrayHasKey('unique_clicks', $returnValue);
        self::assertEquals(4, $returnValue['unique_clicks']);

        self::assertArrayHasKey('click_rate', $returnValue);
        self::assertEquals(0.6, $returnValue['click_rate']);

        self::assertArrayHasKey('opens_total', $returnValue);
        self::assertEquals(1, $returnValue['opens_total']);

        self::assertArrayHasKey('unique_opens', $returnValue);
        self::assertEquals(2, $returnValue['unique_opens']);

        self::assertArrayHasKey('open_rate', $returnValue);
        self::assertEquals(0.5, $returnValue['open_rate']);
    }

    public function test_it_throws_exception()
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://test.example.com/3.0/reports*' => Http::response([], 500),
        ]);

        $this->mock(GetBaseEndpoint::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('https://test.example.com/3.0/%s');

        $mailchimpCampaign = MailchimpCampaign::factory()->create();

        $this->expectExceptionMessage(
            CannotSyncMailchimpReportException::becauseOfHttpErrorWithStatusCode(
                $mailchimpCampaign,
                500,
                ''
            )->getMessage()
        );

        app(GetReport::class)->execute($mailchimpCampaign);
    }
}
