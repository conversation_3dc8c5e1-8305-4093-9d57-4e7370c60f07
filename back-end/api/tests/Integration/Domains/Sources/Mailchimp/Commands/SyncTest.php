<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Commands;

use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccounts;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;

class SyncTest extends TestCase
{
    public function test_handle_dispatches_jobs(): void
    {
        Bus::fake();

        $this->artisan('sources:mailchimp:sync')
            ->expectsOutput('Dispatching jobs to sync all Mailchimp connections')
            ->expectsOutput('Jobs dispatched successfully')
            ->assertOk();

        Bus::assertChained([
            SyncMailchimpAccounts::class,
        ]);
    }
}
