<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MailchimpAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_encrypts_api_key(): void
    {
        $mailchimpAccount = MailchimpAccount::factory()->create();

        $mailchimpAccount->api_key = 'test';
        $mailchimpAccount->save();

        self::assertInstanceOf(MailchimpAccount::class, $mailchimpAccount);
        self::assertEquals('test', $mailchimpAccount->api_key);

        self:$this->assertDatabaseMissing('mailchimp_accounts', [
            'api_key' => 'test',
        ]);
    }

    public function test_it_has_many_mailchimp_accounts(): void
    {
        $mailchimpAccount = MailchimpAccount::factory()
            ->has(MailchimpCampaign::factory()->count(3))
            ->create();

        $this->assertCount(3, $mailchimpAccount->mailchimpCampaigns);
        $this->assertInstanceOf(MailchimpCampaign::class, $mailchimpAccount->mailchimpCampaigns->first());
    }
}
