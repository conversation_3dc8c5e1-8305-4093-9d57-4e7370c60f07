<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use App\Domains\Sources\Mailchimp\Support\Enums\Campaign\Status;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MailchimpCampaignTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_mailchimp_account(): void
    {
        $mailchimpCampaign = MailchimpCampaign::factory()
            ->for(MailchimpAccount::factory())
            ->create();

        $this->assertInstanceOf(MailchimpAccount::class, $mailchimpCampaign->mailchimpAccount);
    }

    public function test_it_has_many_mailchimp_reports(): void
    {
        $mailchimpCampaign = MailchimpCampaign::factory()
            ->has(MailchimpReport::factory()->count(3))
            ->create();

        $this->assertCount(3, $mailchimpCampaign->mailchimpReports);
        $this->assertInstanceOf(MailchimpReport::class, $mailchimpCampaign->mailchimpReports->first());
    }

    public function test_it_casts_to_status_enum(): void
    {
        $mailchimpCampaign = MailchimpCampaign::factory()
            ->create();

        $this->assertInstanceOf(Status::class, $mailchimpCampaign->status);
    }

    public function test_it_belongs_to_mailchimp_audience(): void
    {
        $mailchimpAudience = MailchimpAudience::factory()->create();

        $mailchimpCampaign = MailchimpCampaign::factory()->create([
            'mailchimp_audience_id' => $mailchimpAudience->id,
        ]);

        $this->assertInstanceOf(MailchimpAudience::class, $mailchimpCampaign->mailchimpAudience);
        $this->assertEquals($mailchimpAudience->id, $mailchimpCampaign->mailchimpAudience->id);
    }
}
