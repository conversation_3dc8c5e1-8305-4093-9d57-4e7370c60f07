<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MailchimpAudienceTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_mailchimp_account(): void
    {
        $mailchimpAccount = MailchimpAccount::factory()->create();

        $mailchimpAudience = MailchimpAudience::factory()->create([
            'mailchimp_account_id' => $mailchimpAccount->id,
        ]);

        $this->assertInstanceOf(MailchimpAccount::class, $mailchimpAudience->mailchimpAccount);
        $this->assertEquals($mailchimpAccount->id, $mailchimpAudience->mailchimpAccount->id);
    }

    public function test_it_has_many_mailchimp_campaigns(): void
    {
        $mailchimpAudience = MailchimpAudience::factory()->create();

        MailchimpCampaign::factory()->count(3)->create([
            'mailchimp_audience_id' => $mailchimpAudience->id,
            'mailchimp_account_id' => $mailchimpAudience->mailchimp_account_id,
        ]);

        $this->assertCount(3, $mailchimpAudience->mailchimpCampaigns);
        $this->assertInstanceOf(MailchimpCampaign::class, $mailchimpAudience->mailchimpCampaigns->first());
    }
}
