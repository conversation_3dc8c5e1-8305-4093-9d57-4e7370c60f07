<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Mailchimp\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MailchimpReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_mailchimp_campaign(): void
    {
        $mailchimpReport = MailchimpReport::factory()
            ->for(MailchimpCampaign::factory())
            ->create();

        $this->assertInstanceOf(MailchimpCampaign::class, $mailchimpReport->mailchimpCampaign);
    }

    public function test_it_has_data_source(): void
    {
        $mailchimpReport = MailchimpReport::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $mailchimpReport->mailchimpCampaign->mailchimp_audience_id,
                'sourceable_type' => MailchimpAudience::class,
            ]);

        $dataSource = $mailchimpReport->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(MailchimpAudience::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $mailchimpReport = MailchimpReport::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $mailchimpReport->mailchimpCampaign->mailchimp_audience_id,
                'sourceable_type' => MailchimpAudience::class,
            ]);

        self::assertContains($mailchimpReport->id, MailchimpReport::query()->pluck('id'));
        self::assertContains($mailchimpReport->id, MailchimpReport::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
