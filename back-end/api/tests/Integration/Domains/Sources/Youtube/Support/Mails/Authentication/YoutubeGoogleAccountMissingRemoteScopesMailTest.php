<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Support\Mails\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Support\Mails\Authentication\YoutubeGoogleAccountMissingRemoteScopesMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class YoutubeGoogleAccountMissingRemoteScopesMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new YoutubeGoogleAccountMissingRemoteScopesMail(
            youtubeGoogleAccountId: $youtubeGoogleAccount->id,
            missingRemoteScopes: ['https://www.googleapis.com/auth/youtube.readonly'],
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Youtube Google Account #%s deactivated because of missing remote scopes', $youtubeGoogleAccount->id));

        $mail->assertSeeInHtml($youtubeGoogleAccount->id);
        $mail->assertSeeInHtml($youtubeGoogleAccount->email);
        $mail->assertSeeInHtml('https://www.googleapis.com/auth/youtube.readonly');
    }
}
