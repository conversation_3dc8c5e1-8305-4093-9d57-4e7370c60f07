<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Jobs\SyncYoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Jobs\SyncYoutubeGoogleAccounts;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncYoutubeGoogleAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        $deletedYoutubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncYoutubeGoogleAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncYoutubeGoogleAccount::class,
            fn (SyncYoutubeGoogleAccount $job) => $job->youtubeGoogleAccountId === $youtubeGoogleAccount->id
        );
        Queue::assertNotPushed(
            SyncYoutubeGoogleAccount::class,
            fn (SyncYoutubeGoogleAccount $job) => $job->youtubeGoogleAccountId === $deletedYoutubeGoogleAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncYoutubeGoogleAccounts::class, $scheduledEvents);
    }
}
