<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\GetScopes;
use App\Domains\Sources\Youtube\Jobs\VerifyScopesForYoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Support\Mails\Authentication\YoutubeGoogleAccountMissingRemoteScopesMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyScopesForYoutubeGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_does_nothing_if_scopes_match(): void
    {
        Mail::fake();

        $scopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/analytics.edit',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn($scopes);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'scopes' => $scopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForYoutubeGoogleAccount($youtubeGoogleAccount->id);
        $job->handle();

        Mail::assertNothingQueued();
    }

    public function test_it_does_nothing_if_remote_has_more_scopes(): void
    {
        Mail::fake();

        $scopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(array_merge($scopes, ['https://www.googleapis.com/auth/analytics.edit']));

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'scopes' => $scopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForYoutubeGoogleAccount($youtubeGoogleAccount->id);
        $job->handle();

        Mail::assertNothingQueued();
    }

    public function test_it_throws_exception_and_deactivates_for_missing_scopes(): void
    {
        Mail::fake();

        Config::set('lkq.support.emails', '<EMAIL>');

        $localScopes = [
            'https://www.googleapis.com/auth/analytics.edit',
        ];
        $remoteScopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn($remoteScopes);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'scopes' => $localScopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForYoutubeGoogleAccount($youtubeGoogleAccount->id);
        $job->handle();

        $youtubeGoogleAccount->refresh();
        $this->assertNotNull($youtubeGoogleAccount->deactivated_at);

        Mail::assertQueued(YoutubeGoogleAccountMissingRemoteScopesMail::class);
    }
}
