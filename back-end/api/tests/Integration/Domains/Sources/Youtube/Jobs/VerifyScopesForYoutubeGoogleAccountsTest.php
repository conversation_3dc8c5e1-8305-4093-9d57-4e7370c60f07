<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Jobs\VerifyScopesForYoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Jobs\VerifyScopesForYoutubeGoogleAccounts;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class VerifyScopesForYoutubeGoogleAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        $deactivatedYoutubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'deactivated_at' => now(),
            ]);

        Queue::fake();

        $job = new VerifyScopesForYoutubeGoogleAccounts;
        $job->handle();

        Queue::assertPushed(
            VerifyScopesForYoutubeGoogleAccount::class,
            fn (VerifyScopesForYoutubeGoogleAccount $job) => $job->youtubeGoogleAccountId === $youtubeGoogleAccount->id
        );
        Queue::assertNotPushed(
            VerifyScopesForYoutubeGoogleAccount::class,
            fn (VerifyScopesForYoutubeGoogleAccount $job) => $job->youtubeGoogleAccountId === $deactivatedYoutubeGoogleAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyScopesForYoutubeGoogleAccounts::class, $scheduledEvents);
    }
}
