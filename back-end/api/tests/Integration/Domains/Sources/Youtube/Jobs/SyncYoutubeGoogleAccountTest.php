<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\GetChannelDetails;
use App\Domains\Sources\Youtube\Actions\GetChannelReports;
use App\Domains\Sources\Youtube\Jobs\SyncYoutubeGoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncYoutubeGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_calls_actions(): void
    {
        Queue::fake();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        $this->mock(GetChannelDetails::class)
            ->shouldReceive('execute')
            ->andReturn([
                [
                    'id' => 123,
                    'snippet' => [
                        'title' => 'test title',
                    ],
                    'statistics' => [
                        'subscriberCount' => 1,
                        'videoCount' => 2,
                    ],
                ],
            ]);

        $this->mock(GetChannelReports::class)
            ->shouldReceive('execute')
            ->andReturn([
                [
                    'date' => now()->toDateString(),
                    'views' => 1,
                    'likes' => 2,
                    'comments' => 3,
                    'shares' => 4,
                ],
            ]);

        $job = new SyncYoutubeGoogleAccount($youtubeGoogleAccount->id);
        $job->handle();

        self::assertDatabaseHas('youtube_channels', [
            'external_id' => 123,
            'name' => 'test title',
        ]);

        self::assertDatabaseHas('youtube_channel_insights', [
            'views' => 1,
            'engagement' => 9,
            'subscriber_count' => 1,
            'number_of_videos' => 2,
        ]);
    }
}
