<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class RefreshTokenTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @throws ConnectionException
     */
    public function test_it_doesnt_refresh_token_if_it_is_recent_enough(): void
    {
        Http::preventStrayRequests();

        Http::fake();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);
        $this->assertEquals($youtubeGoogleAccount->token, $token);

        Http::assertNothingSent();
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_it_is_not_recent_enough(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => now()->subMinutes(56),
            ]);

        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_missing_token(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token' => null,
                'token_created_at' => now()->subMinutes(56),
            ]);

        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_missing_token_created_at(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => null,
            ]);

        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_returns_null_if_request_fails(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => now()->subMinutes(56),
            ]);

        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);
        $this->assertNull($token);
    }
}
