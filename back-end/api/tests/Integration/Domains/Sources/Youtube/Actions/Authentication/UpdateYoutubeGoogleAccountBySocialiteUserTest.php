<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\UpdateYoutubeGoogleAccountBySocialiteUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class UpdateYoutubeGoogleAccountBySocialiteUserTest extends TestCase
{
    use RefreshDatabase;

    public function test_execute_creates_new_user(): void
    {
        Carbon::setTestNow(now());
        $externalId = Str::random();

        $this->assertDatabaseMissing(
            'youtube_google_accounts',
            ['external_id' => $externalId]
        );

        $youtubeGoogleAccount = app(UpdateYoutubeGoogleAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($externalId),
            [
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
            ]
        );

        $this->assertNotNull($youtubeGoogleAccount);
        $this->assertInstanceOf(YoutubeGoogleAccount::class, $youtubeGoogleAccount);

        $this->assertSame($externalId, $youtubeGoogleAccount->external_id);
        $this->assertSame('<EMAIL>', $youtubeGoogleAccount->email);
        $this->assertSame('Test user', $youtubeGoogleAccount->name);
        $this->assertSame('https://image.com/non_existent', $youtubeGoogleAccount->image_url);
        $this->assertSame('refresh_token_123', $youtubeGoogleAccount->refresh_token);
        $this->assertSame('token_123', $youtubeGoogleAccount->token);
        $this->assertSame(now()->toDateTimeString(), $youtubeGoogleAccount->token_created_at->toDateTimeString());

        $this->assertNotEmpty($youtubeGoogleAccount->scopes);
        $this->assertContains('https://www.googleapis.com/auth/userinfo.profile', $youtubeGoogleAccount->scopes);
        $this->assertContains('https://www.googleapis.com/auth/userinfo.email', $youtubeGoogleAccount->scopes);
    }

    public function test_execute_updates_existing_user(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()->create();
        $databaseCount = YoutubeGoogleAccount::count();

        Carbon::setTestNow(now()->addDay());

        $youtubeGoogleAccount = app(UpdateYoutubeGoogleAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($youtubeGoogleAccount->external_id)
        );

        $this->assertDatabaseCount(
            'youtube_google_accounts',
            $databaseCount
        );

        $this->assertNotNull($youtubeGoogleAccount);
        $this->assertInstanceOf(YoutubeGoogleAccount::class, $youtubeGoogleAccount);

        $this->assertSame($youtubeGoogleAccount->external_id, $youtubeGoogleAccount->external_id);
        $this->assertSame(now()->toDateTimeString(), $youtubeGoogleAccount->token_created_at->toDateTimeString());
    }

    public function getMockedSocialiteUser(string $id): \Laravel\Socialite\Two\User
    {
        $mock = Mockery::mock(\Laravel\Socialite\Two\User::class);

        $mock->shouldReceive('getId')->andReturn($id);
        $mock->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $mock->shouldReceive('getName')->andReturn('Test user');
        $mock->shouldReceive('getAvatar')->andReturn('https://image.com/non_existent');
        $mock->refreshToken = 'refresh_token_123';
        $mock->token = 'token_123';

        return $mock;
    }
}
