<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Actions\GetChannelDetails;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetChannelDetailsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null_for_youtube_google_account(): void
    {
        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel details. Could not retrieve token for Youtube Google Account #%s.',
            $youtubeGoogleAccount->id
        ));

        app(GetChannelDetails::class)
            ->execute($youtubeGoogleAccount);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel details. Received HTTP error with status code %s for Youtube Google Account #%s. Response body:\n\n',
            500,
            $youtubeGoogleAccount->id,
        ));

        app(GetChannelDetails::class)
            ->execute($youtubeGoogleAccount);
    }

    public function test_list_channel_details(): void
    {
        Http::preventStrayRequests();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl() => Http::response([
                'kind' => 'youtube#channelListResponse',
                'etag' => 'ynrt7LtnZcDRfqWu14xLiyf9DJk',
                'pageInfo' => [
                    'totalResults' => 1,
                    'resultsPerPage' => 50,
                ],
                'items' => [
                    [
                        'kind' => 'youtube#channel',
                        'etag' => '7a6q-ZOM_fwoNVfMXCGN9Wg0qxc',
                        'id' => 'UC_V0GMTdxxzAoKFYj4cOlbA',
                        'snippet' => [
                            'title' => 'Localium',
                            'description' => '',
                            'customUrl' => '@localium4154',
                            'publishedAt' => '2022-01-14T18:59:39.01521Z',
                            'thumbnails' => [
                                'default' => [
                                    'url' => 'https://yt3.ggpht.com/IffL_lxJ4a_fuogqgnvV3JzAvs1kTtu9OaN3BC2ypD8pYMdsds_QRYeGZrs4j4MD5Qa92ZZ1=s88-c-k-c0x00ffffff-no-rj',
                                    'width' => 88,
                                    'height' => 88,
                                ],
                                'medium' => [
                                    'url' => 'https://yt3.ggpht.com/IffL_lxJ4a_fuogqgnvV3JzAvs1kTtu9OaN3BC2ypD8pYMdsds_QRYeGZrs4j4MD5Qa92ZZ1=s240-c-k-c0x00ffffff-no-rj',
                                    'width' => 240,
                                    'height' => 240,
                                ],
                                'high' => [
                                    'url' => 'https://yt3.ggpht.com/IffL_lxJ4a_fuogqgnvV3JzAvs1kTtu9OaN3BC2ypD8pYMdsds_QRYeGZrs4j4MD5Qa92ZZ1=s800-c-k-c0x00ffffff-no-rj',
                                    'width' => 800,
                                    'height' => 800,
                                ],
                            ],
                            'localized' => [
                                'title' => 'Localium',
                                'description' => '',
                            ],
                        ],
                        'contentDetails' => [
                            'relatedPlaylists' => [
                                'likes' => 'LL',
                                'uploads' => 'UU_V0GMTdxxzAoKFYj4cOlbA',
                            ],
                        ],
                        'statistics' => [
                            'viewCount' => '10',
                            'subscriberCount' => '20',
                            'hiddenSubscriberCount' => false,
                            'videoCount' => '30',
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $results = app(GetChannelDetails::class)
            ->execute($youtubeGoogleAccount);

        self::assertCount(1, $results);

        $result = $results[0];

        $snippet = $result['snippet'];
        self::assertSame('Localium', $snippet['title']);

        $statistics = $result['statistics'];
        self::assertSame('10', $statistics['viewCount']);
        self::assertSame('20', $statistics['subscriberCount']);
        self::assertSame('30', $statistics['videoCount']);
    }

    public function test_assert_correct_body(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        Http::fake();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetChannelDetails::class)
            ->execute($youtubeGoogleAccount);

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('id,snippet,contentDetails,statistics', Arr::get($queryParams, 'part'));
            $this->assertEquals(true, Arr::get($queryParams, 'mine'));
            $this->assertEquals(50, Arr::get($queryParams, 'maxResults'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl() => Http::response([
                'kind' => 'youtube#channelListResponse',
                'etag' => 'ynrt7LtnZcDRfqWu14xLiyf9DJk',
                'pageInfo' => [
                    'totalResults' => 1,
                    'resultsPerPage' => 50,
                ],
                'items' => [],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $results = app(GetChannelDetails::class)
            ->execute($youtubeGoogleAccount);

        self::assertEmpty($results);
    }

    protected function getUrl(): string
    {
        return 'https://www.googleapis.com/youtube/v3/channels*';
    }
}
