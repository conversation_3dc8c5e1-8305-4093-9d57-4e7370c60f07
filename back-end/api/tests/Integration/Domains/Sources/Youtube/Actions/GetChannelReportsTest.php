<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Actions\GetChannelReports;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetChannelReportsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null_for_youtube_google_account(): void
    {
        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $youtubeChannel = YoutubeChannel::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel reports. Could not retrieve token for Youtube Channel #%s.',
            $youtubeChannel->id
        ));

        app(GetChannelReports::class)
            ->execute($youtubeChannel);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $youtubeChannel = YoutubeChannel::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel reports. Received HTTP error with status code %s for Youtube Channel #%s. Response body:\n\n',
            500,
            $youtubeChannel->id,
        ));

        app(GetChannelReports::class)
            ->execute($youtubeChannel);
    }

    public function test_list_channel_details(): void
    {
        Http::preventStrayRequests();

        $youtubeChannel = YoutubeChannel::factory()
            ->create();

        Http::fake([
            $this->getUrl() => Http::response([
                'kind' => 'youtubeAnalytics#resultTable',
                'columnHeaders' => [
                    [
                        'name' => 'day',
                        'columnType' => 'DIMENSION',
                        'dataType' => 'STRING',
                    ],
                    [
                        'name' => 'views',
                        'columnType' => 'METRIC',
                        'dataType' => 'INTEGER',
                    ],
                    [
                        'name' => 'likes',
                        'columnType' => 'METRIC',
                        'dataType' => 'INTEGER',
                    ],
                    [
                        'name' => 'comments',
                        'columnType' => 'METRIC',
                        'dataType' => 'INTEGER',
                    ],
                    [
                        'name' => 'shares',
                        'columnType' => 'METRIC',
                        'dataType' => 'INTEGER',
                    ],
                ],
                'rows' => [
                    [
                        '2024-04-18',
                        1,
                        2,
                        3,
                        4,
                    ],
                    [
                        '2024-04-22',
                        3,
                        0,
                        0,
                        0,
                    ],
                    [
                        '2024-04-23',
                        3,
                        0,
                        0,
                        0,
                    ],
                    [
                        '2024-07-31',
                        3,
                        0,
                        0,
                        0,
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $results = app(GetChannelReports::class)
            ->execute($youtubeChannel);

        self::assertCount(4, $results);

        $result = $results[0];

        self::assertSame('2024-04-18', $result['date']);
        self::assertSame(1, $result['views']);
        self::assertSame(2, $result['likes']);
        self::assertSame(3, $result['comments']);
        self::assertSame(4, $result['shares']);
    }

    public function test_assert_correct_body(): void
    {
        $youtubeChannel = YoutubeChannel::factory()
            ->create();

        Http::fake();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetChannelReports::class)
            ->execute($youtubeChannel);

        Http::assertSent(function (Request $request) use ($youtubeChannel) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('views,likes,comments,shares', Arr::get($queryParams, 'metrics'));
            $this->assertEquals(now()->subWeek()->toDateString(), Arr::get($queryParams, 'startDate'));
            $this->assertEquals(now()->toDateString(), Arr::get($queryParams, 'endDate'));
            $this->assertEquals('day', Arr::get($queryParams, 'dimensions'));
            $this->assertEquals(sprintf('channel==%s', $youtubeChannel->external_id), Arr::get($queryParams, 'ids'));
            $this->assertEquals(50, Arr::get($queryParams, 'maxResults'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $youtubeChannel = YoutubeChannel::factory()
            ->create();

        Http::fake([
            $this->getUrl() => Http::response([
                'rows' => [],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $results = app(GetChannelReports::class)
            ->execute($youtubeChannel);

        self::assertEmpty($results);
    }

    protected function getUrl(): string
    {
        return 'https://youtubeanalytics.googleapis.com/v2/reports*';
    }
}
