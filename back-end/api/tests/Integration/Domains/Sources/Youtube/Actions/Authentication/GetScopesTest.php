<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\GetScopes;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetScopesTest extends TestCase
{
    public function test_it_returns_empty_array_for_null_token(): void
    {
        Http::fake();
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturnNull();

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($youtubeGoogleAccount);
        $this->assertEmpty($result);

        Http::assertNothingSent();
    }

    public function test_it_returns_scopes_from_response(): void
    {
        Http::fake([
            '*' => Http::response([
                'scope' => 'scope1 scope2',
            ]),
        ]);
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('token');

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($youtubeGoogleAccount);
        $this->assertContains('scope1', $result);
        $this->assertContains('scope2', $result);

        Http::assertSent(function (Request $request) {
            return $request['access_token'] === 'token';
        });
    }

    public function test_it_returns_empty_array_for_failing_request(): void
    {
        Http::fake([
            '*' => Http::response(null, 500),
        ]);
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('token');

        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($youtubeGoogleAccount);
        $this->assertEmpty($result);
    }
}
