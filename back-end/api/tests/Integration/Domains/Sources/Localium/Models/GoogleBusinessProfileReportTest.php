<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Localium\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfileReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleBusinessProfileReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_business_profile(): void
    {
        $report = GoogleBusinessProfileReport::factory()
            ->for(GoogleBusinessProfile::factory())
            ->create();

        $this->assertInstanceOf(
            GoogleBusinessProfile::class,
            $report->googleBusinessProfile
        );
    }

    public function test_it_has_data_source(): void
    {
        $report = GoogleBusinessProfileReport::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $report->google_business_profile_id,
                'sourceable_type' => GoogleBusinessProfile::class,
            ]);

        $dataSource = $report->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(GoogleBusinessProfile::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $report = GoogleBusinessProfileReport::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $report->google_business_profile_id,
                'sourceable_type' => GoogleBusinessProfile::class,
            ]);

        self::assertContains(
            $report->id,
            GoogleBusinessProfileReport::query()->pluck('id')
        );

        self::assertContains(
            $report->id,
            GoogleBusinessProfileReport::query()
                ->withDataSources([$dataSource->id])
                ->pluck('id')
        );
    }
}
