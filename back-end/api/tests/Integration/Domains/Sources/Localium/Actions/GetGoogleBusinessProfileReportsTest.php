<?php

namespace Tests\Integration\Domains\Sources\Localium\Actions;

use App\Domains\Sources\Localium\Actions\GetGoogleBusinessProfileReports;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use App\Domains\Sources\Localium\Support\Exceptions\CannotGetGoogleBusinessProfileReports;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetGoogleBusinessProfileReportsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    private array $locationIds;

    private GoogleBusinessProfile $profile;

    private LocaliumAccount $account;

    protected function setUp(): void
    {
        parent::setUp();

        $this->account = LocaliumAccount::factory()->create([
            'user_id' => 'USER',
            'token' => 'TOKEN',
        ]);

        $this->profile = GoogleBusinessProfile::factory()->create([
            'external_id' => 'f622ed2c-65b6-4b52-abc0-08678a009cf7',
        ]);

        $this->locationIds = [$this->profile->external_id];

        $this->mockResponse = json_encode([
            'data' => [
                [
                    'location_id' => 'f622ed2c-65b6-4b52-abc0-08678a009cf7',
                    'date' => '********',
                    'call_clicks' => 5,
                    'actions_driving_directions' => 10,
                    'business_direction_requests' => 0,
                    'business_impressions_desktop_maps' => 25,
                    'business_impressions_mobile_maps' => 25,
                    'business_impressions_desktop_search' => 50,
                    'business_impressions_mobile_search' => 50,
                ],
            ],
        ]);
    }

    public function test_it_can_fetch_and_store_statistics(): void
    {
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/statistics' => Http::response($this->mockResponse, 200),
        ]);

        app(GetGoogleBusinessProfileReports::class)->execute($this->account, $this->locationIds);

        Http::assertSent(function (Request $request) {
            return $request->url() === 'https://app.localium.com/api/api/v1/google/data-studio/statistics' &&
                $request->hasHeader('Authorization', 'Bearer TOKEN') &&
                $request->hasHeader('User', 'USER') &&
                $request->hasHeader('Accept', 'application/json') &&
                $request->hasHeader('Content-Type', 'application/json') &&
                $request['location_ids'] === $this->locationIds &&
                $request['time_zone'] === 'Europe/Amsterdam';
        });

        $this->assertDatabaseHas('google_business_profile_reports', [
            'google_business_profile_id' => $this->profile->id,
            'date' => Carbon::createFromFormat('Ymd', '********')->startOfDay(),
            'views_google' => 100,
            'phone_calls' => 5,
            'views_directions' => 10,
            'views_maps' => 50,
        ]);
    }

    public function test_it_updates_existing_statistics(): void
    {
        $existingDate = Carbon::createFromFormat('Ymd', '********')->startOfDay();
        $this->profile->reports()->create([
            'date' => $existingDate,
            'views_google' => 50,
            'phone_calls' => 2,
            'views_directions' => 5,
            'views_maps' => 25,
        ]);

        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/statistics' => Http::response($this->mockResponse, 200),
        ]);

        app(GetGoogleBusinessProfileReports::class)->execute($this->account, $this->locationIds);

        $this->assertDatabaseHas('google_business_profile_reports', [
            'google_business_profile_id' => $this->profile->id,
            'date' => $existingDate->toDateTimeString(),
            'views_google' => 100,
            'phone_calls' => 5,
            'views_directions' => 10,
            'views_maps' => 50,
        ]);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/statistics' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetGoogleBusinessProfileReports::class);
        $this->expectExceptionMessage(
            'Cannot get Google Business Profile statistics. Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetGoogleBusinessProfileReports::class)->execute($this->account, $this->locationIds);
    }

    public function test_it_skips_unknown_location_ids(): void
    {
        $unknownLocationIds = ['unknown-id'];

        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/statistics' => Http::response([
                'data' => [
                    [
                        'location_id' => 'unknown-id',
                        'date' => '********',
                        'views_search' => 100,
                        'actions_phone' => 5,
                        'actions_driving_directions' => 10,
                        'views_maps' => 50,
                    ],
                ],
            ], 200),
        ]);

        app(GetGoogleBusinessProfileReports::class)->execute($this->account, $unknownLocationIds);

        $this->assertDatabaseCount('google_business_profile_reports', 0);
    }
}
