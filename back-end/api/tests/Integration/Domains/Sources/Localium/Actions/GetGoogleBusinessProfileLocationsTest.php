<?php

namespace Tests\Integration\Domains\Sources\Localium\Actions;

use App\Domains\Sources\Localium\Actions\GetGoogleBusinessProfileLocations;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use App\Domains\Sources\Localium\Support\Exceptions\CannotGetGoogleBusinessProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetGoogleBusinessProfileLocationsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    private LocaliumAccount $account;

    protected function setUp(): void
    {
        parent::setUp();

        $this->account = LocaliumAccount::factory()->create([
            'user_id' => 'USER',
            'token' => 'TOKEN',
        ]);

        $this->mockResponse = json_encode([
            'data' => [
                'locations' => [
                    [
                        'id' => '12345',
                        'name' => 'Test Location 1',
                    ],
                    [
                        'id' => '67890',
                        'name' => 'Test Location 2',
                    ],
                ],
            ],
        ]);
    }

    public function test_it_can_fetch_and_store_google_business_profile_locations(): void
    {
        // Mock the HTTP response
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/config' => Http::response($this->mockResponse, 200),
        ]);

        // Execute the action
        app(GetGoogleBusinessProfileLocations::class)->execute($this->account);

        // Assert that the HTTP request was sent with the correct headers
        Http::assertSent(function (Request $request) {
            return $request->url() === 'https://app.localium.com/api/api/v1/google/data-studio/config' &&
                $request->hasHeader('Authorization', 'Bearer TOKEN') &&
                $request->hasHeader('User', 'USER');
        });

        // Assert that the locations were stored in the database
        $this->assertDatabaseCount('google_business_profiles', 2);

        $this->assertDatabaseHas('google_business_profiles', [
            'external_id' => '12345',
            'name' => 'Test Location 1',
            'localium_account_id' => $this->account->id,
        ]);

        $this->assertDatabaseHas('google_business_profiles', [
            'external_id' => '67890',
            'name' => 'Test Location 2',
            'localium_account_id' => $this->account->id,
        ]);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        // Mock an error response
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/config' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        // Expect an exception
        $this->expectException(CannotGetGoogleBusinessProfile::class);
        $this->expectExceptionMessage(
            'Cannot get Google Business Profile. Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        // Execute the action
        app(GetGoogleBusinessProfileLocations::class)->execute($this->account);
    }

    public function test_it_updates_existing_locations(): void
    {
        // Create an existing location in the database
        $existingLocation = GoogleBusinessProfile::factory()->create([
            'external_id' => '12345',
            'name' => 'Old Location Name',
        ]);

        // Mock the HTTP response
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/config' => Http::response($this->mockResponse, 200),
        ]);

        // Execute the action
        app(GetGoogleBusinessProfileLocations::class)->execute($existingLocation->localiumAccount);

        // Assert that the existing location was updated
        $this->assertDatabaseHas('google_business_profiles', [
            'id' => $existingLocation->id,
            'external_id' => '12345',
            'name' => 'Test Location 1',
            'localium_account_id' => $existingLocation->localium_account_id,
        ]);
    }

    public function test_it_handles_empty_response(): void
    {
        // Mock an empty response
        Http::fake([
            'app.localium.com/api/api/v1/google/data-studio/config' => Http::response([
                'data' => [
                    'locations' => [],
                ],
            ], 200),
        ]);

        // Execute the action
        app(GetGoogleBusinessProfileLocations::class)->execute($this->account);

        // Assert that no locations were stored in the database
        $this->assertDatabaseCount('google_business_profiles', 0);
    }
}
