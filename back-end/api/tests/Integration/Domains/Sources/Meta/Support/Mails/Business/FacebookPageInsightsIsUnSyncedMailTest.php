<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Support\Mails\Business\FacebookPageInsightsIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FacebookPageInsightsIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $facebookPage = FacebookPage::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new FacebookPageInsightsIsUnSyncedMail(
            facebookPageId: $facebookPage->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Facebook Page #%s is not having it Insights being synchronised', $facebookPage->id));

        $mail->assertSeeInHtml($facebookPage->id);
        $mail->assertSeeInHtml($facebookPage->name);
    }
}
