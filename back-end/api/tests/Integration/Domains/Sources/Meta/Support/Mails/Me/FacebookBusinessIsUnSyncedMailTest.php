<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Support\Mails\Me;

use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Support\Mails\Me\FacebookBusinessIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FacebookBusinessIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new FacebookBusinessIsUnSyncedMail(
            facebookBusinessId: $facebookBusiness->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Facebook Business #%s is not being synchronised', $facebookBusiness->id));

        $mail->assertSeeInHtml($facebookBusiness->id);
        $mail->assertSeeInHtml($facebookBusiness->name);
    }
}
