<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Support\Mails\Business\InstagramAccountInsightsIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class InstagramAccountIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new InstagramAccountInsightsIsUnSyncedMail(
            instagramAccountId: $instagramAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Instagram Account #%s insights is not being synchronised', $instagramAccount->id));

        $mail->assertSeeInHtml($instagramAccount->id);
        $mail->assertSeeInHtml($instagramAccount->username);
    }
}
