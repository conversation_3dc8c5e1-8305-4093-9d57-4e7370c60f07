<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Mails\Business\FacebookAdAccountIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FacebookAdAccountIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new FacebookAdAccountIsUnSyncedMail(
            facebookAdAccountId: $facebookAdAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Facebook AdAccount #%s is not being synchronised', $facebookAdAccount->id));

        $mail->assertSeeInHtml($facebookAdAccount->id);
        $mail->assertSeeInHtml($facebookAdAccount->name);
    }
}
