<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Commands\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookBusinesses;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SyncBusinessesTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
            ]),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $this->mock(GetFacebookBusinesses::class)
            ->shouldReceive('execute')
            ->twice()
            ->passthru();

        $this->artisan('sources:meta:me:sync:businesses', [
            'facebookAccountId' => $facebookAccount->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Facebook Businesses for Facebook Account #%s (%s)',
                $facebookAccount->id,
                $facebookAccount->name
            ))
            ->expectsOutput('Facebook Account synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:meta:me:sync:businesses', [
            'facebookAccountId' => 9999999,
        ])
            ->expectsOutput('Facebook Account not found')
            ->assertExitCode(1);
    }
}
