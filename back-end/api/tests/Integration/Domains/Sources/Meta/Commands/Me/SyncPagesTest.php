<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Commands\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SyncPagesTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
            ]),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $this->mock(GetFacebookPages::class)
            ->shouldReceive('execute')
            ->twice()
            ->passthru();

        $this->artisan('sources:meta:me:sync:pages', [
            'facebookAccountId' => $facebookAccount->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Facebook Pages for Facebook Account #%s (%s)',
                $facebookAccount->id,
                $facebookAccount->name
            ))
            ->expectsOutput('Facebook Account synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:meta:me:sync:pages', [
            'facebookAccountId' => 9999999,
        ])
            ->expectsOutput('Facebook Account not found')
            ->assertExitCode(1);
    }
}
