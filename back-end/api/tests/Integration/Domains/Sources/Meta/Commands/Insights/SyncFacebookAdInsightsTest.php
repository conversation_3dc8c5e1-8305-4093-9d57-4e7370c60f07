<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Commands\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookAdInsights;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SyncFacebookAdInsightsTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
            ]),
        ]);

        $facebookAdAccount = FacebookAdAccount::factory()
            ->create();

        $spy = $this->spy(GetFacebookAdInsights::class);

        $spy->shouldReceive('execute')
            ->withArgs(function (FacebookAdAccount $facebookAdAccount, Level $level) {
                return $level === Level::AD;
            })
            ->times(2)
            ->passthru();

        $spy->shouldReceive('execute')
            ->withArgs(function (FacebookAdAccount $facebookAdAccount, Level $level) {
                return $level === Level::CAMPAIGN;
            })
            ->times(2)
            ->passthru();

        $this->artisan('sources:meta:insights:sync', [
            'facebookAdAccountId' => $facebookAdAccount->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Facebook Ad Insights for Facebook Ad Account #%s (%s)',
                $facebookAdAccount->id,
                $facebookAdAccount->name
            ))
            ->expectsOutput('Facebook Ad Account synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:meta:insights:sync', [
            'facebookAdAccountId' => 9999999,
        ])
            ->expectsOutput('Facebook Ad Account not found')
            ->assertExitCode(1);
    }
}
