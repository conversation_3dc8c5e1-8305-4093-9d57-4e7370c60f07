<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Commands;

use App\Domains\Sources\Meta\Jobs\SyncFacebookAccounts;
use App\Domains\Sources\Meta\Jobs\SyncFacebookBusinesses;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;

class SyncTest extends TestCase
{
    public function test_handle_dispatches_jobs(): void
    {
        Bus::fake();

        $this->artisan('sources:meta:sync')
            ->expectsOutput('Dispatching jobs to sync all Facebook connections')
            ->expectsOutput('Jobs dispatched successfully')
            ->assertOk();

        Bus::assertChained([
            SyncFacebookAccounts::class,
            SyncFacebookBusinesses::class,
        ]);
    }
}
