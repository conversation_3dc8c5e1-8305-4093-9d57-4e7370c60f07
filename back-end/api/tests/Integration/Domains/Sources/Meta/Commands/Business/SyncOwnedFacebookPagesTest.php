<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Commands\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookPages;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SyncOwnedFacebookPagesTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
            ]),
        ]);

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $this->spy(GetOwnedFacebookPages::class)
            ->shouldReceive('execute')
            ->times(2)
            ->passthru();

        $this->spy(GetAccessibleFacebookPages::class)
            ->shouldReceive('execute')
            ->times(2)
            ->passthru();

        $this->artisan('sources:meta:business:sync:pages', [
            'facebookBusinessId' => $facebookBusiness->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Facebook Pages for Facebook Business #%s (%s)',
                $facebookBusiness->id,
                $facebookBusiness->name
            ))
            ->expectsOutput('Facebook Business synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:meta:business:sync:pages', [
            'facebookBusinessId' => 9999999,
        ])
            ->expectsOutput('Facebook Business not found')
            ->assertExitCode(1);
    }
}
