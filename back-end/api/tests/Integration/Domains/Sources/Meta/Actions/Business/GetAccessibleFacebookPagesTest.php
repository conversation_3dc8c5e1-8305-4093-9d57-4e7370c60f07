<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookPages;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetAccessibleFacebookPagesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_extends_parent(): void
    {
        $class = new GetAccessibleFacebookPages;

        self::assertInstanceOf(GetOwnedFacebookPages::class, $class);
    }

    public function test_it_sets_url(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        $class = new GetAccessibleFacebookPages;
        $url = $class->getUrl($facebookBusiness);

        self::assertEquals(
            sprintf('https://graph.facebook.com/v23.0/%s/client_pages', $facebookBusiness->external_id),
            $url
        );
    }

    protected function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/client_pages', $facebookBusiness->external_id);
    }
}
