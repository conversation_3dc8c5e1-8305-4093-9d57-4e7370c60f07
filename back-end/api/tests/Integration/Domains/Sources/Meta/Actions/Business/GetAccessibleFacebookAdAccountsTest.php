<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetAccessibleFacebookAdAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_extends_parent(): void
    {
        $class = new GetAccessibleFacebookAdAccounts;

        self::assertInstanceOf(GetOwnedFacebookAdAccounts::class, $class);
    }

    public function test_it_sets_url(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        $class = new GetAccessibleFacebookAdAccounts;
        $url = $class->getUrl($facebookBusiness);

        self::assertEquals(
            sprintf('https://graph.facebook.com/v23.0/%s/client_ad_accounts', $facebookBusiness->external_id),
            $url
        );
    }

    protected function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/client_ad_accounts', $facebookBusiness->external_id);
    }
}
