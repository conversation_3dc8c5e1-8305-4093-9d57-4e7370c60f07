<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Authentication;

use App\Domains\Sources\Meta\Actions\Authentication\GenerateOAuthRedirectLink;
use Illuminate\Support\Facades\Config;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class GenerateOAuthRedirectLinkTest extends TestCase
{
    protected function getExpectedRedirectUrl(): string
    {
        return 'https://example.com/oauth/callback';
    }

    protected function getExpectedOAuthUrl(): string
    {
        $baseUrl = 'https://www.facebook.com/v3.3/dialog/oauth';
        $params = [
            'redirect_uri' => $this->getExpectedRedirectUrl(),
            'scope' => 'email',
            'response_type' => 'code',
        ];

        $queryString = http_build_query($params);

        return $baseUrl.'?'.$queryString;
    }

    public function test_it_generates_oauth_redirect_link_with_correct_parameters(): void
    {
        Config::set('services.facebook.config_id', 'config-facebook-id');

        $provider = Mockery::mock('Laravel\Socialite\Two\FacebookProvider');

        $provider->shouldReceive('scopes')
            ->with([
                'email',
                'profile',
                'openid',
            ])
            ->andReturnSelf();

        $provider->shouldReceive('with')
            ->with([
                'config_id' => 'config-facebook-id',
            ])
            ->andReturnSelf();

        $provider->shouldReceive('redirectUrl')
            ->andReturn($this->getExpectedRedirectUrl());

        $provider->shouldReceive('stateless');

        $provider->shouldReceive('redirect->getTargetUrl')
            ->andReturn($this->getExpectedOAuthUrl());

        Socialite::shouldReceive('driver')
            ->with('facebook')
            ->andReturn($provider);

        $redirectUrl = app(GenerateOAuthRedirectLink::class)
            ->execute($this->getExpectedRedirectUrl());

        $this->assertSame($this->getExpectedOAuthUrl(), $redirectUrl);
    }

    public function test_it_generates_oauth_redirect_link(): void
    {
        $redirectUrl = app(GenerateOAuthRedirectLink::class)
            ->execute($this->getExpectedRedirectUrl());

        $this->assertSame($this->getExpectedOAuthUrl(), $redirectUrl);
    }
}
