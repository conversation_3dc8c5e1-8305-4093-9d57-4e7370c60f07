<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetOwnedFacebookAdAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $facebookBusiness = FacebookBusiness::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get ad accounts for Facebook Business #%s. Could not retrieve token.',
            $facebookBusiness->id
        ));

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $facebookBusiness = FacebookBusiness::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get ad accounts for Facebook Business #%s. Received HTTP error with status code %s. Response body:\n\n',
            $facebookBusiness->id,
            500,
        ));

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);
    }

    public function test_updates_existing_facebook_business(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookAdAccount $facebookAdAccount */
        $facebookAdAccount = FacebookAdAccount::factory()->for($facebookBusiness)->create([
            'name' => 'old_name',
        ]);

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => $facebookAdAccount->external_id,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                    'next' => 'https://next.link',
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookAdAccount::count();

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);

        $afterDatabaseCount = FacebookAdAccount::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $facebookAdAccount->refresh();
        $this->assertSame('new_name', $facebookAdAccount->name);
    }

    public function test_creates_new_facebook_businesses(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'facebook_ad_accounts',
            [
                'facebook_business_id' => $facebookBusiness->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);

        $this->assertDatabaseHas(
            'facebook_ad_accounts',
            [
                'facebook_business_id' => $facebookBusiness->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );
    }

    public function test_returns_next_page_token_if_needed(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $result = app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);
        self::assertNull($result);

        $result = app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('token', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('50', Arr::get($queryParams, 'limit'));
            $this->assertEquals('name', Arr::get($queryParams, 'fields'));

            return true;
        });
    }

    public function test_assert_correct_body_set_with_cursor(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookAdAccount::count();

        app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness);

        $afterDatabaseCount = FacebookAdAccount::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    protected function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/owned_ad_accounts*', $facebookBusiness->external_id);
    }
}
