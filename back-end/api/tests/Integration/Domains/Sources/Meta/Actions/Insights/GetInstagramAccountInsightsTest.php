<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetInstagramAccountInsights;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetInstagramAccountInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_does_nothing_if_token_missing(): void
    {
        Http::fake();

        $facebookPage = FacebookPage::factory()->create([
            'token' => null,
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->for($facebookPage)
            ->create();

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);

        Http::assertNothingSent();
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get insights for Instagram Account #%s. Received HTTP error with status code %s. Response body:\n\n',
            $instagramAccount->id,
            500,
        ));

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);
    }

    public function test_creates_new_instagram_account_insights(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [
                    [
                        'name' => 'reach',
                        'period' => 'day',
                        'total_value' => [
                            'value' => 22,
                        ],
                        'title' => 'Bereik',
                        'description' => 'Totale aantal unieke keren dat de mediadoelstellingen van het bedrijfsaccount zijn bekeken',
                        'id' => '12345/insights/reach/day',
                    ],
                    [
                        'name' => 'impressions',
                        'period' => 'day',
                        'total_value' => [
                            'value' => 44,
                        ],
                        'title' => 'Weergaven',
                        'description' => 'Totale aantal keer dat de mediadoelstellingen van het bedrijfsaccount zijn bekeken',
                        'id' => '12345/insights/impressions/day',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->assertDatabaseMissing(
            'instagram_account_insights',
            [
                'instagram_account_id' => $instagramAccount->id,
                'reach' => 22,
                'impressions' => 44,
            ]
        );

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);

        $this->assertDatabaseHas(
            'instagram_account_insights',
            [
                'instagram_account_id' => $instagramAccount->id,
                'reach' => 22,
                'impressions' => 44,
            ]
        );
    }

    protected function getUrl(InstagramAccount $instagramAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights*', $instagramAccount->external_id);
    }

    public function test_updates_existing_instagram_account_insights(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramAccountInsight $instagramAccountInsights */
        $instagramAccountInsights = InstagramAccountInsight::factory()
            ->for($instagramAccount)
            ->create([
                'date' => '2024-11-01 00:00:00',
                'reach' => 11,
                'impressions' => 33,
            ]);

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [
                    [
                        'name' => 'reach',
                        'period' => 'day',
                        'total_value' => [
                            'value' => 22,
                        ],
                        'title' => 'Bereik',
                        'description' => 'Totale aantal unieke keren dat de mediadoelstellingen van het bedrijfsaccount zijn bekeken',
                        'id' => '12345/insights/reach/day',
                    ],
                    [
                        'name' => 'impressions',
                        'period' => 'day',
                        'total_value' => [
                            'value' => 44,
                        ],
                        'title' => 'Weergaven',
                        'description' => 'Totale aantal keer dat de mediadoelstellingen van het bedrijfsaccount zijn bekeken',
                        'id' => '12345/insights/impressions/day',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);

        $instagramAccountInsights->refresh();

        self::assertSame(11, $instagramAccountInsights->reach);
        self::assertSame(33, $instagramAccountInsights->impressions);
    }

    public function test_returns_next_page_token_if_needed(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $result = app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);
        self::assertNull($result);

        $result = app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake();

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('secret', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('day', Arr::get($queryParams, 'period'));
            $this->assertEquals(now()->subDay()->format('Y-m-d'), Arr::get($queryParams, 'since'));
            $this->assertEquals(now()->format('Y-m-d'), Arr::get($queryParams, 'until'));
            $this->assertEquals('reach,views', Arr::get($queryParams, 'metric'));

            return true;
        });
    }

    public function test_assert_correct_body_set_with_cursor(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake();

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $initialDatabaseCount = InstagramAccountInsight::count();

        app(GetInstagramAccountInsights::class)
            ->execute($instagramAccount);

        $afterDatabaseCount = InstagramAccountInsight::count();

        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }
}
