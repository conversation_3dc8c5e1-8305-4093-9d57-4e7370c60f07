<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Authentication;

use App\Domains\Sources\Meta\Actions\Authentication\UpdateFacebookAccountBySocialiteUser;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class UpdateFacebookAccountBySocialiteUserTest extends TestCase
{
    use RefreshDatabase;

    public function test_execute_creates_new_user(): void
    {
        Carbon::setTestNow(now());
        $externalId = Str::random();

        $this->assertDatabaseMissing(
            'facebook_accounts',
            ['external_id' => $externalId]
        );

        $facebookAccount = app(UpdateFacebookAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($externalId)
        );

        $this->assertNotNull($facebookAccount);
        $this->assertInstanceOf(FacebookAccount::class, $facebookAccount);

        $this->assertSame($externalId, $facebookAccount->external_id);
        $this->assertSame('<EMAIL>', $facebookAccount->email);
        $this->assertSame('Test user', $facebookAccount->name);
        $this->assertSame('https://image.com/non_existent', $facebookAccount->image_url);
        $this->assertSame('token_123', $facebookAccount->token);
        $this->assertSame(now()->toDateTimeString(), $facebookAccount->token_created_at->toDateTimeString());
    }

    public function test_execute_updates_existing_user(): void
    {
        $facebookAccount = FacebookAccount::factory()->create();
        $databaseCount = FacebookAccount::count();

        Carbon::setTestNow(now()->addDay());

        $facebookAccount = app(UpdateFacebookAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($facebookAccount->external_id)
        );

        $this->assertDatabaseCount(
            'facebook_accounts',
            $databaseCount
        );

        $this->assertNotNull($facebookAccount);
        $this->assertInstanceOf(FacebookAccount::class, $facebookAccount);

        $this->assertSame($facebookAccount->external_id, $facebookAccount->external_id);
        $this->assertSame(now()->toDateTimeString(), $facebookAccount->token_created_at->toDateTimeString());
    }

    public function getMockedSocialiteUser(string $id): \Laravel\Socialite\Two\User
    {
        $mock = Mockery::mock(\Laravel\Socialite\Two\User::class);

        $mock->shouldReceive('getId')->andReturn($id);
        $mock->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $mock->shouldReceive('getName')->andReturn('Test user');
        $mock->shouldReceive('getAvatar')->andReturn('https://image.com/non_existent');
        $mock->refreshToken = 'refresh_token_123';
        $mock->token = 'token_123';

        return $mock;
    }
}
