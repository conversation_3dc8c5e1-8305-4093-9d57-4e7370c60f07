<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookPageInsights;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetFacebookPageInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_does_nothing_if_token_missing(): void
    {
        Http::fake();

        $facebookPage = FacebookPage::factory()->create([
            'token' => null,
        ]);

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);

        Http::assertNothingSent();
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $facebookPage = FacebookPage::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get insights for Facebook Page #%s. Received HTTP error with status code %s. Response body:\n\n',
            $facebookPage->id,
            500,
        ));

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);
    }

    public function test_updates_existing_facebook_page_insights(): void
    {
        Http::preventStrayRequests();

        $facebookPage = FacebookPage::factory()->create();

        /** @var FacebookPageInsight $facebookPageInsight */
        $facebookPageInsight = FacebookPageInsight::factory()->for($facebookPage)->create([
            'date' => Carbon::parse('2024-10-22T07:00:00+0000'),
        ]);

        Http::fake([
            $this->getUrl($facebookPage) => Http::response([
                'data' => [
                    [
                        'name' => 'page_post_engagements',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 1,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_impressions_unique',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 2,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fan_adds',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 3,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fan_removes',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 4,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fans',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 5,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $initialDatabaseCount = FacebookAdAccount::count();

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);

        $afterDatabaseCount = FacebookAdAccount::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $facebookPageInsight->refresh();
        $this->assertSame(1, $facebookPageInsight->page_post_engagements);
        $this->assertSame(2, $facebookPageInsight->page_impressions_unique);
        $this->assertSame(3, $facebookPageInsight->page_fan_adds);
        $this->assertSame(4, $facebookPageInsight->page_fan_removes);
        $this->assertSame(5, $facebookPageInsight->page_fans);
    }

    public function test_creates_new_facebook_page_insights(): void
    {
        Http::preventStrayRequests();

        $facebookPage = FacebookPage::factory()->create();

        self::assertNull($facebookPage->insights_last_synced_at);

        Http::fake([
            $this->getUrl($facebookPage) => Http::response([
                'data' => [
                    [
                        'name' => 'page_post_engagements',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 1,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_impressions_unique',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 2,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fan_adds',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 3,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fan_removes',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 4,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                    [
                        'name' => 'page_fans',
                        'period' => 'day',
                        'values' => [
                            [
                                'value' => 5,
                                'end_time' => '2024-10-22T07:00:00+0000',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->assertDatabaseMissing(
            'facebook_page_insights',
            [
                'facebook_page_id' => $facebookPage->id,
                'date' => Carbon::parse('2024-10-22T07:00:00+0000'),
                'page_post_engagements' => 1,
                'page_impressions_unique' => 2,
                'page_fan_adds' => 3,
                'page_fan_removes' => 4,
                'page_fans' => 5,
            ]
        );

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);

        $this->assertDatabaseHas(
            'facebook_page_insights',
            [
                'facebook_page_id' => $facebookPage->id,
                'date' => Carbon::parse('2024-10-22T07:00:00+0000'),
                'page_post_engagements' => 1,
                'page_impressions_unique' => 2,
                'page_fan_adds' => 3,
                'page_fan_removes' => 4,
                'page_fans' => 5,
            ]
        );

        $facebookPage->refresh();
        self::assertNotNull($facebookPage->insights_last_synced_at);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $facebookPage = FacebookPage::factory()->create();

        Http::fake();

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('secret', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('day', Arr::get($queryParams, 'period'));
            $this->assertEquals('last_30d', Arr::get($queryParams, 'date_preset'));
            $this->assertEquals('page_post_engagements,page_impressions_unique,page_fans,page_fan_adds,page_fan_removes', Arr::get($queryParams, 'metric'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $facebookPage = FacebookPage::factory()->create();

        Http::fake([
            $this->getUrl($facebookPage) => Http::response([
                'data' => [],
            ]),
        ]);

        $initialDatabaseCount = FacebookPageInsight::count();

        app(GetFacebookPageInsights::class)
            ->execute($facebookPage);

        $afterDatabaseCount = FacebookPageInsight::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    protected function getUrl(FacebookPage $facebookPage): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights*', $facebookPage->external_id);
    }
}
