<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Actions\Insights\GetFacebookAdInsights;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookAdInsight;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class GetFacebookAdInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null_for_facebook_account(): void
    {
        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookAccount::factory())
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get insights for Facebook Ad Account #%s. Could not retrieve token.',
            $facebookAdAccount->id
        ));

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::AD);
    }

    public function test_throws_exception_if_token_null_for_facebook_business(): void
    {
        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get insights for Facebook Ad Account #%s. Could not retrieve token.',
            $facebookAdAccount->id
        ));

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::AD);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookAccount::factory())
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get insights for Facebook Ad Account #%s. Received HTTP error with status code %s. Response body:\n\n',
            $facebookAdAccount->id,
            500,
        ));

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::CAMPAIGN);
    }

    public function test_creates_new_facebook_ad_insights(): void
    {
        Http::preventStrayRequests();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake([
            $this->getUrl($facebookAdAccount) => Http::response([
                'data' => [
                    [
                        'ad_id' => '123',
                        'campaign_id' => '321',
                        'campaign_name' => '[01-01-2024] campaign name',
                        'ctr' => '9.770115',
                        'reach' => '162',
                        'spend' => '0.98',
                        'clicks' => '17',
                        'cpm' => '5.632184',
                        'impressions' => '174',
                        'actions' => [
                            [
                                'action_type' => 'post_engagement',
                                'value' => '7',
                            ],
                            [
                                'action_type' => 'page_engagement',
                                'value' => '7',
                            ],
                            [
                                'action_type' => 'post_reaction',
                                'value' => '1',
                            ],
                            [
                                'action_type' => 'link_click',
                                'value' => '6',
                            ],
                        ],
                        'date_start' => '2024-07-19',
                        'date_stop' => '2024-07-19',
                        'publisher_platform' => 'facebook',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'facebook_campaigns',
            [
                'facebook_ad_account_id' => $facebookAdAccount->id,
                'external_id' => 321,
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_ads',
            [
                'external_id' => 123,
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_ad_insights',
            [
                'cpm' => '5.6322',
            ]
        );

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::AD);

        $this->assertDatabaseHas(
            'facebook_campaigns',
            [
                'facebook_ad_account_id' => $facebookAdAccount->id,
                'external_id' => 321,
            ]
        );

        $this->assertDatabaseHas(
            'facebook_ads',
            [
                'external_id' => 123,
            ]
        );

        $this->assertDatabaseHas(
            'facebook_ad_insights',
            [
                'cpm' => '5.6322',
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_campaign_insights',
            [
                'cpm' => '5.6322',
            ]
        );
    }

    public function test_creates_new_facebook_campaign_insights(): void
    {
        Http::preventStrayRequests();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake([
            $this->getUrl($facebookAdAccount) => Http::response([
                'data' => [
                    [
                        'ad_id' => '123',
                        'campaign_id' => '321',
                        'campaign_name' => '[01-01-2024] campaign name',
                        'ctr' => '9.770115',
                        'reach' => '162',
                        'spend' => '0.98',
                        'clicks' => '17',
                        'cpm' => '5.632184',
                        'impressions' => '174',
                        'actions' => [
                            [
                                'action_type' => 'post_engagement',
                                'value' => '7',
                            ],
                            [
                                'action_type' => 'page_engagement',
                                'value' => '7',
                            ],
                            [
                                'action_type' => 'post_reaction',
                                'value' => '1',
                            ],
                            [
                                'action_type' => 'link_click',
                                'value' => '6',
                            ],
                        ],
                        'date_start' => '2024-07-19',
                        'date_stop' => '2024-07-19',
                        'publisher_platform' => 'facebook',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'facebook_campaigns',
            [
                'facebook_ad_account_id' => $facebookAdAccount->id,
                'external_id' => 321,
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_campaign_insights',
            [
                'cpm' => '5.6322',
            ]
        );

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::CAMPAIGN);

        $this->assertDatabaseHas(
            'facebook_campaigns',
            [
                'facebook_ad_account_id' => $facebookAdAccount->id,
                'external_id' => 321,
            ]
        );

        $this->assertDatabaseHas(
            'facebook_campaign_insights',
            [
                'cpm' => '5.6322',
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_ads',
            [
                'external_id' => 123,
            ]
        );

        $this->assertDatabaseMissing(
            'facebook_ad_insights',
            [
                'cpm' => '5.6322',
            ]
        );
    }

    #[DataProvider('levelProvider')]
    public function test_returns_next_page_token_if_needed(Level $level): void
    {
        Http::preventStrayRequests();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake([
            $this->getUrl($facebookAdAccount) => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $result = app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, $level);
        self::assertNull($result);

        $result = app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, $level);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    #[DataProvider('levelProvider')]
    public function test_assert_correct_body_set_without_cursor(Level $level): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, $level, 'cursor_value');

        Http::assertSent(function (Request $request) use ($level) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $timeRange = sprintf(
                '{"since":"%s","until":"%s"}',
                now()->subWeeks(2)->format('Y-m-d'),
                now()->subDay()->format('Y-m-d')
            );

            $this->assertEquals('token', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('50', Arr::get($queryParams, 'limit'));
            $this->assertEquals(1, Arr::get($queryParams, 'time_increment'));
            $this->assertEquals($level->value, Arr::get($queryParams, 'level'));
            $this->assertEquals('publisher_platform', Arr::get($queryParams, 'breakdowns'));
            $this->assertEquals($timeRange, Arr::get($queryParams, 'time_range'));
            $this->assertEquals('ad_id,campaign_id,campaign_name,ctr,reach,spend,clicks,cpm,impressions,actions', Arr::get($queryParams, 'fields'));

            return true;
        });
    }

    #[DataProvider('levelProvider')]
    public function test_assert_correct_body_set_with_cursor(Level $level): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, $level, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    #[DataProvider('levelProvider')]
    public function test_does_not_throw_exceptions_if_no_data_present(Level $level): void
    {
        Http::preventStrayRequests();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookBusiness::factory())
            ->create();

        Http::fake([
            $this->getUrl($facebookAdAccount) => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = match ($level) {
            Level::AD => FacebookAdInsight::count(),
            Level::CAMPAIGN => FacebookCampaignInsight::count(),
        };

        app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, $level);

        $afterDatabaseCount = match ($level) {
            Level::AD => FacebookAdInsight::count(),
            Level::CAMPAIGN => FacebookCampaignInsight::count(),
        };

        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    public static function levelProvider(): array
    {
        return [
            [Level::AD],
            [Level::CAMPAIGN],
        ];
    }

    protected function getUrl(FacebookAdAccount $facebookAdAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights*', $facebookAdAccount->external_id);
    }
}
