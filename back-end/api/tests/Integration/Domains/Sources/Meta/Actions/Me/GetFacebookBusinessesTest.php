<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Me;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Actions\Me\GetFacebookBusinesses;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetFacebookBusinessesTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $facebookAccount = FacebookAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get businesses for Facebook Account #%s. Could not retrieve token.',
            $facebookAccount->id
        ));

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $facebookAccount = FacebookAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get businesses for Facebook Account #%s. Received HTTP error with status code %s. Response body:\n\n',
            $facebookAccount->id,
            500,
        ));

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);
    }

    public function test_updates_existing_facebook_business(): void
    {
        Http::preventStrayRequests();

        $facebookAccount = FacebookAccount::factory()->create();

        /** @var FacebookBusiness $facebookBusiness */
        $facebookBusiness = FacebookBusiness::factory()->for($facebookAccount)->create([
            'name' => 'old_name',
        ]);

        Http::fake([
            'https://graph.facebook.com/v23.0/me/businesses*' => Http::response([
                'data' => [
                    [
                        'id' => $facebookBusiness->external_id,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                    'next' => 'https://next.link',
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookBusiness::count();

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);

        $afterDatabaseCount = FacebookBusiness::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $facebookBusiness->refresh();
        $this->assertSame('new_name', $facebookBusiness->name);
    }

    public function test_creates_new_facebook_businesses(): void
    {
        Http::preventStrayRequests();

        $facebookAccount = FacebookAccount::factory()->create();

        Http::fake([
            'https://graph.facebook.com/v23.0/me/businesses*' => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'facebook_businesses',
            [
                'facebook_account_id' => $facebookAccount->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);

        $this->assertDatabaseHas(
            'facebook_businesses',
            [
                'facebook_account_id' => $facebookAccount->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );
    }

    public function test_returns_next_page_token_if_needed(): void
    {
        Http::preventStrayRequests();

        $facebookAccount = FacebookAccount::factory()->create();

        Http::fake([
            'https://graph.facebook.com/v23.0/me/businesses*' => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $result = app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);
        self::assertNull($result);

        $result = app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $facebookAccount = FacebookAccount::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('token', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('50', Arr::get($queryParams, 'limit'));

            return true;
        });
    }

    public function test_assert_correct_body_set_with_cursor(): void
    {
        $facebookAccount = FacebookAccount::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $facebookAccount = FacebookAccount::factory()->create();

        Http::fake([
            'https://graph.facebook.com/v23.0/me/businesses*' => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookBusiness::count();

        app(GetFacebookBusinesses::class)
            ->execute($facebookAccount);

        $afterDatabaseCount = FacebookBusiness::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }
}
