<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Authentication;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class RefreshFacebookAccountTokenTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @throws ConnectionException
     */
    public function test_it_doesnt_refresh_token_if_it_is_recent_enough(): void
    {
        Http::preventStrayRequests();
        Http::fake();

        $facebookAccount = FacebookAccount::factory()
            ->create([
                'token_created_at' => now()->subMonth()->addMinute(),
            ]);

        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);
        $this->assertEquals($facebookAccount->token, $token);

        Http::assertNothingSent();
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_it_is_not_recent_enough(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://graph.facebook.com/v23.0/oauth/access_token' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => now()->subMonth()->subMinute(),
            ]);

        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_missing_token(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://graph.facebook.com/v23.0/oauth/access_token' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create([
                'token' => null,
                'token_created_at' => now()->subMinutes(56),
            ]);

        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_does_refresh_token_if_missing_token_created_at(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://graph.facebook.com/v23.0/oauth/access_token' => Http::response([
                'access_token' => 'new_token',
            ]),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => null,
            ]);

        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);
        $this->assertEquals('new_token', $token);
    }

    /**
     * @throws ConnectionException
     */
    public function test_it_returns_null_if_request_fails(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://graph.facebook.com/v23.0/oauth/access_token' => Http::response(null, 500),
        ]);

        $facebookAccount = FacebookAccount::factory()
            ->create([
                'token' => 'old_token',
                'token_created_at' => now()->subMonth()->subMinute(),
            ]);

        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);
        $this->assertEquals('old_token', $token);
    }
}
