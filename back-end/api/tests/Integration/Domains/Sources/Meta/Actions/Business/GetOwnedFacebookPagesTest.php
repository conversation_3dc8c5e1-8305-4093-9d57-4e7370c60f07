<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetOwnedFacebookPagesTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $facebookBusiness = FacebookBusiness::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get pages for Facebook Business #%s. Could not retrieve token.',
            $facebookBusiness->id
        ));

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $facebookBusiness = FacebookBusiness::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get pages for Facebook Business #%s. Received HTTP error with status code %s. Response body:\n\n',
            $facebookBusiness->id,
            500,
        ));

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);
    }

    public function test_updates_existing_facebook_business(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookPage $facebookPage */
        $facebookPage = FacebookPage::factory()->for($facebookBusiness)->create([
            'name' => 'old_name',
        ]);

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => $facebookPage->external_id,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                    'next' => 'https://next.link',
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookPage::count();

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $afterDatabaseCount = FacebookPage::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $facebookPage->refresh();
        $this->assertSame('new_name', $facebookPage->name);
    }

    public function test_creates_new_facebook_businesses(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'facebook_pages',
            [
                'facebook_business_id' => $facebookBusiness->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $this->assertDatabaseHas(
            'facebook_pages',
            [
                'facebook_business_id' => $facebookBusiness->id,
                'name' => 'new_name',
                'external_id' => ********,
            ]
        );
    }

    public function test_it_creates_instagram_account(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookPage $facebookPage */
        $facebookPage = FacebookPage::factory()
            ->for($facebookBusiness)->create([
                'external_id' => ********,
            ]);

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                        'instagram_business_account' => [
                            'followers_count' => 210,
                            'name' => 'Test account',
                            'username' => 'test_account',
                            'id' => '*********',
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'instagram_accounts',
            [
                'facebook_page_id' => $facebookPage->id,
                'username' => 'test_account',
                'external_id' => *********,
            ]
        );

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $this->assertDatabaseHas(
            'instagram_accounts',
            [
                'facebook_page_id' => $facebookPage->id,
                'username' => 'test_account',
                'external_id' => *********,
            ]
        );
    }

    public function test_it_deletes_instagram_account(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookPage $facebookPage */
        $facebookPage = FacebookPage::factory()
            ->for($facebookBusiness)->create([
                'external_id' => ********,
            ]);

        InstagramAccount::factory()->for($facebookPage)->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseHas(
            'instagram_accounts',
            [
                'facebook_page_id' => $facebookPage->id,
                'deleted_at' => null,
            ]
        );

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $this->assertDatabaseMissing(
            'instagram_accounts',
            [
                'facebook_page_id' => $facebookPage->id,
                'deleted_at' => null,
            ]
        );
    }

    public function test_it_creates_instagram_account_insight(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookPage $facebookPage */
        $facebookPage = FacebookPage::factory()
            ->for($facebookBusiness)->create([
                'external_id' => ********,
            ]);

        $instagramAccount = InstagramAccount::factory()->for($facebookPage)->create();

        InstagramAccountInsight::factory()->for($instagramAccount)->create([
            'date' => now()->subDay()->startOfDay(),
            'followers' => 50,
        ]);

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => ********,
                        'name' => 'new_name',
                        'instagram_business_account' => [
                            'followers_count' => 60,
                            'name' => 'Test account',
                            'username' => 'test_account',
                            'id' => $instagramAccount->external_id,
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'instagram_account_insights',
            [
                'instagram_account_id' => $instagramAccount->id,
                'date' => now()->startOfDay()->format('Y-m-d'),
            ]
        );

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $this->assertDatabaseHas(
            'instagram_account_insights',
            [
                'instagram_account_id' => $instagramAccount->id,
                'date' => now()->startOfDay()->format('Y-m-d'),
                'followers' => 60,
            ]
        );
    }

    public function test_creates_sets_access_token_if_present(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        /** @var FacebookPage $facebookPage */
        $facebookPage = FacebookPage::factory()->for($facebookBusiness)->create([
            'name' => 'old_name',
        ]);

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [
                    [
                        'id' => $facebookPage->external_id,
                        'name' => 'new_name',
                        'access_token' => 'secret',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $facebookPage->refresh();
        $this->assertSame('secret', $facebookPage->token);
        $this->assertNotNull($facebookPage->token_created_at);
    }

    public function test_returns_next_page_token_if_needed(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $result = app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);
        self::assertNull($result);

        $result = app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('token', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('50', Arr::get($queryParams, 'limit'));
            $this->assertEquals('access_token,name,instagram_business_account{followers_count,name,username}', Arr::get($queryParams, 'fields'));

            return true;
        });
    }

    public function test_assert_correct_body_set_with_cursor(): void
    {
        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake();

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $facebookBusiness = FacebookBusiness::factory()->create();

        Http::fake([
            $this->getUrl($facebookBusiness) => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshFacebookAccountToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = FacebookPage::count();

        app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness);

        $afterDatabaseCount = FacebookPage::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    protected function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/owned_pages*', $facebookBusiness->external_id);
    }
}
