<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItemInsights;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use App\Domains\Sources\Meta\Support\Enums\Instagram\MediaType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetInstagramMediaItemInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_does_nothing_if_token_missing(): void
    {
        Http::fake();

        $facebookPage = FacebookPage::factory()->create([
            'token' => null,
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->for($facebookPage)
            ->create();

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, []);

        Http::assertNothingSent();
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get media item insights for Instagram Account #%s. Received HTTP error with status code %s. Response body:\n\n',
            $instagramAccount->id,
            500,
        ));

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, []);
    }

    public function test_creates_new_instagram_media_item_insights(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $videoMediaItem */
        $videoMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 123,
                'media_type' => MediaType::VIDEO,
            ]);

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":66}],"title":"Bereikte accounts","description":"","id":"123\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":8}],"title":"Interacties met reels","description":"","id":"123\/insights\/total_interactions\/lifetime"},{"name":"plays","period":"lifetime","values":[{"value":79}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"123\/insights\/plays\/lifetime"}]}',
                ],
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":72}],"title":"Bereikte accounts","description":"","id":"321\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":2}],"title":"Interacties met reels","description":"","id":"321\/insights\/total_interactions\/lifetime"},{"name":"impressions","period":"lifetime","values":[{"value":87}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"321\/insights\/plays\/lifetime"}]}',
                ],
            ]),
        ]);

        $this->assertDatabaseMissing(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $videoMediaItem->id,
            ]
        );

        $this->assertDatabaseMissing(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $imageMediaItem->id,
            ]
        );

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$videoMediaItem, $imageMediaItem]);

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $videoMediaItem->id,
                'reach' => 66,
                'total_interactions' => 8,
                'impressions' => 79,
            ]
        );

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $imageMediaItem->id,
                'reach' => 72,
                'total_interactions' => 2,
                'impressions' => 87,
            ]
        );
    }

    public function test_it_accounts_for_previous_totals(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $videoMediaItem */
        $videoMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 123,
                'media_type' => MediaType::VIDEO,
            ]);

        InstagramMediaItemInsight::factory()
            ->for($videoMediaItem)
            ->create([
                'date' => now()->subDay()->startOfDay()->format('Y-m-d'),
                'reach' => 65,
                'total_interactions' => 7,
                'impressions' => 78,
            ]);

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        InstagramMediaItemInsight::factory()
            ->for($imageMediaItem)
            ->create([
                'date' => now()->subDay()->startOfDay()->format('Y-m-d'),
                'reach' => 71,
                'total_interactions' => 1,
                'impressions' => 86,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":66}],"title":"Bereikte accounts","description":"","id":"123\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":8}],"title":"Interacties met reels","description":"","id":"123\/insights\/total_interactions\/lifetime"},{"name":"plays","period":"lifetime","values":[{"value":79}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"123\/insights\/plays\/lifetime"}]}',
                ],
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":72}],"title":"Bereikte accounts","description":"","id":"321\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":2}],"title":"Interacties met reels","description":"","id":"321\/insights\/total_interactions\/lifetime"},{"name":"impressions","period":"lifetime","values":[{"value":87}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"321\/insights\/plays\/lifetime"}]}',
                ],
            ]),
        ]);

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$videoMediaItem, $imageMediaItem]);

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $videoMediaItem->id,
                'reach' => 1,
                'total_interactions' => 1,
                'impressions' => 1,
            ]
        );

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $imageMediaItem->id,
                'reach' => 1,
                'total_interactions' => 1,
                'impressions' => 1,
            ]
        );
    }

    public function test_updates_instagram_media_item_insights(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $videoMediaItem */
        $videoMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 123,
                'media_type' => MediaType::VIDEO,
            ]);

        InstagramMediaItemInsight::factory()
            ->for($videoMediaItem)
            ->create([
                'date' => now()->startOfDay()->format('Y-m-d'),
                'reach' => 65,
                'total_interactions' => 7,
                'impressions' => 78,
            ]);

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        InstagramMediaItemInsight::factory()
            ->for($imageMediaItem)
            ->create([
                'date' => now()->startOfDay()->format('Y-m-d'),
                'reach' => 71,
                'total_interactions' => 1,
                'impressions' => 86,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":66}],"title":"Bereikte accounts","description":"","id":"123\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":8}],"title":"Interacties met reels","description":"","id":"123\/insights\/total_interactions\/lifetime"},{"name":"plays","period":"lifetime","values":[{"value":79}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"123\/insights\/plays\/lifetime"}]}',
                ],
                [
                    'code' => 200,
                    'headers' => [],
                    'body' => '{"data":[{"name":"reach","period":"lifetime","values":[{"value":72}],"title":"Bereikte accounts","description":"","id":"321\/insights\/reach\/lifetime"},{"name":"total_interactions","period":"lifetime","values":[{"value":2}],"title":"Interacties met reels","description":"","id":"321\/insights\/total_interactions\/lifetime"},{"name":"impressions","period":"lifetime","values":[{"value":87}],"title":"Eerste afspeelgebeurtenissen","description":"","id":"321\/insights\/plays\/lifetime"}]}',
                ],
            ]),
        ]);

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$videoMediaItem, $imageMediaItem]);

        self::assertSame(2, InstagramMediaItemInsight::count());

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $videoMediaItem->id,
                'reach' => 66,
                'total_interactions' => 8,
                'impressions' => 79,
            ]
        );

        $this->assertDatabaseHas(
            'instagram_media_item_insights',
            [
                'instagram_media_item_id' => $imageMediaItem->id,
                'reach' => 72,
                'total_interactions' => 2,
                'impressions' => 87,
            ]
        );
    }

    public function test_assert_correct_body_set(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        Http::fake();

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$imageMediaItem]);

        Http::assertSent(function (Request $request) {
            $this->assertEquals('secret', Arr::get($request->data(), 'access_token'));
            $this->assertIsArray(Arr::get($request->data(), 'batch'));

            $batchItem = Arr::get($request->data(), 'batch')[0];

            $this->assertEquals('GET', Arr::get($batchItem, 'method'));
            $this->assertEquals('321/insights?metric=reach%2Ctotal_interactions%2Cimpressions', Arr::get($batchItem, 'relative_url'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([]),
        ]);

        $initialDatabaseCount = InstagramMediaItemInsight::count();

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$imageMediaItem]);

        $afterDatabaseCount = InstagramMediaItemInsight::count();

        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    public function test_does_not_throw_exceptions_if_code_error(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([
                [
                    'code' => 403,
                    'body' => '{"data":[]}',
                ],
            ]),
        ]);

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$imageMediaItem]);

        Http::assertSentCount(1);
    }

    public function test_does_not_throw_exceptions_if_empty_data(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $imageMediaItem */
        $imageMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => 321,
                'media_type' => MediaType::IMAGE,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([
                [
                    'code' => 200,
                    'body' => '{"data":[]}',
                ],
            ]),
        ]);

        app(GetInstagramMediaItemInsights::class)
            ->execute($instagramAccount, [$imageMediaItem]);

        Http::assertSentCount(1);
    }

    protected function getUrl(): string
    {
        return sprintf('https://graph.facebook.com/v23.0/');
    }
}
