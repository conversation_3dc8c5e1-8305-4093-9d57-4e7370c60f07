<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Actions\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItems;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetInstagramMediaItemsTest extends TestCase
{
    use RefreshDatabase;

    public function test_does_nothing_if_token_missing(): void
    {
        Http::fake();

        $facebookPage = FacebookPage::factory()->create([
            'token' => null,
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->for($facebookPage)
            ->create();

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);

        Http::assertNothingSent();
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get media items for Instagram Account #%s. Received HTTP error with status code %s. Response body:\n\n',
            $instagramAccount->id,
            500,
        ));

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);
    }

    public function test_creates_new_instagram_media_item(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [
                    [
                        'id' => '123',
                        'media_type' => 'VIDEO',
                        'media_url' => 'https://example.com/image.jpg',
                        'timestamp' => '2024-07-30T12:48:07+0000',
                        'thumbnail_url' => 'https://example.com/thumbnail.jpg',
                    ],
                    [
                        'id' => '321',
                        'media_type' => 'VIDEO',
                        'media_url' => 'https://example.com/image.jpg',
                        'timestamp' => '2024-07-30T12:48:07+0000',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $this->assertDatabaseMissing(
            'instagram_media_items',
            [
                'instagram_account_id' => $instagramAccount->id,
                'external_id' => 123,
            ]
        );

        $this->assertDatabaseMissing(
            'instagram_media_items',
            [
                'instagram_account_id' => $instagramAccount->id,
                'external_id' => 321,
            ]
        );

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);

        $this->assertDatabaseHas(
            'instagram_media_items',
            [
                'instagram_account_id' => $instagramAccount->id,
                'external_id' => 123,
                'media_url' => 'https://example.com/thumbnail.jpg',
            ]
        );

        $this->assertDatabaseHas(
            'instagram_media_items',
            [
                'instagram_account_id' => $instagramAccount->id,
                'external_id' => 321,
                'media_url' => 'https://example.com/image.jpg',
            ]
        );
    }

    public function test_update_instagram_media_items(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        /** @var InstagramMediaItem $instagramMediaItem */
        $instagramMediaItem = InstagramMediaItem::factory()
            ->for($instagramAccount)
            ->create([
                'external_id' => '321',
                'media_url' => 'https://example.com/different.jpg',
            ]);

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [
                    [
                        'id' => '321',
                        'media_type' => 'VIDEO',
                        'media_url' => 'https://example.com/image.jpg',
                        'timestamp' => '2024-07-30T12:48:07+0000',
                    ],
                ],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);

        $instagramMediaItem->refresh();

        self::assertSame('https://example.com/image.jpg', $instagramMediaItem->media_url);
    }

    public function test_returns_next_page_token_if_needed(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::sequence([
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                    ],
                ]),
                Http::response([
                    'data' => [],
                    'paging' => [
                        'cursors' => [
                            'before' => 'before_cursor',
                            'after' => 'after_cursor',
                        ],
                        'next' => 'https://next.link',
                    ],
                ]),
            ]),
        ]);

        $result = app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);
        self::assertNull($result);

        $result = app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);
        self::assertNotNull($result);
        self::assertEquals('after_cursor', $result);
    }

    public function test_assert_correct_body_set_without_cursor(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake();

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('secret', Arr::get($queryParams, 'access_token'));
            $this->assertEquals('id,media_type,media_url,timestamp,thumbnail_url', Arr::get($queryParams, 'fields'));

            return true;
        });
    }

    public function test_assert_correct_body_set_with_cursor(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake();

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount, 'cursor_value');

        Http::assertSent(function (Request $request) {
            $parsedUrl = parse_url($request->url());
            parse_str($parsedUrl['query'], $queryParams);

            $this->assertEquals('cursor_value', Arr::get($queryParams, 'after'));

            return true;
        });
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        Http::preventStrayRequests();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        Http::fake([
            $this->getUrl($instagramAccount) => Http::response([
                'data' => [],
                'paging' => [
                    'cursors' => [
                        'before' => 'before_cursor',
                        'after' => 'after_cursor',
                    ],
                ],
            ]),
        ]);

        $initialDatabaseCount = InstagramMediaItem::count();

        app(GetInstagramMediaItems::class)
            ->execute($instagramAccount);

        $afterDatabaseCount = InstagramMediaItem::count();

        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }

    protected function getUrl(InstagramAccount $instagramAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/media*', $instagramAccount->external_id);
    }
}
