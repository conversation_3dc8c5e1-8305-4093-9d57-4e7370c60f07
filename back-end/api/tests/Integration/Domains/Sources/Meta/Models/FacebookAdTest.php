<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookAdInsight;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookAdTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_campaign(): void
    {
        $facebookAd = FacebookAd::factory()
            ->for(FacebookCampaign::factory())
            ->create();

        $this->assertInstanceOf(FacebookCampaign::class, $facebookAd->facebookCampaign);
    }

    public function test_it_belongs_to_facebook_ad_account(): void
    {
        $facebookAd = FacebookAd::factory()
            ->for(FacebookAdAccount::factory())
            ->create();

        $this->assertInstanceOf(FacebookAdAccount::class, $facebookAd->facebookAdAccount);
    }

    public function test_it_has_many_facebook_ad_insights(): void
    {
        $facebookAd = FacebookAd::factory()
            ->has(FacebookAdInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAd->facebookAdInsights);
        $this->assertInstanceOf(FacebookAdInsight::class, $facebookAd->facebookAdInsights->first());
    }
}
