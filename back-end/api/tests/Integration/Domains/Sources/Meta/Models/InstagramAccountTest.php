<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InstagramAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_page(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->for(FacebookPage::factory())
            ->create();

        $this->assertInstanceOf(FacebookPage::class, $instagramAccount->facebookPage);
    }

    public function test_it_has_many_instagram_account_insights(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->has(InstagramAccountInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $instagramAccount->instagramAccountInsights);
        $this->assertInstanceOf(InstagramAccountInsight::class, $instagramAccount->instagramAccountInsights->first());
    }

    public function test_it_has_many_instagram_media_items(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->has(InstagramMediaItem::factory()->count(3))
            ->create();

        $this->assertCount(3, $instagramAccount->instagramMediaItems);
        $this->assertInstanceOf(InstagramMediaItem::class, $instagramAccount->instagramMediaItems->first());
    }

    public function test_it_has_data_source(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $instagramAccount->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(InstagramAccount::class, $dataSource->sourceable);
    }
}
