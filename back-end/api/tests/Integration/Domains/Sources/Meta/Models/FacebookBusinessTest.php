<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookBusinessTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_account(): void
    {
        $facebookBusiness = FacebookBusiness::factory()
            ->for(FacebookAccount::factory())
            ->create();

        $this->assertInstanceOf(FacebookAccount::class, $facebookBusiness->facebookAccount);
    }

    public function test_it_has_many_facebook_pages(): void
    {
        $facebookBusiness = FacebookBusiness::factory()
            ->has(FacebookPage::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookBusiness->facebookPages);
        $this->assertInstanceOf(FacebookPage::class, $facebookBusiness->facebookPages->first());
    }

    public function test_it_has_many_facebook_ad_accounts(): void
    {
        $facebookBusiness = FacebookBusiness::factory()
            ->has(FacebookAdAccount::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookBusiness->facebookAdAccounts);
        $this->assertInstanceOf(FacebookAdAccount::class, $facebookBusiness->facebookAdAccounts->first());
    }
}
