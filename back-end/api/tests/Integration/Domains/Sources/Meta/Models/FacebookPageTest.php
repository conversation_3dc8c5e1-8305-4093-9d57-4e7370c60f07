<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookPageTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_account(): void
    {
        $facebookPage = FacebookPage::factory()
            ->for(FacebookAccount::factory())
            ->create();

        $this->assertInstanceOf(FacebookAccount::class, $facebookPage->facebookAccount);
    }

    public function test_it_belongs_to_facebook_business(): void
    {
        $facebookPage = FacebookPage::factory()
            ->for(FacebookBusiness::factory())
            ->create();

        $this->assertInstanceOf(FacebookBusiness::class, $facebookPage->facebookBusiness);
    }

    public function test_it_has_many_facebook_page_insights(): void
    {
        $facebookPage = FacebookPage::factory()
            ->has(FacebookPageInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookPage->facebookPageInsights);
        $this->assertInstanceOf(FacebookPageInsight::class, $facebookPage->facebookPageInsights->first());
    }

    public function test_it_has_one_instagram_account(): void
    {
        $facebookPage = FacebookPage::factory()
            ->has(InstagramAccount::factory())
            ->create();

        $this->assertInstanceOf(InstagramAccount::class, $facebookPage->instagramAccount);
    }

    public function test_it_has_data_source(): void
    {
        $facebookPage = FacebookPage::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $facebookPage->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(FacebookPage::class, $dataSource->sourceable);
    }
}
