<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookPageInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_page(): void
    {
        $facebookPageInsight = FacebookPageInsight::factory()
            ->for(FacebookPage::factory())
            ->create();

        $this->assertInstanceOf(FacebookPage::class, $facebookPageInsight->facebookPage);
    }

    public function test_it_has_data_source(): void
    {
        $facebookPageInsight = FacebookPageInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $facebookPageInsight->facebook_page_id,
                'sourceable_type' => FacebookPage::class,
            ]);

        $dataSource = $facebookPageInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(FacebookPage::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $facebookPageInsight = FacebookPageInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $facebookPageInsight->facebook_page_id,
                'sourceable_type' => FacebookPage::class,
            ]);

        self::assertContains($facebookPageInsight->id, FacebookPageInsight::query()->pluck('id'));
        self::assertContains($facebookPageInsight->id, FacebookPageInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
