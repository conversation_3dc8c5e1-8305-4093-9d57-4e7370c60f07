<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InstagramMediaItemTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_instagram_account(): void
    {
        $instagramMediaItem = InstagramMediaItem::factory()
            ->for(InstagramAccount::factory())
            ->create();

        $this->assertInstanceOf(InstagramAccount::class, $instagramMediaItem->instagramAccount);
    }

    public function test_it_has_many_instagram_media_item_insights(): void
    {
        $instagramMediaItem = InstagramMediaItem::factory()
            ->has(InstagramMediaItemInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $instagramMediaItem->instagramMediaItemInsights);
        $this->assertInstanceOf(InstagramMediaItemInsight::class, $instagramMediaItem->instagramMediaItemInsights->first());
    }
}
