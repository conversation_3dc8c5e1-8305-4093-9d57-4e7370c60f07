<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookAdAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_business(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->for(FacebookBusiness::factory())
            ->create();

        $this->assertInstanceOf(FacebookBusiness::class, $facebookAdAccount->facebookBusiness);
    }

    public function test_it_belongs_to_facebook_accounts(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->for(FacebookAccount::factory())
            ->create();

        $this->assertInstanceOf(FacebookAccount::class, $facebookAdAccount->facebookAccount);
    }

    public function test_it_has_many_facebook_campaigns(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookCampaign::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAdAccount->facebookCampaigns);
        $this->assertInstanceOf(FacebookCampaign::class, $facebookAdAccount->facebookCampaigns->first());
    }

    public function test_it_has_many_facebook_ads(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(FacebookAd::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAdAccount->facebookAds);
        $this->assertInstanceOf(FacebookAd::class, $facebookAdAccount->facebookAds->first());
    }

    public function test_it_has_data_source(): void
    {
        $facebookAdAccount = FacebookAdAccount::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $facebookAdAccount->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(FacebookAdAccount::class, $dataSource->sourceable);
    }
}
