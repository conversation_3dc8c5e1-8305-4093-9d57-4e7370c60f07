<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookCampaignTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_ad_account(): void
    {
        $facebookCampaign = FacebookCampaign::factory()
            ->for(FacebookAdAccount::factory())
            ->create();

        $this->assertInstanceOf(FacebookAdAccount::class, $facebookCampaign->facebookAdAccount);
    }

    public function test_it_has_many_facebook_ads(): void
    {
        $facebookCampaign = FacebookCampaign::factory()
            ->has(FacebookAd::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookCampaign->facebookAds);
        $this->assertInstanceOf(FacebookAd::class, $facebookCampaign->facebookAds->first());
    }

    public function test_it_has_many_facebook_ad_insights(): void
    {
        $facebookCampaign = FacebookCampaign::factory()
            ->has(FacebookCampaignInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookCampaign->facebookCampaignInsights);
        $this->assertInstanceOf(FacebookCampaignInsight::class, $facebookCampaign->facebookCampaignInsights->first());
    }
}
