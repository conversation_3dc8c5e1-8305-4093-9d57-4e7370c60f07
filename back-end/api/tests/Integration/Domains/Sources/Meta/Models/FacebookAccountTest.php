<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_facebook_businesses(): void
    {
        $facebookAccount = FacebookAccount::factory()
            ->has(FacebookBusiness::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAccount->facebookBusinesses);
        $this->assertInstanceOf(FacebookBusiness::class, $facebookAccount->facebookBusinesses->first());
    }

    public function test_it_has_many_facebook_pages(): void
    {
        $facebookAccount = FacebookAccount::factory()
            ->has(FacebookPage::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAccount->facebookPages);
        $this->assertInstanceOf(FacebookPage::class, $facebookAccount->facebookPages->first());
    }

    public function test_it_has_many_facebook_ad_accounts(): void
    {
        $facebookAccount = FacebookAccount::factory()
            ->has(FacebookAdAccount::factory()->count(3))
            ->create();

        $this->assertCount(3, $facebookAccount->facebookAdAccounts);
        $this->assertInstanceOf(FacebookAdAccount::class, $facebookAccount->facebookAdAccounts->first());
    }
}
