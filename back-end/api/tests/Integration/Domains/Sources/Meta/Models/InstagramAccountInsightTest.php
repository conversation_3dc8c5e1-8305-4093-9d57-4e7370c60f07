<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InstagramAccountInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_instagram_account(): void
    {
        $instagramAccountInsight = InstagramAccountInsight::factory()
            ->for(InstagramAccount::factory())
            ->create();

        $this->assertInstanceOf(InstagramAccount::class, $instagramAccountInsight->instagramAccount);
    }

    public function test_it_has_data_source(): void
    {
        $instagramAccountInsight = InstagramAccountInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $instagramAccountInsight->instagram_account_id,
                'sourceable_type' => InstagramAccount::class,
            ]);

        $dataSource = $instagramAccountInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(InstagramAccount::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $instagramAccountInsight = InstagramAccountInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $instagramAccountInsight->instagram_account_id,
                'sourceable_type' => InstagramAccount::class,
            ]);

        self::assertContains($instagramAccountInsight->id, InstagramAccountInsight::query()->pluck('id'));
        self::assertContains($instagramAccountInsight->id, InstagramAccountInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
