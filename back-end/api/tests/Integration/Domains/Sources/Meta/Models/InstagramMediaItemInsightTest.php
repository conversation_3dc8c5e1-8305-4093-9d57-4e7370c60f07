<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InstagramMediaItemInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_instagram_media_item(): void
    {
        $instagramMediaItemInsight = InstagramMediaItemInsight::factory()
            ->for(InstagramMediaItem::factory())
            ->create();

        $this->assertInstanceOf(InstagramMediaItem::class, $instagramMediaItemInsight->instagramMediaItem);
    }

    public function test_it_has_data_source(): void
    {
        $instagramMediaItemInsight = InstagramMediaItemInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $instagramMediaItemInsight->instagramMediaItem->instagram_account_id,
                'sourceable_type' => InstagramAccount::class,
            ]);

        $dataSource = $instagramMediaItemInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(InstagramAccount::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $instagramMediaItemInsight = InstagramMediaItemInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $instagramMediaItemInsight->instagramMediaItem->instagram_account_id,
                'sourceable_type' => InstagramAccount::class,
            ]);

        self::assertContains($instagramMediaItemInsight->id, InstagramMediaItemInsight::query()->pluck('id'));
        self::assertContains($instagramMediaItemInsight->id, InstagramMediaItemInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
