<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookCampaignInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_campaign(): void
    {
        $facebookCampaignInsight = FacebookCampaignInsight::factory()
            ->for(FacebookCampaign::factory())
            ->create();

        $this->assertInstanceOf(FacebookCampaign::class, $facebookCampaignInsight->facebookCampaign);
    }

    public function test_it_has_data_source(): void
    {
        $facebookCampaignInsight = FacebookCampaignInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $facebookCampaignInsight->facebookCampaign->facebook_ad_account_id,
                'sourceable_type' => FacebookAdAccount::class,
            ]);

        $dataSource = $facebookCampaignInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(FacebookAdAccount::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $facebookCampaignInsight = FacebookCampaignInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $facebookCampaignInsight->facebookCampaign->facebook_ad_account_id,
                'sourceable_type' => FacebookAdAccount::class,
            ]);

        self::assertContains($facebookCampaignInsight->id, FacebookCampaignInsight::query()->pluck('id'));
        self::assertContains($facebookCampaignInsight->id, FacebookCampaignInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
