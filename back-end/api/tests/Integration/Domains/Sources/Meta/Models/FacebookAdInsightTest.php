<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FacebookAdInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_facebook_ad(): void
    {
        $facebookAdInsight = FacebookAdInsight::factory()
            ->for(FacebookAd::factory())
            ->create();

        $this->assertInstanceOf(FacebookAd::class, $facebookAdInsight->facebookAd);
    }
}
