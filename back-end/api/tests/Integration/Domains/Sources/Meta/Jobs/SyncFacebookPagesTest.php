<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookPageInsights;
use App\Domains\Sources\Meta\Jobs\SyncFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookPagesTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $facebookPage = FacebookPage::factory()
            ->create();

        $deletedFacebookPage = FacebookPage::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncFacebookPages;
        $job->handle();

        Queue::assertPushed(
            SyncFacebookPageInsights::class,
            fn (SyncFacebookPageInsights $job) => $job->facebookPageId === $facebookPage->id
        );
        Queue::assertNotPushed(
            SyncFacebookPageInsights::class,
            fn (SyncFacebookPageInsights $job) => $job->facebookPageId === $deletedFacebookPage->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncFacebookPages::class, $scheduledEvents);
    }
}
