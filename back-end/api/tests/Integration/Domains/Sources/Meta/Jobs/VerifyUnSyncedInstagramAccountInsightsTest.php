<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedInstagramAccountInsights;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Support\Mails\Business\InstagramAccountInsightsIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedInstagramAccountInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        InstagramAccount::factory()
            ->create([
                'insights_last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedInstagramAccountInsights;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedAccount = InstagramAccount::factory()
            ->create([
                'insights_last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedInstagramAccountInsights;
        $job->handle();

        Mail::assertQueued(
            InstagramAccountInsightsIsUnSyncedMail::class,
            fn (InstagramAccountInsightsIsUnSyncedMail $mail) => $mail->instagramAccountId === $unSyncedAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedInstagramAccountInsights::class, $scheduledEvents);
    }
}
