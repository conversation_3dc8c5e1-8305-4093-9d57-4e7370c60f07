<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncAccessibleFacebookAdAccountsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetAccessibleFacebookAdAccounts::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookBusiness::factory()
            ->create();

        $job = new SyncAccessibleFacebookAdAccounts($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetAccessibleFacebookAdAccounts::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $job = new SyncAccessibleFacebookAdAccounts($facebookBusiness->id);
        $job->handle();

        Queue::assertPushed(
            SyncAccessibleFacebookAdAccounts::class,
            fn (SyncAccessibleFacebookAdAccounts $job) => $job->facebookBusinessId === $facebookBusiness->id && $job->cursor === 'cursor'
        );
    }
}
