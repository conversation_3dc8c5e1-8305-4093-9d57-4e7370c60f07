<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Me\GetFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookBusinesses;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookPages;
use App\Domains\Sources\Meta\Jobs\SyncFacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookAccountTest extends TestCase
{
    public function test_it_dispatches(): void
    {
        Queue::fake();

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $job = new SyncFacebookAccount($facebookAccount->id);
        $job->handle();

        Queue::assertPushed(
            GetFacebookPages::class,
            fn (GetFacebookPages $job) => $job->facebookAccountId === $facebookAccount->id
        );
        Queue::assertPushed(
            GetFacebookAdAccounts::class,
            fn (GetFacebookAdAccounts $job) => $job->facebookAccountId === $facebookAccount->id
        );
        Queue::assertPushed(
            GetFacebookBusinesses::class,
            fn (GetFacebookBusinesses $job) => $job->facebookAccountId === $facebookAccount->id
        );
    }
}
