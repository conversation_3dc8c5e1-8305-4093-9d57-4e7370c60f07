<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookPageInsights;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Support\Mails\Business\FacebookPageInsightsIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedFacebookPageInsightsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        FacebookPage::factory()
            ->create([
                'insights_last_synced_at' => now(),
                'token' => null,
            ]);

        $job = new VerifyUnSyncedFacebookPageInsights;
        $job->handle();

        Mail::assertNothingQueued();

        FacebookPage::factory()
            ->create([
                'insights_last_synced_at' => now(),
                'token' => 'secret',
            ]);

        $job = new VerifyUnSyncedFacebookPageInsights;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedPage = FacebookPage::factory()
            ->create([
                'insights_last_synced_at' => now()->subDay()->subSecond(),
                'token' => 'secret',
            ]);

        $job = new VerifyUnSyncedFacebookPageInsights;
        $job->handle();

        Mail::assertQueued(
            FacebookPageInsightsIsUnSyncedMail::class,
            fn (FacebookPageInsightsIsUnSyncedMail $mail) => $mail->facebookPageId === $unSyncedPage->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedFacebookPageInsights::class, $scheduledEvents);
    }
}
