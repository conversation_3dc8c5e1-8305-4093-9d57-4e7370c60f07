<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItemInsights;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramMediaItemInsights;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Tests\TestCase;

class SyncInstagramMediaItemInsightsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $instagramAccount = InstagramAccount::factory()
            ->has(InstagramMediaItem::factory())
            ->create();

        $this->mock(GetInstagramMediaItemInsights::class)
            ->shouldReceive('execute')
            ->withArgs(function (InstagramAccount $argsInstagramAccount, array $argsInstagramMediaItems) use ($instagramAccount) {
                return $instagramAccount->id === $argsInstagramAccount->id && $argsInstagramMediaItems[0]->id === $instagramAccount->instagramMediaItems()->first()->id;
            })
            ->once();

        $job = new SyncInstagramMediaItemInsights(
            instagramAccountId: $instagramAccount->id,
            instagramMediaItemIds: $instagramAccount->instagramMediaItems()->pluck('id')->all()
        );
        $job->handle();
    }
}
