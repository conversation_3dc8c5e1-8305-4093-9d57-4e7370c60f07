<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Business;

use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncOwnedFacebookPagesTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetOwnedFacebookPages::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookBusiness::factory()
            ->create();

        $job = new SyncOwnedFacebookPages($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetOwnedFacebookPages::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $job = new SyncOwnedFacebookPages($facebookBusiness->id);
        $job->handle();

        Queue::assertPushed(
            SyncOwnedFacebookPages::class,
            fn (SyncOwnedFacebookPages $job) => $job->facebookBusinessId === $facebookBusiness->id && $job->cursor === 'cursor'
        );
    }
}
