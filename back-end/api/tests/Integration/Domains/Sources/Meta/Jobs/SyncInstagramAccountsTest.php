<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncInstagramAccountInsight;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramMediaItems;
use App\Domains\Sources\Meta\Jobs\SyncInstagramAccounts;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncInstagramAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $deletedInstagramAccount = InstagramAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncInstagramAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncInstagramAccountInsight::class,
            fn (SyncInstagramAccountInsight $job) => $job->instagramAccountId === $instagramAccount->id
        );
        Queue::assertNotPushed(
            SyncInstagramAccountInsight::class,
            fn (SyncInstagramAccountInsight $job) => $job->instagramAccountId === $deletedInstagramAccount->id
        );

        Queue::assertPushed(
            SyncInstagramMediaItems::class,
            fn (SyncInstagramMediaItems $job) => $job->instagramAccountId === $instagramAccount->id
        );
        Queue::assertNotPushed(
            SyncInstagramMediaItems::class,
            fn (SyncInstagramMediaItems $job) => $job->instagramAccountId === $deletedInstagramAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncInstagramAccounts::class, $scheduledEvents);
    }
}
