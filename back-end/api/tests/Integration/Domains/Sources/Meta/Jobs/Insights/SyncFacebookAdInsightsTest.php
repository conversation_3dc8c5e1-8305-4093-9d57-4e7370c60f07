<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookAdInsights;
use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookAdInsight;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookAdInsightsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetFacebookAdInsights::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->create();

        $job = new SyncFacebookAdInsight($facebookAdAccount->id, Level::AD);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetFacebookAdInsights::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookAdAccount = FacebookAdAccount::factory()
            ->create();

        $job = new SyncFacebookAdInsight($facebookAdAccount->id, Level::AD);
        $job->handle();

        Queue::assertPushed(
            SyncFacebookAdInsight::class,
            fn (SyncFacebookAdInsight $job) => $job->facebookAdAccountId === $facebookAdAccount->id && $job->level === Level::AD
        );
    }
}
