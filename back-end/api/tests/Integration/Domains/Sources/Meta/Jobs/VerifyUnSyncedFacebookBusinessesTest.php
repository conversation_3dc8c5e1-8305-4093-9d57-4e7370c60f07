<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookBusinesses;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Support\Mails\Me\FacebookBusinessIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedFacebookBusinessesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        FacebookBusiness::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedFacebookBusinesses;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedBusiness = FacebookBusiness::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedFacebookBusinesses;
        $job->handle();

        Mail::assertQueued(
            FacebookBusinessIsUnSyncedMail::class,
            fn (FacebookBusinessIsUnSyncedMail $mail) => $mail->facebookBusinessId === $unSyncedBusiness->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedFacebookBusinesses::class, $scheduledEvents);
    }
}
