<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookPages;
use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncAccessibleFacebookPagesTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetAccessibleFacebookPages::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookBusiness::factory()
            ->create();

        $job = new SyncAccessibleFacebookPages($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetAccessibleFacebookPages::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $job = new SyncAccessibleFacebookPages($facebookBusiness->id);
        $job->handle();

        Queue::assertPushed(
            SyncAccessibleFacebookPages::class,
            fn (SyncAccessibleFacebookPages $job) => $job->facebookBusinessId === $facebookBusiness->id && $job->cursor === 'cursor'
        );
    }
}
