<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Business;

use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncOwnedFacebookAdAccountsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetOwnedFacebookAdAccounts::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookBusiness::factory()
            ->create();

        $job = new SyncOwnedFacebookAdAccounts($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetOwnedFacebookAdAccounts::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $job = new SyncOwnedFacebookAdAccounts($facebookBusiness->id);
        $job->handle();

        Queue::assertPushed(
            SyncOwnedFacebookAdAccounts::class,
            fn (SyncOwnedFacebookAdAccounts $job) => $job->facebookBusinessId === $facebookBusiness->id && $job->cursor === 'cursor'
        );
    }
}
