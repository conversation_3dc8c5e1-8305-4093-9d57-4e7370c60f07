<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookPages;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookPages;
use App\Domains\Sources\Meta\Jobs\SyncFacebookBusinesses;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookBusinessesTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $facebookBusiness = FacebookBusiness::factory()
            ->create();

        $deletedFacebookBusiness = FacebookBusiness::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncFacebookBusinesses;
        $job->handle();

        Queue::assertPushed(
            SyncOwnedFacebookPages::class,
            fn (SyncOwnedFacebookPages $job) => $job->facebookBusinessId === $facebookBusiness->id
        );
        Queue::assertNotPushed(
            SyncOwnedFacebookPages::class,
            fn (SyncOwnedFacebookPages $job) => $job->facebookBusinessId === $deletedFacebookBusiness->id
        );

        Queue::assertPushed(
            SyncAccessibleFacebookPages::class,
            fn (SyncAccessibleFacebookPages $job) => $job->facebookBusinessId === $facebookBusiness->id
        );
        Queue::assertNotPushed(
            SyncAccessibleFacebookPages::class,
            fn (SyncAccessibleFacebookPages $job) => $job->facebookBusinessId === $deletedFacebookBusiness->id
        );

        Queue::assertPushed(
            SyncOwnedFacebookAdAccounts::class,
            fn (SyncOwnedFacebookAdAccounts $job) => $job->facebookBusinessId === $facebookBusiness->id
        );
        Queue::assertNotPushed(
            SyncOwnedFacebookAdAccounts::class,
            fn (SyncOwnedFacebookAdAccounts $job) => $job->facebookBusinessId === $deletedFacebookBusiness->id
        );

        Queue::assertPushed(
            SyncAccessibleFacebookAdAccounts::class,
            fn (SyncAccessibleFacebookAdAccounts $job) => $job->facebookBusinessId === $facebookBusiness->id
        );
        Queue::assertNotPushed(
            SyncAccessibleFacebookAdAccounts::class,
            fn (SyncAccessibleFacebookAdAccounts $job) => $job->facebookBusinessId === $deletedFacebookBusiness->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncFacebookBusinesses::class, $scheduledEvents);
    }
}
