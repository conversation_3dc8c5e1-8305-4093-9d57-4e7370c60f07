<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookAdInsight;
use App\Domains\Sources\Meta\Jobs\SyncFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookAdAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $facebookAdAccount = FacebookAdAccount::factory()
            ->create();

        $deletedFacebookAdAccount = FacebookAdAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncFacebookAdAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncFacebookAdInsight::class,
            fn (SyncFacebookAdInsight $job) => $job->facebookAdAccountId === $facebookAdAccount->id && $job->level === Level::AD
        );
        Queue::assertNotPushed(
            SyncFacebookAdInsight::class,
            fn (SyncFacebookAdInsight $job) => $job->facebookAdAccountId === $deletedFacebookAdAccount->id && $job->level === Level::AD
        );

        Queue::assertPushed(
            SyncFacebookAdInsight::class,
            fn (SyncFacebookAdInsight $job) => $job->facebookAdAccountId === $facebookAdAccount->id && $job->level === Level::CAMPAIGN
        );
        Queue::assertNotPushed(
            SyncFacebookAdInsight::class,
            fn (SyncFacebookAdInsight $job) => $job->facebookAdAccountId === $deletedFacebookAdAccount->id && $job->level === Level::CAMPAIGN
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncFacebookAdAccounts::class, $scheduledEvents);
    }
}
