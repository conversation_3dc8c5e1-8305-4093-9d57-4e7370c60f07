<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookAdAccounts as Action;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class GetFacebookAdAccountsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(Action::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $job = new GetFacebookAdAccounts($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(Action::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $job = new GetFacebookAdAccounts($facebookAccount->id);
        $job->handle();

        Queue::assertPushed(
            GetFacebookAdAccounts::class,
            fn (GetFacebookAdAccounts $job) => $job->facebookAccountId === $facebookAccount->id && $job->cursor === 'cursor'
        );
    }
}
