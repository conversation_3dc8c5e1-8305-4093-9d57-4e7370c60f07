<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Mails\Business\FacebookAdAccountIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedFacebookAdAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        FacebookAdAccount::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedFacebookAdAccounts;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedAdAccount = FacebookAdAccount::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedFacebookAdAccounts;
        $job->handle();

        Mail::assertQueued(
            FacebookAdAccountIsUnSyncedMail::class,
            fn (FacebookAdAccountIsUnSyncedMail $mail) => $mail->facebookAdAccountId === $unSyncedAdAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedFacebookAdAccounts::class, $scheduledEvents);
    }
}
