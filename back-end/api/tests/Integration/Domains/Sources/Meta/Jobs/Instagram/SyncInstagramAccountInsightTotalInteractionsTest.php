<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramAccountInsightTotalInteractions;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Tests\TestCase;

class SyncInstagramAccountInsightTotalInteractionsTest extends TestCase
{
    public function test_it_calculates_values(): void
    {
        $instagramAccountInsightYesterday = InstagramAccountInsight::factory()
            ->for(
                InstagramAccount::factory()
                    ->has(
                        InstagramMediaItem::factory()
                            ->has(InstagramMediaItemInsight::factory())
                            ->count(3)
                    )
            )
            ->create([
                'date' => now()->subDay(),
                'total_interactions' => 0,
            ]);
        $instagramAccountInsightToday = InstagramAccountInsight::factory()
            ->for(
                InstagramAccount::factory()
                    ->has(
                        InstagramMediaItem::factory()
                            ->has(
                                InstagramMediaItemInsight::factory()
                                    ->state([
                                        'date' => now()->startOfDay()->format('Y-m-d'),
                                        'total_interactions' => 1,
                                    ])
                            )
                            ->count(3)
                    )
            )
            ->create([
                'date' => now()->startOfDay(),
                'total_interactions' => 0,
            ]);

        $job = new SyncInstagramAccountInsightTotalInteractions;
        $job->handle();

        $instagramAccountInsightYesterday->refresh();
        self::assertEquals(0, $instagramAccountInsightYesterday->total_interactions);

        $instagramAccountInsightToday->refresh();
        self::assertEquals(3, $instagramAccountInsightToday->total_interactions);
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncInstagramAccountInsightTotalInteractions::class, $scheduledEvents);
    }
}
