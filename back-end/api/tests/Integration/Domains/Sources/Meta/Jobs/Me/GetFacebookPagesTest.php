<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookPages as Action;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class GetFacebookPagesTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(Action::class)
            ->shouldReceive('execute')
            ->once();

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $job = new GetFacebookPages($facebookAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(Action::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $job = new GetFacebookPages($facebookAccount->id);
        $job->handle();

        Queue::assertPushed(
            GetFacebookPages::class,
            fn (GetFacebookPages $job) => $job->facebookAccountId === $facebookAccount->id && $job->cursor === 'cursor'
        );
    }
}
