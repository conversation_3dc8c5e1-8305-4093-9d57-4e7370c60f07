<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItems;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramMediaItemInsights;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramMediaItems;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncInstagramMediaItemsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        Queue::fake();

        $this->mock(GetInstagramMediaItems::class)
            ->shouldReceive('execute')
            ->once();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $job = new SyncInstagramMediaItems($instagramAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetInstagramMediaItems::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $job = new SyncInstagramMediaItems($instagramAccount->id);
        $job->handle();

        Queue::assertPushed(
            SyncInstagramMediaItems::class,
            fn (SyncInstagramMediaItems $job) => $job->instagramAccountId === $instagramAccount->id
        );
    }

    public function test_it_dispatches_sync_media_item_insights_job(): void
    {
        Queue::fake();

        $this->mock(GetInstagramMediaItems::class)
            ->shouldReceive('execute')
            ->once();

        $instagramAccount = InstagramAccount::factory()
            ->has(InstagramMediaItem::factory())
            ->create();

        $job = new SyncInstagramMediaItems($instagramAccount->id);
        $job->handle();

        Queue::assertPushed(
            SyncInstagramMediaItemInsights::class,
            fn (SyncInstagramMediaItemInsights $job) => $job->instagramAccountId === $instagramAccount->id
        );
    }
}
