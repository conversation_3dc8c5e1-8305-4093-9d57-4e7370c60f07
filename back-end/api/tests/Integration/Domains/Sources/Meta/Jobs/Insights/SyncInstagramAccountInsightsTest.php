<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetInstagramAccountInsights;
use App\Domains\Sources\Meta\Jobs\Insights\SyncInstagramAccountInsight;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncInstagramAccountInsightsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetInstagramAccountInsights::class)
            ->shouldReceive('execute')
            ->once();

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $job = new SyncInstagramAccountInsight($instagramAccount->id);
        $job->handle();
    }

    public function test_it_self_dispatches(): void
    {
        Queue::fake();

        $this->mock(GetInstagramAccountInsights::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('cursor');

        $instagramAccount = InstagramAccount::factory()
            ->create();

        $job = new SyncInstagramAccountInsight($instagramAccount->id);
        $job->handle();

        Queue::assertPushed(
            SyncInstagramAccountInsight::class,
            fn (SyncInstagramAccountInsight $job) => $job->instagramAccountId === $instagramAccount->id
        );
    }
}
