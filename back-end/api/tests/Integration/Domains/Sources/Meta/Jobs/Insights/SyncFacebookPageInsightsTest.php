<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookPageInsights;
use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookPageInsights;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Tests\TestCase;

class SyncFacebookPageInsightsTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetFacebookPageInsights::class)
            ->shouldReceive('execute')
            ->once();

        $facebookPage = FacebookPage::factory()
            ->create();

        $job = new SyncFacebookPageInsights($facebookPage->id);
        $job->handle();
    }
}
