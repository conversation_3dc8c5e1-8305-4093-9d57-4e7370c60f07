<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\SyncFacebookAccount;
use App\Domains\Sources\Meta\Jobs\SyncFacebookAccounts;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncFacebookAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $facebookAccount = FacebookAccount::factory()
            ->create();

        $deletedFacebookAccount = FacebookAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncFacebookAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncFacebookAccount::class,
            fn (SyncFacebookAccount $job) => $job->facebookAccountId === $facebookAccount->id
        );
        Queue::assertNotPushed(
            SyncFacebookAccount::class,
            fn (SyncFacebookAccount $job) => $job->facebookAccountId === $deletedFacebookAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncFacebookAccounts::class, $scheduledEvents);
    }
}
