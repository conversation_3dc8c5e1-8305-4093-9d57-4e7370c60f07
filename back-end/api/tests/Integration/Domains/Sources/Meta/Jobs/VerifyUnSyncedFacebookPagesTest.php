<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Support\Mails\Business\FacebookPageIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedFacebookPagesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        FacebookPage::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedFacebookPages;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedPage = FacebookPage::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedFacebookPages;
        $job->handle();

        Mail::assertQueued(
            FacebookPageIsUnSyncedMail::class,
            fn (FacebookPageIsUnSyncedMail $mail) => $mail->facebookPageId === $unSyncedPage->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedFacebookPages::class, $scheduledEvents);
    }
}
