<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Actions\Analytics\ConvertEventsToDataSourceForProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ConvertEventsToDataSourceForPropertyTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_doesnt_throw_exception_for_properties_without_data_source()
    {
        $googleProperty = GoogleAnalyticsProperty::factory()
            ->create();

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $this->assertDatabaseCount('google_analytics_reports', 0);
    }

    public function test_it_doesnt_throw_exception_for_empty_data_source_options()
    {
        $googleProperty = GoogleAnalyticsProperty::factory()
            ->create();

        /** @var DataSource $dataSource */
        $dataSource = DataSource::factory()
            ->for($googleProperty, 'sourceable')
            ->create([
                'options' => [
                    'types' => [ReportType::BOOKING->value => []],
                ],
            ]);

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $dataSource->options = [
            'types' => [],
        ];
        $dataSource->save();

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $dataSource->options = [];
        $dataSource->save();

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $this->assertDatabaseCount('google_analytics_reports', 0);
    }

    public function test_it_doesnt_throw_exception_for_empty_data_source_events()
    {
        $googleProperty = GoogleAnalyticsProperty::factory()
            ->create();

        DataSource::factory()
            ->for($googleProperty, 'sourceable')
            ->create([
                'options' => [
                    'types' => [
                        ReportType::BOOKING->value => [],
                    ],
                ],
            ]);

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $this->assertDatabaseCount('google_analytics_reports', 0);
    }

    public function test_it_maps_based_on_datasource(): void
    {
        $googleProperty = GoogleAnalyticsProperty::factory()
            ->create();

        DataSource::factory()
            ->for($googleProperty, 'sourceable')
            ->create([
                'options' => [
                    'types' => [
                        ReportType::BOOKING->value => ['mapped'],
                    ],
                ],
            ]);

        GoogleAnalyticsReport::factory()
            ->count(5)
            ->for($googleProperty)
            ->asType(ReportType::EVENT)
            ->create([
                'name' => 'not_mapped',
            ]);

        GoogleAnalyticsReport::factory()
            ->count(5)
            ->for($googleProperty)
            ->asType(ReportType::EVENT)
            ->create([
                'name' => 'mapped',
            ]);

        $unMappedEntries = GoogleAnalyticsReport::query()
            ->where('google_analytics_property_id', $googleProperty->id)
            ->where('type', ReportType::EVENT->value)
            ->count();

        $this->assertEquals(10, $unMappedEntries);

        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleProperty);

        $unMappedEntries = GoogleAnalyticsReport::query()
            ->where('google_analytics_property_id', $googleProperty->id)
            ->where('type', ReportType::EVENT->value)
            ->count();

        $mappedEntries = GoogleAnalyticsReport::query()
            ->where('google_analytics_property_id', $googleProperty->id)
            ->where('type', ReportType::BOOKING->value)
            ->count();

        $this->assertDatabaseCount('google_analytics_reports', 10);
        $this->assertEquals(5, $unMappedEntries);
        $this->assertEquals(5, $mappedEntries);
    }
}
