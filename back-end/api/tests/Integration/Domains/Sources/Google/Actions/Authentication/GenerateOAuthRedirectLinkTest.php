<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Sources\Google\Actions\Authentication\GenerateOAuthRedirectLink;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class GenerateOAuthRedirectLinkTest extends TestCase
{
    protected function getExpectedRedirectUrl(): string
    {
        return 'https://example.com/oauth/callback';
    }

    protected function getExpectedOAuthUrl(): string
    {
        $baseUrl = 'https://accounts.google.com/o/oauth2/auth';
        $params = [
            'redirect_uri' => $this->getExpectedRedirectUrl(),
            'scope' => 'openid profile email https://www.googleapis.com/auth/analytics.readonly https://www.googleapis.com/auth/adwords',
            'response_type' => 'code',
            'access_type' => 'offline',
            'prompt' => 'consent select_account',
        ];

        $queryString = http_build_query($params);

        return $baseUrl.'?'.$queryString;
    }

    public function test_it_generates_oauth_redirect_link_with_correct_parameters(): void
    {
        $provider = Mockery::mock('Laravel\Socialite\Two\GoogleProvider');

        $provider->shouldReceive('scopes')
            ->with([
                'https://www.googleapis.com/auth/analytics.readonly',
                'https://www.googleapis.com/auth/adwords',
            ])
            ->andReturnSelf();

        $provider->shouldReceive('with')
            ->with([
                'access_type' => 'offline',
                'prompt' => 'consent select_account',
                'state' => [],
            ])
            ->andReturnSelf();

        $provider->shouldReceive('redirectUrl')
            ->andReturn($this->getExpectedRedirectUrl());

        $provider->shouldReceive('stateless');

        $provider->shouldReceive('redirect->getTargetUrl')
            ->andReturn($this->getExpectedOAuthUrl());

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn($provider);

        $redirectUrl = app(GenerateOAuthRedirectLink::class)
            ->execute($this->getExpectedRedirectUrl());

        $this->assertSame($this->getExpectedOAuthUrl(), $redirectUrl);
    }

    public function test_it_generates_oauth_redirect_link(): void
    {
        $redirectUrl = app(GenerateOAuthRedirectLink::class)
            ->execute($this->getExpectedRedirectUrl());

        $this->assertSame($this->getExpectedOAuthUrl(), $redirectUrl);
    }
}
