<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ListAccessibleAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot list accessible ads accounts. Could not retrieve token for Google Account #%s.',
            $googleAccount->id,
        ));

        app(ListAccessibleAccounts::class)
            ->execute(
                googleAccount: $googleAccount
            );
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers:listAccessibleCustomers' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot list accessible ads accounts. Received HTTP error with status code %s for Google Account #%s. Response body:\n\n',
            500,
            $googleAccount->id,
        ));

        app(ListAccessibleAccounts::class)
            ->execute(
                googleAccount: $googleAccount
            );
    }

    public function test_list_resource_names(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers:listAccessibleCustomers' => Http::response([
                'resourceNames' => [
                    'customers/********',
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $results = app(ListAccessibleAccounts::class)
            ->execute(
                googleAccount: $googleAccount
            );

        self::assertIsArray($results);
        self::assertSame('customers/********', $results[0]);
    }
}
