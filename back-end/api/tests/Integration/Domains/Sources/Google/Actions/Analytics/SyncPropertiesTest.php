<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\SyncProperties;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Enums\Analytics\IndustryCategory;
use App\Domains\Sources\Google\Support\Enums\Analytics\PropertyType;
use App\Domains\Sources\Google\Support\Enums\Analytics\ServiceLevel;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class SyncPropertiesTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot sync Google Analytics properties. Could not retrieve token for Google Analytics Account #%s.',
            $googleAnalyticsAccount->id
        ));

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);
    }

    public function test_sets_next_page_token_in_request_if_present(): void
    {
        Http::fake([
            '*' => Http::response([
                'properties' => [],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount, 'next-page-token');

        Http::assertSent(function (Request $request) {
            $hasUrl = Str::contains($request->url(), 'https://analyticsadmin.googleapis.com/v1beta/properties');
            $hasToken = $request['pageToken'] === 'next-page-token';

            return $hasUrl && $hasToken;
        });
    }

    public function test_calls_itself_with_next_page_token_if_in_response(): void
    {
        Http::fake([
            '*' => Http::sequence()
                ->push(['properties' => [], 'nextPageToken' => 'token'])
                ->push(['properties' => []]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);

        Http::assertSentCount(2);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot sync Google Analytics properties. Received HTTP error with status code %s for Google Analytics Account #%s. Response body:\n\n',
            500,
            $googleAnalyticsAccount->id
        ));

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);
    }

    public function test_updates_existing_google_analytics_properties(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        /** @var GoogleAnalyticsProperty $googleAnalyticsProperty */
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->for($googleAnalyticsAccount)
            ->create();

        $previousLastSyncedAt = $googleAnalyticsProperty->last_synced_at;
        Carbon::setTestNow(now()->addDay());

        $this->assertNotSame($previousLastSyncedAt, now());

        Http::fake([
            '*' => Http::response([
                'properties' => [
                    [
                        'name' => $googleAnalyticsProperty->external_id,
                        'displayName' => 'changedName',
                        'industryCategory' => $googleAnalyticsProperty->industry_category->value,
                        'timeZone' => $googleAnalyticsProperty->time_zone,
                        'currencyCode' => $googleAnalyticsProperty->currency_code,
                        'serviceLevel' => $googleAnalyticsProperty->service_level->value,
                        'propertyType' => $googleAnalyticsProperty->property_type->value,
                        'createTime' => $googleAnalyticsProperty->external_created_at,
                        'updateTime' => $googleAnalyticsProperty->external_updated_at,
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = GoogleAnalyticsProperty::count();

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);

        $afterDatabaseCount = GoogleAnalyticsProperty::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $googleAnalyticsProperty->refresh();
        $this->assertSame('changedName', $googleAnalyticsProperty->name);

        $this->assertNotSame($previousLastSyncedAt->toDateTimeString(), $googleAnalyticsProperty->last_synced_at->toDateTimeString());
        $this->assertSame($previousLastSyncedAt->addDay()->toDateTimeString(), $googleAnalyticsProperty->last_synced_at->toDateTimeString());
    }

    public function test_creates_new_existing_google_analytics_accounts(): void
    {
        Http::fake([
            '*' => Http::response([
                'properties' => [
                    $this->getPropertyRequestData(),
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();

        $this->assertDatabaseMissing(
            'google_analytics_properties',
            ['external_id' => 'properties/123']
        );

        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);

        $this->assertDatabaseHas(
            'google_analytics_properties',
            [
                'google_analytics_account_id' => $googleAnalyticsAccount->id,
                'external_id' => 'properties/123',
            ]
        );
    }

    public function test_some_properties_can_be_missing_from_response(): void
    {
        $requestData = $this->getPropertyRequestData();
        unset($requestData['industryCategory']);

        Http::fake([
            '*' => Http::response([
                'properties' => [$requestData],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()->create();
        app(SyncProperties::class)
            ->execute($googleAnalyticsAccount);

        $this->assertDatabaseHas(
            'google_analytics_properties',
            [
                'google_analytics_account_id' => $googleAnalyticsAccount->id,
                'external_id' => 'properties/123',
                'industry_category' => null,
            ]
        );
    }

    protected function getPropertyRequestData(): array
    {
        return [
            'name' => 'properties/123',
            'displayName' => 'changedName',
            'industryCategory' => IndustryCategory::NEWS->value,
            'timeZone' => 'Europe/Amsterdam',
            'currencyCode' => 'USD',
            'serviceLevel' => ServiceLevel::GOOGLE_ANALYTICS_360->value,
            'propertyType' => PropertyType::PROPERTY_TYPE_ORDINARY->value,
            'createTime' => now()->toIso8601ZuluString('millisecond'),
            'updateTime' => now()->toIso8601ZuluString('millisecond'),
        ];
    }
}
