<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Ads\GetCampaignsWithMetrics;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use App\Domains\Sources\Google\Support\Enums\Ads\AdvertisingChannelType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetCampaignsWithMetricsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleAdAccount = GoogleAdAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get campaign metrics. Could not retrieve token for Google Ad Account #%s.',
            $googleAdAccount->id,
        ));

        app(GetCampaignsWithMetrics::class)
            ->execute(
                googleAdAccount: $googleAdAccount,
            );
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAdAccount = GoogleAdAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get campaign metrics. Received HTTP error with status code %s for Google Ad Account #%s. Response body:\n\n',
            500,
            $googleAdAccount->id,
        ));

        app(GetCampaignsWithMetrics::class)
            ->execute(
                googleAdAccount: $googleAdAccount,
            );
    }

    public function test_updates_existing_google_ad_account(): void
    {
        Http::preventStrayRequests();

        $googleAdAccount = GoogleAdAccount::factory()->create();

        /** @var GoogleAdCampaign $googleAdCampaign */
        $googleAdCampaign = GoogleAdCampaign::factory()
            ->for($googleAdAccount)
            ->create([
                'external_id' => ***********,
                'name' => 'Name',
                'advertising_channel_type' => AdvertisingChannelType::SEARCH,
            ]);

        $googleAdCampaignInsight = GoogleAdCampaignInsight::factory()
            ->for($googleAdCampaign)
            ->create([
                'date' => '2024-11-21',
                'clicks' => 5,
            ]);

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response([
                [
                    'results' => [
                        [
                            'campaign' => [
                                'resourceName' => 'customers/**********/campaigns/***********',
                                'advertisingChannelType' => 'DISPLAY',
                                'name' => 'BCS - van Maanen - Maandactie',
                                'id' => '***********',
                            ],
                            'metrics' => [
                                'clicks' => '3',
                                'videoViews' => '0',
                                'ctr' => 0,
                                'averageCpm' => 443803.********,
                                'averageCpc' => 443803.********,
                                'costMicros' => '1248864',
                                'impressions' => '2814',
                            ],
                            'segments' => [
                                'date' => '2024-11-21',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialGoogleAdCampaignCount = GoogleAdCampaign::count();
        $initialGoogleAdCampaignInsightCount = GoogleAdCampaignInsight::count();

        app(GetCampaignsWithMetrics::class)
            ->execute(
                googleAdAccount: $googleAdAccount,
            );

        $afterGoogleAdCampaignCount = GoogleAdCampaign::count();
        $afterGoogleAdCampaignInsightCount = GoogleAdCampaignInsight::count();
        $this->assertSame($initialGoogleAdCampaignCount, $afterGoogleAdCampaignCount);
        $this->assertSame($initialGoogleAdCampaignInsightCount, $afterGoogleAdCampaignInsightCount);

        $googleAdCampaign->refresh();
        $this->assertSame('BCS - van Maanen - Maandactie', $googleAdCampaign->name);

        $googleAdCampaignInsight->refresh();
        $this->assertSame(3, $googleAdCampaignInsight->clicks);
    }

    public function test_creates_new_existing_google_ad_accounts(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response([
                [
                    'results' => [
                        [
                            'campaign' => [
                                'resourceName' => 'customers/**********/campaigns/***********',
                                'advertisingChannelType' => 'DISPLAY',
                                'name' => 'BCS - van Maanen - Maandactie',
                                'id' => '***********',
                            ],
                            'metrics' => [
                                'clicks' => '0',
                                'videoViews' => '0',
                                'ctr' => 0,
                                'averageCpm' => 443803.********,
                                'averageCpc' => 443803.********,
                                'costMicros' => '1248864',
                                'impressions' => '2814',
                            ],
                            'segments' => [
                                'date' => '2024-11-21',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAdAccount = GoogleAdAccount::factory()->create();

        $this->assertDatabaseMissing(
            'google_ad_campaigns',
            ['external_id' => '***********']
        );
        $this->assertDatabaseMissing(
            'google_ad_campaign_insights',
            ['date' => '2024-11-21']
        );

        app(GetCampaignsWithMetrics::class)
            ->execute(
                googleAdAccount: $googleAdAccount,
            );

        $this->assertDatabaseHas(
            'google_ad_campaigns',
            [
                'google_ad_account_id' => $googleAdAccount->id,
                'external_id' => '***********',
            ]
        );
        $this->assertDatabaseHas(
            'google_ad_campaign_insights',
            ['date' => '2024-11-21']
        );
    }
}
