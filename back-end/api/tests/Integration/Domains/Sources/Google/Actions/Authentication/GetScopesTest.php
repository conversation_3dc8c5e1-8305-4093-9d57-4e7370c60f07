<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Sources\Google\Actions\Authentication\GetScopes;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetScopesTest extends TestCase
{
    public function test_it_returns_empty_array_for_null_token(): void
    {
        Http::fake();
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturnNull();

        $googleAccount = GoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($googleAccount);
        $this->assertEmpty($result);

        Http::assertNothingSent();
    }

    public function test_it_returns_scopes_from_response(): void
    {
        Http::fake([
            '*' => Http::response([
                'scope' => 'scope1 scope2',
            ]),
        ]);
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($googleAccount);
        $this->assertContains('scope1', $result);
        $this->assertContains('scope2', $result);

        Http::assertSent(function (Request $request) {
            return $request['access_token'] === 'token';
        });
    }

    public function test_it_returns_empty_array_for_failing_request(): void
    {
        Http::fake([
            '*' => Http::response(null, 500),
        ]);
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()
            ->create([
                'token_created_at' => now(),
            ]);

        $result = app(GetScopes::class)->execute($googleAccount);
        $this->assertEmpty($result);
    }
}
