<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\ConvertLocalDateHourToUTC;
use App\Domains\Sources\Google\Actions\Analytics\GetChannelGroupsForProperty;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetChannelGroupsForPropertyException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetChannelGroupsForPropertyTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleProperty = GoogleAnalyticsProperty::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Could not retrieve token.',
            $googleProperty->id
        ));

        app(GetChannelGroupsForProperty::class)
            ->execute($googleProperty);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleProperty = GoogleAnalyticsProperty::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Received HTTP error with status code %s. Response body:\n\n',
            $googleProperty->id,
            500,
        ));

        app(GetChannelGroupsForProperty::class)
            ->execute($googleProperty);
    }

    /**
     * @throws CannotGetChannelGroupsForPropertyException
     * @throws ConnectionException
     */
    public function test_updates_existing_google_analytics_reports(): void
    {
        Carbon::setTestNow(now());

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()->create();

        /** @var GoogleAnalyticsReport $googleAnalyticsReport */
        $googleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->asType(ReportType::CHANNEL_GROUP)
            ->for($googleAnalyticsProperty)
            ->create([
                'value' => 1,
                'name' => 'pageView',
            ]);

        Http::fake([
            '*' => Http::response([
                'metadata' => [
                    'timeZone' => 'Europe/Amsterdam',
                ],
                'rows' => [
                    [
                        'dimensionValues' => [
                            [
                                'value' => now()
                                    ->timezone('Europe/Amsterdam')
                                    ->format('YmdH'),
                            ],
                            [
                                'value' => 'pageView',
                            ],
                        ],
                        'metricValues' => [
                            [
                                'value' => '2',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->spy(ConvertLocalDateHourToUTC::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($dateHour, $timeZone) {
                return (new ConvertLocalDateHourToUTC)->execute($dateHour, $timeZone);
            });

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = GoogleAnalyticsReport::count();

        app(GetChannelGroupsForProperty::class)
            ->execute($googleAnalyticsProperty);

        $afterDatabaseCount = GoogleAnalyticsReport::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $googleAnalyticsReport->refresh();
        $this->assertSame(2, $googleAnalyticsReport->value);
    }

    public function test_creates_new_google_analytics_reports(): void
    {
        Carbon::setTestNow(now());

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()->create();

        Http::fake([
            '*' => Http::response([
                'metadata' => [
                    'timeZone' => 'Europe/Amsterdam',
                ],
                'rows' => [
                    [
                        'dimensionValues' => [
                            [
                                'value' => now()
                                    ->timezone('Europe/Amsterdam')
                                    ->format('YmdH'),
                            ],
                            [
                                'value' => 'pageView',
                            ],
                        ],
                        'metricValues' => [
                            [
                                'value' => '2',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->assertDatabaseMissing(
            'google_analytics_reports',
            [
                'google_analytics_property_id' => $googleAnalyticsProperty->id,
                'date' => now()->setMinute(0)->setSecond(0),
                'type' => ReportType::CHANNEL_GROUP->value,
                'name' => 'pageView',
            ]
        );

        app(GetChannelGroupsForProperty::class)
            ->execute($googleAnalyticsProperty);

        $this->assertDatabaseHas(
            'google_analytics_reports',
            [
                'google_analytics_property_id' => $googleAnalyticsProperty->id,
                'date' => now()->setMinute(0)->setSecond(0),
                'type' => ReportType::CHANNEL_GROUP->value,
                'name' => 'pageView',
                'value' => 2,
            ]
        );
    }

    public function test_assert_correct_body_set(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()->create();

        Http::fake();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        app(GetChannelGroupsForProperty::class)
            ->execute($googleAnalyticsProperty);

        Http::assertSent(function (Request $request) use ($googleAnalyticsProperty) {
            $postData = json_decode($request->body(), true);

            $this->assertSame('dateHour', Arr::get($postData, 'dimensions.0.name'));
            $this->assertSame('firstUserDefaultChannelGroup', Arr::get($postData, 'dimensions.1.name'));
            $this->assertSame('sessions', Arr::get($postData, 'metrics.0.name'));
            $this->assertSame('7daysAgo', Arr::get($postData, 'dateRanges.0.startDate'));
            $this->assertSame('yesterday', Arr::get($postData, 'dateRanges.0.endDate'));

            $this->assertSame('POST', $request->method());
            $this->assertStringContainsString($googleAnalyticsProperty->external_id, $request->url());

            return true;
        });
    }

    public function test_throws_exception_if_date_cannot_be_converted(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()->create();

        Http::fake([
            '*' => Http::response([
                'metadata' => [
                    'timeZone' => 'Europe/Amsterdam',
                ],
                'rows' => [
                    [
                        'dimensionValues' => [
                            [
                                'value' => 'cannot_parse',
                            ],
                            [
                                'value' => 'pageView',
                            ],
                        ],
                        'metricValues' => [
                            [
                                'value' => '2',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $this->expectExceptionMessage(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Error in converting date hour (%s) and time zone (%s) into UTC. Original message:',
            $googleAnalyticsProperty->id,
            'cannot_parse',
            'Europe/Amsterdam',
        ));

        app(GetChannelGroupsForProperty::class)
            ->execute($googleAnalyticsProperty);
    }

    public function test_does_not_throw_exceptions_if_no_data_present(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()->create();

        Http::fake([
            '*' => Http::response([
                'metadata' => [
                    'timeZone' => 'Europe/Amsterdam',
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = GoogleAnalyticsReport::count();

        app(GetChannelGroupsForProperty::class)
            ->execute($googleAnalyticsProperty);

        $afterDatabaseCount = GoogleAnalyticsReport::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);
    }
}
