<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\SyncAccounts;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class SyncAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot sync Google Analytics accounts. Could not retrieve token for Google Account #%s.',
            $googleAccount->id
        ));

        app(SyncAccounts::class)
            ->execute($googleAccount);
    }

    public function test_sets_next_page_token_in_request_if_present(): void
    {
        Http::fake([
            '*' => Http::response([
                'accounts' => [],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        app(SyncAccounts::class)
            ->execute($googleAccount, 'next-page-token');

        Http::assertSent(function (Request $request) {
            $hasUrl = Str::contains($request->url(), 'https://analyticsadmin.googleapis.com/v1beta/accounts');
            $hasToken = $request['pageToken'] === 'next-page-token';

            return $hasUrl && $hasToken;
        });
    }

    public function test_calls_itself_with_next_page_token_if_in_response(): void
    {
        Http::fake([
            '*' => Http::sequence()
                ->push(['accounts' => [], 'nextPageToken' => 'token'])
                ->push(['accounts' => []]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        app(SyncAccounts::class)
            ->execute($googleAccount);

        Http::assertSentCount(2);
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::fake([
            '*' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot sync Google Analytics accounts. Received HTTP error with status code %s for Google Account #%s. Response body:\n\n',
            500,
            $googleAccount->id
        ));

        app(SyncAccounts::class)
            ->execute($googleAccount);
    }

    public function test_updates_existing_google_analytics_accounts(): void
    {
        $googleAccount = GoogleAccount::factory()->create();

        /** @var GoogleAnalyticsAccount $googleAnalyticsAccount */
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->for($googleAccount)
            ->create();

        $previousLastSyncedAt = $googleAnalyticsAccount->last_synced_at;
        Carbon::setTestNow(now()->addDay());

        $this->assertNotSame($previousLastSyncedAt, now());

        Http::fake([
            '*' => Http::response([
                'accounts' => [
                    [
                        'name' => $googleAnalyticsAccount->external_id,
                        'displayName' => 'changedName',
                        'regionCode' => $googleAnalyticsAccount->region_code,
                        'createTime' => $googleAnalyticsAccount->external_created_at,
                        'updateTime' => $googleAnalyticsAccount->external_updated_at,
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = GoogleAnalyticsAccount::count();

        app(SyncAccounts::class)
            ->execute($googleAccount);

        $afterDatabaseCount = GoogleAnalyticsAccount::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $googleAnalyticsAccount->refresh();
        $this->assertSame('changedName', $googleAnalyticsAccount->name);

        $this->assertNotSame($previousLastSyncedAt->toDateTimeString(), $googleAnalyticsAccount->last_synced_at->toDateTimeString());
        $this->assertSame($previousLastSyncedAt->addDay()->toDateTimeString(), $googleAnalyticsAccount->last_synced_at->toDateTimeString());
    }

    public function test_creates_new_existing_google_analytics_accounts(): void
    {
        Http::fake([
            '*' => Http::response([
                'accounts' => [
                    [
                        'name' => 'accounts/123',
                        'displayName' => 'changedName',
                        'regionCode' => 'NL',
                        'createTime' => now()->toIso8601ZuluString('millisecond'),
                        'updateTime' => now()->toIso8601ZuluString('millisecond'),
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $this->assertDatabaseMissing(
            'google_analytics_accounts',
            ['external_id' => 'accounts/123']
        );

        app(SyncAccounts::class)
            ->execute($googleAccount);

        $this->assertDatabaseHas(
            'google_analytics_accounts',
            [
                'google_account_id' => $googleAccount->id,
                'external_id' => 'accounts/123',
            ]
        );
    }
}
