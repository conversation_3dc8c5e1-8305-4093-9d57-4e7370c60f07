<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Ads\SyncCustomerClients;
use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SyncCustomerClientsTest extends TestCase
{
    use RefreshDatabase;

    public function test_throws_exception_if_token_null(): void
    {
        Http::preventStrayRequests();

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturnNull();

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get customer clients. Could not retrieve token for Google Account #%s, customer id %s.',
            $googleAccount->id,
            '********'
        ));

        app(SyncCustomerClients::class)
            ->execute(
                googleAccount: $googleAccount,
                customerId: ********
            );
    }

    public function test_throws_exception_if_response_not_successful(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response(null, 500),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $this->expectExceptionMessage(sprintf(
            'Cannot get customer clients. Received HTTP error with status code %s for Google Account #%s, customer id %s. Response body:\n\n',
            500,
            $googleAccount->id,
            '********'
        ));

        app(SyncCustomerClients::class)
            ->execute(
                googleAccount: $googleAccount,
                customerId: ********
            );
    }

    public function test_doesnt_throw_exception_if_customer_not_enabled(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response('CUSTOMER_NOT_ENABLED', 403),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $result = app(SyncCustomerClients::class)
            ->execute(
                googleAccount: $googleAccount,
                customerId: ********
            );

        self::assertIsArray($result);
    }

    public function test_updates_existing_google_ad_account(): void
    {
        Http::preventStrayRequests();

        $googleAccount = GoogleAccount::factory()->create();

        /** @var GoogleAdAccount $googleAdAccount */
        $googleAdAccount = GoogleAdAccount::factory()
            ->for($googleAccount)
            ->create([
                'name' => 'Test account',
                'customer_id' => '********',
                'external_id' => '********',
            ]);

        $previousLastSyncedAt = $googleAdAccount->last_synced_at;
        Carbon::setTestNow(now()->addDay());

        $this->assertNotSame($previousLastSyncedAt, now());

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response([
                [
                    'results' => [
                        [
                            'customerClient' => [
                                'resourceName' => 'customers/********/customerClients/********',
                                'hidden' => false,
                                'level' => '0',
                                'timeZone' => 'Europe/Amsterdam',
                                'testAccount' => false,
                                'manager' => false,
                                'descriptiveName' => 'Company - NL',
                                'currencyCode' => 'EUR',
                                'status' => 'ENABLED',
                            ],
                        ],
                        [
                            'nonClient' => [
                                'other' => 'resource',
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $initialDatabaseCount = GoogleAdAccount::count();

        app(SyncCustomerClients::class)
            ->execute(
                googleAccount: $googleAccount,
                customerId: ********,
                loginCustomerId: ********
            );

        $afterDatabaseCount = GoogleAdAccount::count();
        $this->assertSame($initialDatabaseCount, $afterDatabaseCount);

        $googleAdAccount->refresh();
        $this->assertSame('Company - NL', $googleAdAccount->name);

        $this->assertNotSame($previousLastSyncedAt->toDateTimeString(), $googleAdAccount->last_synced_at->toDateTimeString());
        $this->assertSame($previousLastSyncedAt->addDay()->toDateTimeString(), $googleAdAccount->last_synced_at->toDateTimeString());
    }

    public function test_creates_new_existing_google_ad_accounts(): void
    {
        Http::preventStrayRequests();

        Http::fake([
            'https://googleads.googleapis.com/v18/customers/*/googleAds:searchStream' => Http::response([
                [
                    'results' => [
                        [
                            'customerClient' => [
                                'resourceName' => 'customers/********/customerClients/********',
                                'hidden' => false,
                                'level' => '0',
                                'timeZone' => 'Europe/Amsterdam',
                                'testAccount' => false,
                                'manager' => false,
                                'descriptiveName' => 'Company - NL',
                                'currencyCode' => 'EUR',
                                'status' => 'ENABLED',
                            ],
                            [
                                'nonClient' => [
                                    'other' => 'resource',
                                ],
                            ],
                        ],
                    ],
                ],
            ]),
        ]);

        $this->mock(RefreshToken::class)
            ->shouldReceive('execute')
            ->andReturn('token');

        $googleAccount = GoogleAccount::factory()->create();

        $this->assertDatabaseMissing(
            'google_ad_accounts',
            ['external_id' => '********']
        );

        app(SyncCustomerClients::class)
            ->execute(
                googleAccount: $googleAccount,
                customerId: ********
            );

        $this->assertDatabaseHas(
            'google_ad_accounts',
            [
                'google_account_id' => $googleAccount->id,
                'external_id' => '********',
            ]
        );
    }
}
