<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Sources\Google\Actions\Authentication\UpdateGoogleAccountBySocialiteUser;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class UpdateGoogleAccountBySocialiteUserTest extends TestCase
{
    use RefreshDatabase;

    public function test_execute_creates_new_user(): void
    {
        Carbon::setTestNow(now());
        $externalId = Str::random();

        $this->assertDatabaseMissing(
            'google_accounts',
            ['external_id' => $externalId]
        );

        $googleAccount = app(UpdateGoogleAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($externalId),
            [
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
            ]
        );

        $this->assertNotNull($googleAccount);
        $this->assertInstanceOf(GoogleAccount::class, $googleAccount);

        $this->assertSame($externalId, $googleAccount->external_id);
        $this->assertSame('<EMAIL>', $googleAccount->email);
        $this->assertSame('Test user', $googleAccount->name);
        $this->assertSame('https://image.com/non_existent', $googleAccount->image_url);
        $this->assertSame('refresh_token_123', $googleAccount->refresh_token);
        $this->assertSame('token_123', $googleAccount->token);
        $this->assertSame(now()->toDateTimeString(), $googleAccount->token_created_at->toDateTimeString());

        $this->assertNotEmpty($googleAccount->scopes);
        $this->assertContains('https://www.googleapis.com/auth/userinfo.profile', $googleAccount->scopes);
        $this->assertContains('https://www.googleapis.com/auth/userinfo.email', $googleAccount->scopes);
    }

    public function test_execute_updates_existing_user(): void
    {
        $googleAccount = GoogleAccount::factory()->create();
        $databaseCount = GoogleAccount::count();

        Carbon::setTestNow(now()->addDay());

        $googleAccount = app(UpdateGoogleAccountBySocialiteUser::class)->execute(
            $this->getMockedSocialiteUser($googleAccount->external_id)
        );

        $this->assertDatabaseCount(
            'google_accounts',
            $databaseCount
        );

        $this->assertNotNull($googleAccount);
        $this->assertInstanceOf(GoogleAccount::class, $googleAccount);

        $this->assertSame($googleAccount->external_id, $googleAccount->external_id);
        $this->assertSame(now()->toDateTimeString(), $googleAccount->token_created_at->toDateTimeString());
    }

    public function getMockedSocialiteUser(string $id): \Laravel\Socialite\Two\User
    {
        $mock = Mockery::mock(\Laravel\Socialite\Two\User::class);

        $mock->shouldReceive('getId')->andReturn($id);
        $mock->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $mock->shouldReceive('getName')->andReturn('Test user');
        $mock->shouldReceive('getAvatar')->andReturn('https://image.com/non_existent');
        $mock->refreshToken = 'refresh_token_123';
        $mock->token = 'token_123';

        return $mock;
    }
}
