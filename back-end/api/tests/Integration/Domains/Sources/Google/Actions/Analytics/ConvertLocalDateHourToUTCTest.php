<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\ConvertLocalDateHourToUTC;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ConvertLocalDateHourToUTCTest extends TestCase
{
    use RefreshDatabase;

    public function test_converts_timezones_to_utc(): void
    {
        $tests = [
            [
                'dateHour' => '2021011211',
                'timeZone' => 'Europe/Amsterdam',
                'expected' => '2021-01-12 10:00:00',
            ],
            [
                'dateHour' => '2021011211',
                'timeZone' => 'Etc/Greenwich',
                'expected' => '2021-01-12 11:00:00',
            ],
            [
                'dateHour' => '2021011211',
                'timeZone' => 'Hongkong',
                'expected' => '2021-01-12 03:00:00',
            ],
        ];

        foreach ($tests as $test) {
            $actual = app(ConvertLocalDateHourToUTC::class)
                ->execute(
                    dateHour: $test['dateHour'],
                    timeZone: $test['timeZone']
                )
                ->toDateTimeString();

            $this->assertEquals($test['expected'], $actual);
        }
    }
}
