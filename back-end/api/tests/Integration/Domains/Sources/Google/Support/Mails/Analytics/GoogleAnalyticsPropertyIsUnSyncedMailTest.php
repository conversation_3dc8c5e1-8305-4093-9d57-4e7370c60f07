<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Support\Mails\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsPropertyIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class GoogleAnalyticsPropertyIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new GoogleAnalyticsPropertyIsUnSyncedMail(
            googleAnalyticsPropertyId: $googleAnalyticsProperty->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Google Analytics Property #%s is not being synchronised', $googleAnalyticsProperty->id));

        $mail->assertSeeInHtml($googleAnalyticsProperty->id);
        $mail->assertSeeInHtml($googleAnalyticsProperty->name);
    }
}
