<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Support\Mails\Authentication;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Mails\Authentication\GoogleAccountMissingRemoteScopesMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class GoogleAccountMissingRemoteScopesMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new GoogleAccountMissingRemoteScopesMail(
            googleAccountId: $googleAccount->id,
            missingRemoteScopes: ['https://www.googleapis.com/auth/analytics.readonly'],
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Google Account #%s deactivated because of missing remote scopes', $googleAccount->id));

        $mail->assertSeeInHtml($googleAccount->id);
        $mail->assertSeeInHtml($googleAccount->email);
        $mail->assertSeeInHtml('https://www.googleapis.com/auth/analytics.readonly');
    }
}
