<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Support\Mails\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsAccountIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class GoogleAnalyticsAccountIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new GoogleAnalyticsAccountIsUnSyncedMail(
            googleAnalyticsAccountId: $googleAnalyticsAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Google Analytics Account #%s is not being synchronised', $googleAnalyticsAccount->id));

        $mail->assertSeeInHtml($googleAnalyticsAccount->id);
        $mail->assertSeeInHtml($googleAnalyticsAccount->name);
    }
}
