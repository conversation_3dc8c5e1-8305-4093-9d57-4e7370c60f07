<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Support\Mails\Ads;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Support\Mails\Ads\GoogleAdAccountIsUnSyncedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class GoogleAdAccountIsUnSyncedMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_properties(): void
    {
        $googleAdAccount = GoogleAdAccount::factory()
            ->create();

        Config::set('lkq.support.emails', '<EMAIL>');

        $mail = new GoogleAdAccountIsUnSyncedMail(
            googleAdAccountId: $googleAdAccount->id,
        );

        $mail->assertHasTo('<EMAIL>');

        $mail->assertHasSubject(sprintf('Google Ad Account #%s is not being synchronised', $googleAdAccount->id));

        $mail->assertSeeInHtml($googleAdAccount->id);
        $mail->assertSeeInHtml($googleAdAccount->name);
    }
}
