<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_google_analytics_accounts(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->has(GoogleAnalyticsAccount::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAccount->googleAnalyticsAccounts);
        $this->assertInstanceOf(GoogleAnalyticsAccount::class, $googleAccount->googleAnalyticsAccounts->first());
    }

    public function test_it_has_many_google_ad_accounts(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->has(GoogleAdAccount::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAccount->googleAdAccounts);
        $this->assertInstanceOf(GoogleAdAccount::class, $googleAccount->googleAdAccounts->first());
    }
}
