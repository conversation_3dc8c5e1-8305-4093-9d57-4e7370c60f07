<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeChannelInsight;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class YoutubeChannelTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_youtube_videos(): void
    {
        $youtubeGoogleAccount = YoutubeChannel::factory()
            ->has(YoutubeVideo::factory()->count(3))
            ->create();

        $this->assertCount(3, $youtubeGoogleAccount->youtubeVideos);
        $this->assertInstanceOf(YoutubeVideo::class, $youtubeGoogleAccount->youtubeVideos->first());
    }

    public function test_it_has_many_youtube_channel_insights(): void
    {
        $youtubeGoogleAccount = YoutubeChannel::factory()
            ->has(YoutubeChannelInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $youtubeGoogleAccount->youtubeChannelInsights);
        $this->assertInstanceOf(YoutubeChannelInsight::class, $youtubeGoogleAccount->youtubeChannelInsights->first());
    }

    public function test_it_has_data_source(): void
    {
        $youtubeGoogleAccount = YoutubeChannel::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $youtubeGoogleAccount->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(YoutubeChannel::class, $dataSource->sourceable);
    }
}
