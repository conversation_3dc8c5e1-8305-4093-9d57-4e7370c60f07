<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class YoutubeVideoTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_youtube_video_insights(): void
    {
        $youtubeGoogleAccount = YoutubeVideo::factory()
            ->has(YoutubeVideoInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $youtubeGoogleAccount->youtubeVideoInsights);
        $this->assertInstanceOf(YoutubeVideoInsight::class, $youtubeGoogleAccount->youtubeVideoInsights->first());
    }
}
