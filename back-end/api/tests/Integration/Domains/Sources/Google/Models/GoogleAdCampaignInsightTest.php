<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAdCampaignInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_ad_campaign(): void
    {
        $googleAdCampaignInsight = GoogleAdCampaignInsight::factory()
            ->for(GoogleAdCampaign::factory())
            ->create();

        $this->assertInstanceOf(GoogleAdCampaign::class, $googleAdCampaignInsight->googleAdCampaign);
    }

    public function test_it_has_data_source(): void
    {
        $googleAdCampaignInsight = GoogleAdCampaignInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $googleAdCampaignInsight->googleAdCampaign->google_ad_account_id,
                'sourceable_type' => GoogleAdAccount::class,
            ]);

        $dataSource = $googleAdCampaignInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(GoogleAdAccount::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $googleAdCampaignInsight = GoogleAdCampaignInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $googleAdCampaignInsight->googleAdCampaign->google_ad_account_id,
                'sourceable_type' => GoogleAdAccount::class,
            ]);

        self::assertContains($googleAdCampaignInsight->id, GoogleAdCampaignInsight::query()->pluck('id'));
        self::assertContains($googleAdCampaignInsight->id, GoogleAdCampaignInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
