<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAdCampaignTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_ad_account(): void
    {
        $googleAdCampaign = GoogleAdCampaign::factory()
            ->for(GoogleAdAccount::factory())
            ->create();

        $this->assertInstanceOf(GoogleAdAccount::class, $googleAdCampaign->googleAdAccount);
    }

    public function test_it_has_many_google_ad_campaign_insights(): void
    {
        $googleAdCampaign = GoogleAdCampaign::factory()
            ->has(GoogleAdCampaignInsight::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAdCampaign->googleAdCampaignInsights);
        $this->assertInstanceOf(GoogleAdCampaignInsight::class, $googleAdCampaign->googleAdCampaignInsights->first());
    }
}
