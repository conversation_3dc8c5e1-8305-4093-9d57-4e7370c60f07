<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAnalyticsReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_analytics_property(): void
    {
        $googleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->for(GoogleAnalyticsProperty::factory())
            ->create();

        $this->assertInstanceOf(GoogleAnalyticsProperty::class, $googleAnalyticsReport->googleAnalyticsProperty);
    }

    public function test_it_belongs_to_data_source(): void
    {
        $googleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->for(GoogleAnalyticsProperty::factory())
            ->create();

        DataSource::factory()
            ->forSourceable($googleAnalyticsReport->googleAnalyticsProperty)
            ->create();

        $this->assertInstanceOf(DataSource::class, $googleAnalyticsReport->dataSource);
    }

    public function test_it_can_filter_by_data_source_using_scope(): void
    {
        $firstGoogleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->for(GoogleAnalyticsProperty::factory())
            ->create();
        $firstDataSource = DataSource::factory()
            ->forSourceable($firstGoogleAnalyticsReport->googleAnalyticsProperty)
            ->create();

        $secondGoogleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->for(GoogleAnalyticsProperty::factory())
            ->create();
        $secondDataSource = DataSource::factory()
            ->forSourceable($secondGoogleAnalyticsReport->googleAnalyticsProperty)
            ->create();

        $thirdGoogleAnalyticsReport = GoogleAnalyticsReport::factory()
            ->for(GoogleAnalyticsProperty::factory())
            ->create();
        DataSource::factory()
            ->forSourceable($thirdGoogleAnalyticsReport->googleAnalyticsProperty)
            ->create();

        $ids = GoogleAnalyticsReport::query()
            ->withDataSources([
                $firstDataSource->id,
                $secondDataSource->id,
            ])
            ->pluck('id');

        $this->assertContains($firstGoogleAnalyticsReport->id, $ids);
        $this->assertContains($secondGoogleAnalyticsReport->id, $ids);
        $this->assertNotContains($thirdGoogleAnalyticsReport->id, $ids);
    }
}
