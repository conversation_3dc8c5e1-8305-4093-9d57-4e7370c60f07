<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAnalyticsPropertyTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_analytics_account(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->for(GoogleAnalyticsAccount::factory())
            ->create();

        $this->assertInstanceOf(GoogleAnalyticsAccount::class, $googleAnalyticsProperty->googleAnalyticsAccount);
    }

    public function test_it_has_many_google_analytics_reports(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(GoogleAnalyticsReport::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAnalyticsProperty->googleAnalyticsReports);
        $this->assertInstanceOf(GoogleAnalyticsReport::class, $googleAnalyticsProperty->googleAnalyticsReports->first());
    }

    public function test_it_has_data_source(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $googleAnalyticsProperty->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(GoogleAnalyticsProperty::class, $dataSource->sourceable);
    }

    public function test_it_can_filter_by_data_source_using_scope(): void
    {
        $firstGoogleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();
        $firstDataSource = DataSource::factory()
            ->forSourceable($firstGoogleAnalyticsProperty)
            ->create();

        $secondGoogleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();
        $secondDataSource = DataSource::factory()
            ->forSourceable($secondGoogleAnalyticsProperty)
            ->create();

        $thirdGoogleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();
        DataSource::factory()
            ->forSourceable($thirdGoogleAnalyticsProperty)
            ->create();

        $ids = GoogleAnalyticsProperty::query()
            ->withDataSources([
                $firstDataSource->id,
                $secondDataSource->id,
            ])
            ->pluck('id');

        $this->assertContains($firstGoogleAnalyticsProperty->id, $ids);
        $this->assertContains($secondGoogleAnalyticsProperty->id, $ids);
        $this->assertNotContains($thirdGoogleAnalyticsProperty->id, $ids);
    }
}
