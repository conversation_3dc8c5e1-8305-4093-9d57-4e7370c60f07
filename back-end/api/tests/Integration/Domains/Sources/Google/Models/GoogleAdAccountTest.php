<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAdAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_account(): void
    {
        $googleAdAccount = GoogleAdAccount::factory()
            ->for(GoogleAccount::factory())
            ->create();

        $this->assertInstanceOf(GoogleAccount::class, $googleAdAccount->googleAccount);
    }

    public function test_it_has_many_google_ad_campaigns(): void
    {
        $googleAdAccount = GoogleAdAccount::factory()
            ->has(GoogleAdCampaign::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAdAccount->googleAdCampaigns);
        $this->assertInstanceOf(GoogleAdCampaign::class, $googleAdAccount->googleAdCampaigns->first());
    }

    public function test_it_has_data_source(): void
    {
        $googleAdAccount = GoogleAdAccount::factory()
            ->has(DataSource::factory())
            ->create();

        $dataSource = $googleAdAccount->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(GoogleAdAccount::class, $dataSource->sourceable);
    }
}
