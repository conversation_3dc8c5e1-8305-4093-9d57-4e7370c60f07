<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class YoutubeGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_youtube_channels(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::factory()
            ->has(YoutubeChannel::factory()->count(3))
            ->create();

        $this->assertCount(3, $youtubeGoogleAccount->youtubeChannels);
        $this->assertInstanceOf(YoutubeChannel::class, $youtubeGoogleAccount->youtubeChannels->first());
    }
}
