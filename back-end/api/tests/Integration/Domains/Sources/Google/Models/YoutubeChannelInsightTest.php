<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeChannelInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class YoutubeChannelInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_youtube_channel(): void
    {
        $youtubeChannelInsight = YoutubeChannelInsight::factory()
            ->for(YoutubeChannel::factory())
            ->create();

        $this->assertInstanceOf(YoutubeChannel::class, $youtubeChannelInsight->youtubeChannel);
    }

    public function test_it_has_data_source(): void
    {
        $youtubeChannelInsight = YoutubeChannelInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $youtubeChannelInsight->youtube_channel_id,
                'sourceable_type' => YoutubeChannel::class,
            ]);

        $dataSource = $youtubeChannelInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(YoutubeChannel::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $youtubeChannelInsight = YoutubeChannelInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $youtubeChannelInsight->youtube_channel_id,
                'sourceable_type' => YoutubeChannel::class,
            ]);

        self::assertContains($youtubeChannelInsight->id, YoutubeChannelInsight::query()->pluck('id'));
        self::assertContains($youtubeChannelInsight->id, YoutubeChannelInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
