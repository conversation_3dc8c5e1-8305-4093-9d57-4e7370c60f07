<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAnalyticsAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_google_account(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->for(GoogleAccount::factory())
            ->create();

        $this->assertInstanceOf(GoogleAccount::class, $googleAnalyticsAccount->googleAccount);
    }

    public function test_it_has_many_google_analytics_properties(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->has(GoogleAnalyticsProperty::factory()->count(3))
            ->create();

        $this->assertCount(3, $googleAnalyticsAccount->googleAnalyticsProperties);
        $this->assertInstanceOf(GoogleAnalyticsProperty::class, $googleAnalyticsAccount->googleAnalyticsProperties->first());
    }
}
