<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class YoutubeVideoInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_youtube_video(): void
    {
        $youtubeVideoInsight = YoutubeVideoInsight::factory()
            ->for(YoutubeVideo::factory())
            ->create();

        $this->assertInstanceOf(YoutubeVideo::class, $youtubeVideoInsight->youtubeVideo);
    }

    public function test_it_has_data_source(): void
    {
        $youtubeVideoInsight = YoutubeVideoInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $youtubeVideoInsight->youtubeVideo->youtubeChannel->id,
                'sourceable_type' => YoutubeChannel::class,
            ]);

        $dataSource = $youtubeVideoInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(YoutubeChannel::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $youtubeVideoInsight = YoutubeVideoInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $youtubeVideoInsight->youtubeVideo->youtubeChannel->id,
                'sourceable_type' => YoutubeChannel::class,
            ]);

        self::assertContains($youtubeVideoInsight->id, YoutubeVideoInsight::query()->pluck('id'));
        self::assertContains($youtubeVideoInsight->id, YoutubeVideoInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
