<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Analytics\ConvertEventsToDataSourceForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetChannelGroupsForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetEventsForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetSessionsForProperty;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetEventsForPropertyException;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetSessionsForPropertyException;
use Illuminate\Http\Client\ConnectionException;
use Tests\TestCase;

class SyncGoogleAnalyticsPropertyTest extends TestCase
{
    /**
     * @throws ConnectionException
     * @throws CannotGetSessionsForPropertyException
     * @throws CannotGetEventsForPropertyException
     */
    public function test_it_calls_action(): void
    {
        $this->mock(GetSessionsForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->mock(GetEventsForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->mock(GetChannelGroupsForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->mock(ConvertEventsToDataSourceForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();

        $job = new SyncGoogleAnalyticProperty($googleAnalyticsProperty->id);
        $job->handle();
    }
}
