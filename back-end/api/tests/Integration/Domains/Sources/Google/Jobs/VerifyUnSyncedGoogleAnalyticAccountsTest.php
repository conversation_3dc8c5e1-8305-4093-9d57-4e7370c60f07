<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsAccountIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedGoogleAnalyticAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        GoogleAnalyticsAccount::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticAccounts;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedAccount = GoogleAnalyticsAccount::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticAccounts;
        $job->handle();

        Mail::assertQueued(
            GoogleAnalyticsAccountIsUnSyncedMail::class,
            fn (GoogleAnalyticsAccountIsUnSyncedMail $mail) => $mail->googleAnalyticsAccountId === $unSyncedAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedGoogleAnalyticAccounts::class, $scheduledEvents);
    }
}
