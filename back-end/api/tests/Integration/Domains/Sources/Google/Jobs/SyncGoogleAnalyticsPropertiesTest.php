<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperties;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAnalyticsPropertiesTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->create();

        $deletedGoogleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncGoogleAnalyticProperties;
        $job->handle();

        Queue::assertPushed(
            SyncGoogleAnalyticProperty::class,
            fn (SyncGoogleAnalyticProperty $job) => $job->googleAnalyticsPropertyId === $googleAnalyticsProperty->id
        );
        Queue::assertNotPushed(
            SyncGoogleAnalyticProperty::class,
            fn (SyncGoogleAnalyticProperty $job) => $job->googleAnalyticsPropertyId === $deletedGoogleAnalyticsProperty->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncGoogleAnalyticProperties::class, $scheduledEvents);
    }
}
