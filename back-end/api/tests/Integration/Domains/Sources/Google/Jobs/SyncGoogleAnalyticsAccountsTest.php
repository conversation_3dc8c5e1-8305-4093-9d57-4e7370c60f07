<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccount;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAnalyticsAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->create();

        $deletedGoogleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncGoogleAnalyticAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncGoogleAnalyticAccount::class,
            fn (SyncGoogleAnalyticAccount $job) => $job->googleAnalyticsAccountId === $googleAnalyticsAccount->id
        );
        Queue::assertNotPushed(
            SyncGoogleAnalyticAccount::class,
            fn (SyncGoogleAnalyticAccount $job) => $job->googleAnalyticsAccountId === $deletedGoogleAnalyticsAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncGoogleAnalyticAccounts::class, $scheduledEvents);
    }
}
