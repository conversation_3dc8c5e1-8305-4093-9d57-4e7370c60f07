<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAnalyticProperties;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsPropertyIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedGoogleAnalyticPropertiesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        GoogleAnalyticsProperty::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        Mail::assertNothingQueued();

        GoogleAnalyticsProperty::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        Mail::assertQueued(
            GoogleAnalyticsPropertyIsUnSyncedMail::class,
            fn (GoogleAnalyticsPropertyIsUnSyncedMail $mail) => $mail->googleAnalyticsPropertyId === $unSyncedProperty->id
        );
    }

    public function test_it_deletes(): void
    {
        Mail::fake();

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        $googleAnalyticsProperty->refresh();
        $this->assertNull($googleAnalyticsProperty->deleted_at);

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        $googleAnalyticsProperty->refresh();
        $this->assertNotNull($googleAnalyticsProperty->deleted_at);

        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAnalyticProperties;
        $job->handle();

        $googleAnalyticsProperty->refresh();
        $this->assertNull($googleAnalyticsProperty->deleted_at);
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedGoogleAnalyticProperties::class, $scheduledEvents);
    }
}
