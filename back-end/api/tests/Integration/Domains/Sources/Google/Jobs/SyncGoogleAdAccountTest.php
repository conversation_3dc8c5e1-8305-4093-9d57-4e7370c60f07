<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\SyncCustomerClients;
use App\Domains\Sources\Google\Jobs\SyncGoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Support\Exceptions\Ads\CannotGetCustomerClientsException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAdAccountTest extends TestCase
{
    /**
     * @throws ConnectionException
     * @throws CannotGetCustomerClientsException
     */
    public function test_it_calls_action(): void
    {
        Queue::fake();

        $googleAccount = GoogleAccount::factory()
            ->create();

        /** @var GoogleAdAccount $googleManagerAdAccount */
        $googleManagerAdAccount = GoogleAdAccount::factory()
            ->for($googleAccount)
            ->create([
                'is_manager' => true,
            ]);
        /** @var GoogleAdAccount $googleSubManagerAdAccount */
        $googleSubManagerAdAccount = GoogleAdAccount::factory()
            ->for($googleAccount)
            ->create([
                'is_manager' => true,
            ]);
        /** @var GoogleAdAccount $googleSubAdAccount */
        $googleSubAdAccount = GoogleAdAccount::factory()
            ->for($googleAccount)
            ->create([
                'is_manager' => false,
            ]);

        $this->mock(SyncCustomerClients::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn([$googleManagerAdAccount, $googleSubManagerAdAccount, $googleSubAdAccount]);

        $job = new SyncGoogleAdAccount(
            $googleAccount->id,
            $googleManagerAdAccount->external_id
        );
        $job->handle();

        Queue::assertPushed(SyncGoogleAdAccount::class, function (SyncGoogleAdAccount $job) use ($googleSubManagerAdAccount) {
            return $job->customerId === $googleSubManagerAdAccount->external_id;
        });
        Queue::assertNotPushed(SyncGoogleAdAccount::class, function (SyncGoogleAdAccount $job) use ($googleManagerAdAccount) {
            return $job->customerId === $googleManagerAdAccount->external_id;
        });
        Queue::assertNotPushed(SyncGoogleAdAccount::class, function (SyncGoogleAdAccount $job) use ($googleSubAdAccount) {
            return $job->customerId === $googleSubAdAccount->external_id;
        });
    }
}
