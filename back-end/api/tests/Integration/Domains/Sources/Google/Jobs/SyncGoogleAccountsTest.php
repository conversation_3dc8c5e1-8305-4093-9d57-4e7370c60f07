<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\SyncGoogleAccount;
use App\Domains\Sources\Google\Jobs\SyncGoogleAccounts;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $googleAccount = GoogleAccount::factory()
            ->create();

        $deletedGoogleAccount = GoogleAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncGoogleAccounts;
        $job->handle();

        Queue::assertPushed(
            SyncGoogleAccount::class,
            fn (SyncGoogleAccount $job) => $job->googleAccountId === $googleAccount->id
        );
        Queue::assertNotPushed(
            SyncGoogleAccount::class,
            fn (SyncGoogleAccount $job) => $job->googleAccountId === $deletedGoogleAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncGoogleAccounts::class, $scheduledEvents);
    }
}
