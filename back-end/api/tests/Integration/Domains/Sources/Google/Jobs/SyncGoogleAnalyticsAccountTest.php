<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Analytics\SyncProperties;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsPropertiesException;
use Illuminate\Http\Client\ConnectionException;
use Tests\TestCase;

class SyncGoogleAnalyticsAccountTest extends TestCase
{
    /**
     * @throws CannotSyncGoogleAnalyticsPropertiesException
     * @throws ConnectionException
     */
    public function test_it_calls_action(): void
    {
        $this->mock(SyncProperties::class)
            ->shouldReceive('execute')
            ->once();

        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->create();

        $job = new SyncGoogleAnalyticAccount($googleAnalyticsAccount->id);
        $job->handle();
    }
}
