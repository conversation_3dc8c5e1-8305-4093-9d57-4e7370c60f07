<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Authentication\GetScopes;
use App\Domains\Sources\Google\Jobs\VerifyScopesForGoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Mails\Authentication\GoogleAccountMissingRemoteScopesMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyScopesForGoogleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_does_nothing_if_scopes_match(): void
    {
        Mail::fake();

        $scopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/analytics.edit',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn($scopes);

        $googleAccount = GoogleAccount::factory()
            ->create([
                'scopes' => $scopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForGoogleAccount($googleAccount->id);
        $job->handle();

        Mail::assertNothingQueued();
    }

    public function test_it_does_nothing_if_remote_has_more_scopes(): void
    {
        Mail::fake();

        $scopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(array_merge($scopes, ['https://www.googleapis.com/auth/analytics.edit']));

        $googleAccount = GoogleAccount::factory()
            ->create([
                'scopes' => $scopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForGoogleAccount($googleAccount->id);
        $job->handle();

        Mail::assertNothingQueued();
    }

    public function test_it_throws_exception_and_deactivates_for_missing_scopes(): void
    {
        Mail::fake();

        Config::set('lkq.support.emails', '<EMAIL>');

        $localScopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/analytics.edit',
        ];
        $remoteScopes = [
            'https://www.googleapis.com/auth/analytics.edit',
        ];

        $this->mock(GetScopes::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn($remoteScopes);

        $googleAccount = GoogleAccount::factory()
            ->create([
                'scopes' => $localScopes,
                'token_created_at' => now(),
            ]);

        $job = new VerifyScopesForGoogleAccount($googleAccount->id);
        $job->handle();

        $googleAccount->refresh();
        $this->assertNotNull($googleAccount->deactivated_at);

        Mail::assertQueued(GoogleAccountMissingRemoteScopesMail::class);
    }
}
