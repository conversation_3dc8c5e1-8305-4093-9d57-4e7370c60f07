<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\SyncGoogleAdCampaign;
use App\Domains\Sources\Google\Jobs\SyncGoogleAdCampaigns;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAdCampaignsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_dispatches_jobs(): void
    {
        Queue::fake();

        $googleAdAccount = GoogleAdAccount::factory()
            ->create();

        $deletedGoogleAdAccount = GoogleAdAccount::factory()
            ->create([
                'deleted_at' => now(),
            ]);

        $job = new SyncGoogleAdCampaigns;
        $job->handle();

        Queue::assertPushed(
            SyncGoogleAdCampaign::class,
            fn (SyncGoogleAdCampaign $job) => $job->googleAdAccountId === $googleAdAccount->id
        );
        Queue::assertNotPushed(
            SyncGoogleAdCampaign::class,
            fn (SyncGoogleAdCampaign $job) => $job->googleAdAccountId === $deletedGoogleAdAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(SyncGoogleAdCampaigns::class, $scheduledEvents);
    }
}
