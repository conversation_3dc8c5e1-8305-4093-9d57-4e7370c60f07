<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAdAccounts;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Support\Mails\Ads\GoogleAdAccountIsUnSyncedMail;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class VerifyUnSyncedGoogleAdAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mails(): void
    {
        Mail::fake();

        GoogleAdAccount::factory()
            ->create([
                'last_synced_at' => now(),
            ]);

        $job = new VerifyUnSyncedGoogleAdAccounts;
        $job->handle();

        Mail::assertNothingQueued();

        $unSyncedAccount = GoogleAdAccount::factory()
            ->create([
                'last_synced_at' => now()->subDay()->subSecond(),
            ]);

        $job = new VerifyUnSyncedGoogleAdAccounts;
        $job->handle();

        Mail::assertQueued(
            GoogleAdAccountIsUnSyncedMail::class,
            fn (GoogleAdAccountIsUnSyncedMail $mail) => $mail->googleAdAccountId === $unSyncedAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyUnSyncedGoogleAdAccounts::class, $scheduledEvents);
    }
}
