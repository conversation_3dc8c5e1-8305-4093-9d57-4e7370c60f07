<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domains\Sources\Google\Actions\Analytics\SyncAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAccount;
use App\Domains\Sources\Google\Jobs\SyncGoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsAccountsException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SyncGoogleAccountTest extends TestCase
{
    /**
     * @throws ConnectionException
     * @throws CannotSyncGoogleAnalyticsAccountsException
     */
    public function test_it_calls_action(): void
    {
        Queue::fake();

        $this->mock(SyncAccounts::class)
            ->shouldReceive('execute')
            ->once();

        $this->mock(ListAccessibleAccounts::class)
            ->shouldReceive('execute')
            ->once()
            ->andReturn(['customer/123']);

        $googleAccount = GoogleAccount::factory()
            ->create();

        $job = new SyncGoogleAccount($googleAccount->id);
        $job->handle();

        Queue::assertPushed(SyncGoogleAdAccount::class, function (SyncGoogleAdAccount $job) use ($googleAccount) {
            return $job->customerId === 123 && $job->googleAccountId === $googleAccount->id;
        });
    }
}
