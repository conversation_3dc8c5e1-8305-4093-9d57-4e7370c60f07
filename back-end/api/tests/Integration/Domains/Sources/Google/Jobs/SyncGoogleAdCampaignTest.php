<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\GetCampaignsWithMetrics;
use App\Domains\Sources\Google\Jobs\SyncGoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Tests\TestCase;

class SyncGoogleAdCampaignTest extends TestCase
{
    public function test_it_calls_action(): void
    {
        $this->mock(GetCampaignsWithMetrics::class)
            ->shouldReceive('execute')
            ->once();

        $googleAdAccount = GoogleAdAccount::factory()
            ->create();

        $job = new SyncGoogleAdCampaign($googleAdAccount->id);
        $job->handle();
    }
}
