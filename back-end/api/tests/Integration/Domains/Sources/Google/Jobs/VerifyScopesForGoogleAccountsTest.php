<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Jobs\VerifyScopesForGoogleAccount;
use App\Domains\Sources\Google\Jobs\VerifyScopesForGoogleAccounts;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Console\Scheduling\CallbackEvent;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class VerifyScopesForGoogleAccountsTest extends TestCase
{
    public function test_it_dispatches_jobs(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->create();

        $deactivatedGoogleAccount = GoogleAccount::factory()
            ->create([
                'deactivated_at' => now(),
            ]);

        Queue::fake();

        $job = new VerifyScopesForGoogleAccounts;
        $job->handle();

        Queue::assertPushed(
            VerifyScopesForGoogleAccount::class,
            fn (VerifyScopesForGoogleAccount $job) => $job->googleAccountId === $googleAccount->id
        );
        Queue::assertNotPushed(
            VerifyScopesForGoogleAccount::class,
            fn (VerifyScopesForGoogleAccount $job) => $job->googleAccountId === $deactivatedGoogleAccount->id
        );
    }

    public function test_job_is_scheduled(): void
    {
        $schedule = app(Schedule::class);
        $scheduledEvents = array_map(fn (CallbackEvent|Event $event) => $event->description, $schedule->events());

        $this->assertContains(VerifyScopesForGoogleAccounts::class, $scheduledEvents);
    }
}
