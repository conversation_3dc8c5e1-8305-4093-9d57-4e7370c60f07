<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands;

use App\Domains\Sources\Google\Jobs\SyncGoogleAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperties;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;

class SyncTest extends TestCase
{
    public function test_handle_dispatches_jobs(): void
    {
        Bus::fake();

        $this->artisan('sources:google:sync')
            ->expectsOutput('Dispatching jobs to sync all Google connections')
            ->expectsOutput('Jobs dispatched successfully')
            ->assertOk();

        Bus::assertChained([
            SyncGoogleAccounts::class,
            SyncGoogleAnalyticAccounts::class,
            SyncGoogleAnalyticProperties::class,
        ]);
    }
}
