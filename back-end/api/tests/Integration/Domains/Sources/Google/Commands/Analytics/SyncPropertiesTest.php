<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\SyncProperties;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Tests\TestCase;

class SyncPropertiesTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->create();

        $this->mock(SyncProperties::class)
            ->shouldReceive('execute')
            ->once();

        $this->artisan('sources:google:analytics:sync:properties', [
            'googleAnalyticsAccountId' => $googleAnalyticsAccount->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Google Analytics properties for Google Analytics Account #%s (%s)',
                $googleAnalyticsAccount->id,
                $googleAnalyticsAccount->name
            ))
            ->expectsOutput('Google Analytics properties synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:google:analytics:sync:properties', [
            'googleAnalyticsAccountId' => 9999999,
        ])
            ->expectsOutput('Google Analytics Account not found')
            ->assertExitCode(1);
    }
}
