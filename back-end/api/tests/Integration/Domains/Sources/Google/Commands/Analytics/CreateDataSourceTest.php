<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Tests\TestCase;

class CreateDataSourceTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->create();

        $this->artisan('sources:google:analytics:data_source', [
            'googleAnalyticsPropertyId' => $googleAnalyticsProperty->id,
        ])
            ->expectsQuestion('What title?', 'Title')
            ->expectsQuestion('What region?', 'BE')
            ->expectsQuestion('What business_unit?', 'BU')
            ->expectsQuestion('What channel?', 'YT')
            ->expectsOutput('Data source created for Google Analytics Property connected successfully')
            ->assertOk();

        $this->assertDatabaseHas('data_sources', [
            'title' => 'Title',
            'region' => 'BE',
            'business_unit' => 'BU',
            'channel' => 'YT',
            'sourceable_id' => $googleAnalyticsProperty->id,
            'sourceable_type' => GoogleAnalyticsProperty::class,
        ]);
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:google:analytics:data_source', [
            'googleAnalyticsPropertyId' => 9999999,
        ])
            ->expectsOutput('Google Analytics Property not found')
            ->assertExitCode(1);
    }

    public function test_throws_error_for_existing_data_source(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->has(DataSource::factory())
            ->create();

        $this->artisan('sources:google:analytics:data_source', [
            'googleAnalyticsPropertyId' => $googleAnalyticsProperty->id,
        ])
            ->expectsOutput('Data Source already exists for Google Analytics Property')
            ->assertExitCode(1);
    }
}
