<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\GetSessionsForProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Tests\TestCase;

class GetSessionsForPropertyTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->for(GoogleAnalyticsAccount::factory())
            ->create();

        $this->mock(GetSessionsForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->artisan('sources:google:analytics:sync:sessions', [
            'googleAnalyticsPropertyId' => $googleAnalyticsProperty->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Google Analytics sessions for Google Analytics Property #%s (%s / %s)',
                $googleAnalyticsProperty->id,
                $googleAnalyticsProperty->googleAnalyticsAccount->name,
                $googleAnalyticsProperty->name
            ))
            ->expectsOutput('Google Analytics sessions synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:google:analytics:sync:sessions', [
            'googleAnalyticsPropertyId' => 9999999,
        ])
            ->expectsOutput('Google Analytics Property not found')
            ->assertExitCode(1);
    }
}
