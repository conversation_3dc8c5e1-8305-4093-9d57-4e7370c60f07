<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\ConvertEventsToDataSourceForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetEventsForProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Tests\TestCase;

class GetEventsForPropertyTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->for(GoogleAnalyticsAccount::factory())
            ->create();

        $this->mock(GetEventsForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->mock(ConvertEventsToDataSourceForProperty::class)
            ->shouldReceive('execute')
            ->once();

        $this->artisan('sources:google:analytics:sync:events', [
            'googleAnalyticsPropertyId' => $googleAnalyticsProperty->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Google Analytics events for Google Analytics Property #%s (%s / %s)',
                $googleAnalyticsProperty->id,
                $googleAnalyticsProperty->googleAnalyticsAccount->name,
                $googleAnalyticsProperty->name
            ))
            ->expectsOutput('Google Analytics events synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:google:analytics:sync:events', [
            'googleAnalyticsPropertyId' => 9999999,
        ])
            ->expectsOutput('Google Analytics Property not found')
            ->assertExitCode(1);
    }
}
