<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\SyncAccounts;
use App\Domains\Sources\Google\Models\GoogleAccount;
use Tests\TestCase;

class SyncAccountsTest extends TestCase
{
    public function test_handle_calls_action(): void
    {
        $googleAccount = GoogleAccount::factory()
            ->create();

        $this->mock(SyncAccounts::class)
            ->shouldReceive('execute')
            ->once();

        $this->artisan('sources:google:analytics:sync:accounts', [
            'googleAccountId' => $googleAccount->id,
        ])
            ->expectsOutput(sprintf(
                'Syncing Google Analytics accounts for Google Account #%s (%s)',
                $googleAccount->id,
                $googleAccount->name
            ))
            ->expectsOutput('Google Analytics accounts synced successfully')
            ->assertOk();
    }

    public function test_throws_error_for_missing_property(): void
    {
        $this->artisan('sources:google:analytics:sync:accounts', [
            'googleAccountId' => 9999999,
        ])
            ->expectsOutput('Google Account not found')
            ->assertExitCode(1);
    }
}
