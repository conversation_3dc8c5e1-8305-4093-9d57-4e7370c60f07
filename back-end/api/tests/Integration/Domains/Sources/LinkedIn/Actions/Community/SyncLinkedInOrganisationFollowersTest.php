<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Actions\Community\SyncLinkedInOrganisationFollowers;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationFollowers;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class SyncLinkedInOrganisationFollowersTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/organisation-followers-response.json'
        );
    }

    public function test_it_can_sync_follower_count(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '********',
        ]);

        Http::fake([
            'api.linkedin.com/rest/organizationalEntityFollowerStatistics*' => Http::response($this->mockResponse, 200),
        ]);

        app(SyncLinkedInOrganisationFollowers::class)->execute($organisation);

        Http::assertSent(function (Request $request) use ($organisation) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/organizationalEntityFollowerStatistics') &&
                $request['q'] === 'organizationalEntity' &&
                $request['organizationalEntity'] === "urn:li:organization:{$organisation->external_id}";
        });

        $this->assertDatabaseHas('linked_in_organisation_insights', [
            'linkedin_organisation_id' => $organisation->id,
            'date' => now()->startOfDay()->toDateTimeString(),
            'followers' => 2909, // Sum of all organic followers
        ]);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $linkedInAccount = LinkedInAccount::factory()->create([
            'token' => null,
        ]);
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
        ]);

        $this->expectException(CannotGetLinkedInOrganisationFollowers::class);

        app(SyncLinkedInOrganisationFollowers::class)->execute($organisation);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        $organisation = LinkedInOrganisation::factory()->create();

        Http::fake([
            'api.linkedin.com/rest/organizationalEntityFollowerStatistics*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInOrganisationFollowers::class);

        app(SyncLinkedInOrganisationFollowers::class)->execute($organisation);
    }

    public function test_it_updates_existing_insight_for_same_day(): void
    {
        $organisation = LinkedInOrganisation::factory()->create([
            'external_id' => '********',
        ]);

        $existingInsight = LinkedInOrganisationInsight::factory()
            ->for($organisation)
            ->create([
                'date' => now()->startOfDay(),
                'followers' => 1000,
            ]);

        Http::fake([
            'api.linkedin.com/rest/organizationalEntityFollowerStatistics*' => Http::response($this->mockResponse, 200),
        ]);

        app(SyncLinkedInOrganisationFollowers::class)->execute($organisation);

        $this->assertDatabaseHas('linked_in_organisation_insights', [
            'id' => $existingInsight->id,
            'linkedin_organisation_id' => $organisation->id,
            'date' => now()->startOfDay()->toDateTimeString(),
            'followers' => 2909,
        ]);
    }
}
