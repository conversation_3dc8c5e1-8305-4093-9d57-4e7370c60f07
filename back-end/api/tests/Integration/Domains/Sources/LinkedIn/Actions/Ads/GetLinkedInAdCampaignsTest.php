<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetLinkedInAdCampaigns;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetLinkedInAdCampaignsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/ad-campaigns-response.json'
        );
    }

    public function test_it_can_fetch_and_store_linkedin_ad_campaigns(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAccounts/*/adCampaigns*' => Http::response($this->mockResponse, 200),
        ]);

        $adAccount = LinkedInAdAccount::factory()->create();
        $nextPage = app(GetLinkedInAdCampaigns::class)->execute($adAccount);

        Http::assertSent(function ($request) use ($adAccount) {
            return Str::contains($request->url(), "https://api.linkedin.com/rest/adAccounts/{$adAccount->external_id}/adCampaigns") &&
                $request->hasHeader('Authorization', 'Bearer '.$adAccount->linkedinAccount->token);
        });

        $this->assertDatabaseCount('linked_in_ad_campaigns', 42);
        $this->assertDatabaseHas('linked_in_ad_campaigns', [
            'linkedin_ad_account_id' => $adAccount->id,
            'external_id' => '*********',
            'name' => 'ONGOING_ENGAGEMENT_2024_22FEB',
        ]);

        $this->assertEquals(10, $nextPage);
    }
}
