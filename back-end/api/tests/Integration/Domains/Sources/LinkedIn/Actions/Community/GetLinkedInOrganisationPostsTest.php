<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationPosts;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationPosts;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetLinkedInOrganisationPostsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/organisation-posts-response.json'
        );
    }

    public function test_it_can_fetch_and_store_linkedin_posts(): void
    {
        Http::fake([
            'api.linkedin.com/rest/posts*' => Http::response($this->mockResponse, 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
        ]);

        $nextPage = app(GetLinkedInOrganisationPosts::class)->execute($organisation);

        Http::assertSent(function (Request $request) use ($organisation, $linkedInAccount) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/posts') &&
                $request->hasHeader('Authorization', 'Bearer '.$linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '2.0.0') &&
                $request['author'] === "urn:li:organization:{$organisation->external_id}" &&
                $request['q'] === 'author' &&
                $request['sortBy'] === 'LAST_MODIFIED';
        });

        $this->assertDatabaseCount('linked_in_organisation_posts', 50);
        $this->assertDatabaseHas('linked_in_organisation_posts', [
            'linkedin_organisation_id' => $organisation->id,
            'external_id' => 'urn:li:ugcPost:7297541033863630848',
            'state' => 'PUBLISHED',
            'visibility' => 'PUBLIC',
            'date' => Carbon::createFromTimestampMs(*************)->toDateTimeString(),
            'commentary_excerpt' => '𝗟𝗞𝗤 𝘇𝗲𝘁 𝗶𝗻 𝗼𝗽 𝗲𝗲𝗻 𝗶𝗻𝗰𝗹𝘂𝘀𝗶𝗲𝘃𝗲 𝗲𝗻 𝗴𝗲𝘇𝗼𝗻𝗱𝗲 𝘄𝗲𝗿𝗸𝗼𝗺𝗴𝗲𝘃𝗶𝗻𝗴 𝘄𝗮𝗮𝗿𝗶𝗻 𝗶𝗲𝗱𝗲𝗿𝗲𝗲𝗻 𝘇𝗶𝗰𝗵 𝗴𝗼𝗲𝗱 𝘃𝗼𝗲𝗹𝘁 𝗲𝗻 𝘇𝗶𝗰𝗵 𝗼𝗽𝘁𝗶𝗺𝗮𝗮𝗹 𝗸𝗮𝗻 𝗼𝗻𝘁𝘄𝗶𝗸𝗸𝗲𝗹𝗲𝗻. Daarom startte LKQ in 2024 het project  “Inspired to Thrive”, waarbinnen collega’s onder andere opgeleid worden tot Wellbeing Champions....',
        ]);

        $this->assertEquals(50, $nextPage);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $organisation = LinkedInOrganisation::factory()->create();

        $this->expectException(CannotGetLinkedInOrganisationPosts::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn posts for organisation {$organisation->id} because of missing token"
        );

        app(GetLinkedInOrganisationPosts::class)->execute($organisation);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        $organisation = LinkedInOrganisation::factory()->create();

        Http::fake([
            'api.linkedin.com/rest/posts*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInOrganisationPosts::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn posts for organisation {$organisation->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetLinkedInOrganisationPosts::class)->execute($organisation);
    }
}
