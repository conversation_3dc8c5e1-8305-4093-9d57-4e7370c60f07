<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetAccessibleLinkedInAdAccounts;
use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Ads\CannotGetLinkedInAdAccountsForLinkedInAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetAccessibleLinkedInAdAccountsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/ad-accounts-response.json'
        );
    }

    public function test_it_can_fetch_and_store_linkedin_ad_accounts(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAccounts*' => Http::response($this->mockResponse, 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $nextPage = app(GetAccessibleLinkedInAdAccounts::class)->execute($linkedInAccount);

        Http::assertSent(function (Request $request) use ($linkedInAccount) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/adAccounts') &&
                $request->hasHeader('Authorization', 'Bearer '.$linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '1.0.0') &&
                $request['q'] === 'search' &&
                $request['count'] === 50;
        });

        // Assert accounts were stored
        $this->assertDatabaseCount('linked_in_ad_accounts', 3);

        // Assert first account details
        $this->assertDatabaseHas('linked_in_ad_accounts', [
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '*********',
            'name' => 'Advertentieaccount Automotive Academy',
            'currency' => 'EUR',
            'status' => 'ACTIVE',
            'type' => 'BUSINESS',
            'reference' => 'urn:li:organization:********',
        ]);

        // Assert pagination
        $this->assertEquals(1, $nextPage);
    }

    public function test_it_handles_empty_response(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAccounts*' => Http::response([
                'paging' => [
                    'start' => 0,
                    'count' => 50,
                    'links' => [],
                ],
                'elements' => [],
            ], 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $nextPage = app(GetAccessibleLinkedInAdAccounts::class)->execute($linkedInAccount);

        $this->assertNull($nextPage);
        $this->assertDatabaseCount('linked_in_ad_accounts', 0);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $accountWithoutToken = LinkedInAccount::factory()->create([
            'token' => null,
        ]);

        $this->expectException(CannotGetLinkedInAdAccountsForLinkedInAccount::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn ad accounts for LinkedIn account {$accountWithoutToken->id} because of missing token"
        );

        app(GetAccessibleLinkedInAdAccounts::class)->execute($accountWithoutToken);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();

        Http::fake([
            'api.linkedin.com/rest/adAccounts*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInAdAccountsForLinkedInAccount::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn ad accounts for LinkedIn account {$linkedInAccount->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetAccessibleLinkedInAdAccounts::class)->execute($linkedInAccount);
    }

    public function test_it_updates_existing_ad_accounts(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();
        $existingAccount = LinkedInAdAccount::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '*********',
            'name' => 'Old Name',
        ]);

        Http::fake([
            'api.linkedin.com/rest/adAccounts*' => Http::response($this->mockResponse, 200),
        ]);

        // Act
        app(GetAccessibleLinkedInAdAccounts::class)->execute($linkedInAccount);

        // Assert
        $this->assertDatabaseHas('linked_in_ad_accounts', [
            'id' => $existingAccount->id,
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '*********',
            'name' => 'Advertentieaccount Automotive Academy',
        ]);
    }
}
