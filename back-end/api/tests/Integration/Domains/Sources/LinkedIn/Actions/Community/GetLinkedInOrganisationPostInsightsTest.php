<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationPostInsights;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPostInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationPostInsights;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetLinkedInOrganisationPostInsightsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/post-statistics-response.json'
        );
    }

    public function test_it_can_fetch_and_save_post_statistics(): void
    {
        Http::fake([
            'api.linkedin.com/rest/organizationalEntityShareStatistics*' => Http::response(
                $this->mockResponse,
                200
            ),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '********',
        ]);

        $post1 = LinkedInOrganisationPost::factory()->create([
            'linkedin_organisation_id' => $organisation->id,
            'external_id' => 'urn:li:ugcPost:7297193252535853056',
        ]);

        $post2 = LinkedInOrganisationPost::factory()->create([
            'linkedin_organisation_id' => $organisation->id,
            'external_id' => 'urn:li:ugcPost:7297541033863630848',
        ]);

        $postIds = [
            'urn:li:ugcPost:7297541033863630848',
            'urn:li:ugcPost:7297193252535853056',
        ];

        $startDate = Carbon::createFromTimestamp(**********);
        $endDate = Carbon::createFromTimestamp(**********);

        $statistics = app(GetLinkedInOrganisationPostInsights::class)->execute(
            $organisation,
            $postIds,
            $startDate,
            $endDate
        );

        // Assert API request was made correctly
        Http::assertSent(function (Request $request) use ($organisation, $linkedInAccount) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/organizationalEntityShareStatistics') &&
                $request->hasHeader('Authorization', 'Bearer '.$linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '1.0.0') &&
                $request['organizationalEntity'] === "urn:li:organization:{$organisation->external_id}" &&
                $request['q'] === 'organizationalEntity' &&
                $request['timeIntervals.timeGranularityType'] === 'DAY';
        });

        // Assert statistics were returned correctly
        $this->assertCount(5, $statistics);
        $this->assertEquals(953, $statistics[0]['totalShareStatistics']['uniqueImpressionsCount']);
        $this->assertEquals(1427, $statistics[0]['totalShareStatistics']['impressionCount']);

        // Assert statistics were saved correctly
        $this->assertDatabaseCount('linked_in_organisation_post_insights', 5);

        // Assert specific statistics for post 1
        $post1Statistics = LinkedInOrganisationPostInsight::where('linkedin_organisation_post_id', $post1->id)
            ->orderBy('date')
            ->get();

        $this->assertCount(3, $post1Statistics);

        $firstStat = $post1Statistics->first();
        $this->assertEquals(953, $firstStat->unique_impressions_count);
        $this->assertEquals(1427, $firstStat->impression_count);
        $this->assertEquals(0.4128, $firstStat->engagement);
        $this->assertEquals(Carbon::createFromTimestampMs(1739750400000), $firstStat->date);

        // Assert specific statistics for post 2
        $post2Statistics = LinkedInOrganisationPostInsight::where('linkedin_organisation_post_id', $post2->id)
            ->orderBy('date')
            ->get();

        $this->assertCount(2, $post2Statistics);

        $firstStat = $post2Statistics->first();
        $this->assertEquals(177, $firstStat->unique_impressions_count);
        $this->assertEquals(234, $firstStat->impression_count);
        $this->assertEquals(0.4231, $firstStat->engagement);
        $this->assertEquals(Carbon::createFromTimestampMs(1739836800000), $firstStat->date);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $organisation = LinkedInOrganisation::factory()->create();

        $this->expectException(CannotGetLinkedInOrganisationPostInsights::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn Organisation Post Insights for organisation {$organisation->id} because of missing token"
        );

        app(GetLinkedInOrganisationPostInsights::class)->execute(
            $organisation,
            ['dummy-post-id'],
            now(),
            now()
        );
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        $organisation = LinkedInOrganisation::factory()->create();

        Http::fake([
            'api.linkedin.com/rest/organizationalEntityShareStatistics*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInOrganisationPostInsights::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn Organisation Post Insights for organisation {$organisation->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetLinkedInOrganisationPostInsights::class)->execute(
            $organisation,
            ['dummy-post-id'],
            now(),
            now()
        );
    }
}
