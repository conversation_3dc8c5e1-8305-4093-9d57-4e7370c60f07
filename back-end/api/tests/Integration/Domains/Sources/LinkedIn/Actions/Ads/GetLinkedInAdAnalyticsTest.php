<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetLinkedInAdAnalytics;
use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Ads\CannotGetLinkedInAdAnalyticsException;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetLinkedInAdAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    private LinkedInAccount $linkedInAccount;

    private LinkedInAdAccount $adAccount;

    private LinkedInAdCampaign $campaign;

    private Carbon $startDate;

    private Carbon $endDate;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/ad-analytics-response.json'
        );

        $this->linkedInAccount = LinkedInAccount::factory()->create();
        $this->adAccount = LinkedInAdAccount::factory()->create([
            'linkedin_account_id' => $this->linkedInAccount->id,
        ]);
        $this->campaign = LinkedInAdCampaign::factory()->create([
            'linkedin_ad_account_id' => $this->adAccount->id,
            'external_id' => '*********',
        ]);

        $this->startDate = Carbon::create(2024, 12, 1);
        $this->endDate = Carbon::create(2024, 12, 31);
    }

    public function test_it_can_fetch_and_store_linkedin_ad_analytics(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAnalytics*' => Http::response($this->mockResponse, 200),
        ]);

        app(GetLinkedInAdAnalytics::class)->execute(
            [$this->campaign->external_id],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );

        Http::assertSent(function (Request $request) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/adAnalytics') &&
                $request->hasHeader('Authorization', 'Bearer '.$this->linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '1.0.0') &&
                $request['q'] === 'analytics' &&
                $request['pivot'] === 'CAMPAIGN' &&
                $request['timeGranularity'] === 'DAILY' &&
                $request['campaigns'] === ["urn:li:sponsoredCampaign:{$this->campaign->external_id}"];
        });

        $this->assertDatabaseHas('linked_in_ad_insights', [
            'linkedin_ad_campaign_id' => $this->campaign->id,
            'clicks' => 14,
            'impressions' => 247,
            'reach' => 247,
            'spend' => 12.06,
        ]);
    }

    public function test_it_handles_nonexistent_campaigns(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAnalytics*' => Http::response($this->mockResponse, 200),
        ]);

        app(GetLinkedInAdAnalytics::class)->execute(
            ['nonexistent-id'],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );

        $this->assertDatabaseCount('linked_in_ad_insights', 0);
    }

    public function test_it_handles_empty_response(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAnalytics*' => Http::response([
                'paging' => [
                    'start' => 0,
                    'count' => 10,
                    'links' => [],
                ],
                'elements' => [],
            ], 200),
        ]);

        app(GetLinkedInAdAnalytics::class)->execute(
            [$this->campaign->external_id],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );

        $this->assertDatabaseCount('linked_in_ad_insights', 0);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $this->expectException(CannotGetLinkedInAdAnalyticsException::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn ad analytics for LinkedIn account {$this->linkedInAccount->id} because of missing token"
        );

        app(GetLinkedInAdAnalytics::class)->execute(
            [$this->campaign->external_id],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        Http::fake([
            'api.linkedin.com/rest/adAnalytics*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInAdAnalyticsException::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn ad analytics for LinkedIn account {$this->linkedInAccount->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetLinkedInAdAnalytics::class)->execute(
            [$this->campaign->external_id],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );
    }

    public function test_it_updates_existing_insights(): void
    {
        $existingInsight = LinkedInAdInsight::factory()->create([
            'linkedin_ad_campaign_id' => $this->campaign->id,
            'date' => '2025-02-17',
            'clicks' => 10,
            'impressions' => 100,
        ]);

        Http::fake([
            'api.linkedin.com/rest/adAnalytics*' => Http::response($this->mockResponse, 200),
        ]);

        app(GetLinkedInAdAnalytics::class)->execute(
            [$this->campaign->external_id],
            $this->linkedInAccount,
            $this->startDate,
            $this->endDate
        );

        $this->assertDatabaseHas('linked_in_ad_insights', [
            'id' => $existingInsight->id,
            'linkedin_ad_campaign_id' => $this->campaign->id,
            'date' => '2025-02-17',
            'clicks' => 14,
            'impressions' => 247,
        ]);
    }
}
