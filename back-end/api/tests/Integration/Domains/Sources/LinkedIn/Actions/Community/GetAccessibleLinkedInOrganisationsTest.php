<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Actions\Community\GetAccessibleLinkedInOrganisations;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationsForLinkedInAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class GetAccessibleLinkedInOrganisationsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/organisations-response.json'
        );
    }

    public function test_it_can_fetch_and_store_linkedin_organisations(): void
    {
        Http::fake([
            'api.linkedin.com/rest/organizationAcls*' => Http::response($this->mockResponse, 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $nextPage = app(GetAccessibleLinkedInOrganisations::class)->execute($linkedInAccount);

        Http::assertSent(function (Request $request) use ($linkedInAccount) {
            return Str::contains($request->url(), 'https://api.linkedin.com/rest/organizationAcls') &&
                $request->hasHeader('Authorization', 'Bearer '.$linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '2.0.0') &&
                $request['q'] === 'roleAssignee';
        });

        // Assert organisations were stored
        $this->assertDatabaseCount('linked_in_organisations', 5);

        // Assert first organisation details
        $this->assertDatabaseHas('linked_in_organisations', [
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '3332577',
            'state' => 'APPROVED',
        ]);

        // Assert pagination
        $this->assertNull($nextPage);
    }

    public function test_it_handles_empty_response(): void
    {
        Http::fake([
            'api.linkedin.com/rest/organizationAcls*' => Http::response([
                'paging' => [
                    'start' => 0,
                    'count' => 10,
                    'links' => [],
                ],
                'elements' => [],
            ], 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $nextPage = app(GetAccessibleLinkedInOrganisations::class)->execute($linkedInAccount);

        $this->assertNull($nextPage);
        $this->assertDatabaseCount('linked_in_organisations', 0);
    }

    public function test_it_throws_exception_when_token_is_missing(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $accountWithoutToken = LinkedInAccount::factory()->create([
            'token' => null,
        ]);

        $this->expectException(CannotGetLinkedInOrganisationsForLinkedInAccount::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn organisations for LinkedIn account {$accountWithoutToken->id} because of missing token"
        );

        app(GetAccessibleLinkedInOrganisations::class)->execute($accountWithoutToken);
    }

    public function test_it_throws_exception_on_api_error(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();

        Http::fake([
            'api.linkedin.com/rest/organizationAcls*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInOrganisationsForLinkedInAccount::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn organisations for LinkedIn account {$linkedInAccount->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetAccessibleLinkedInOrganisations::class)->execute($linkedInAccount);
    }

    public function test_it_updates_existing_organisations(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();
        $existingOrganisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '3332577',
            'state' => 'PENDING',
        ]);

        Http::fake([
            'api.linkedin.com/rest/organizationAcls*' => Http::response($this->mockResponse, 200),
        ]);

        // Act
        app(GetAccessibleLinkedInOrganisations::class)->execute($linkedInAccount);

        // Assert
        $this->assertDatabaseHas('linked_in_organisations', [
            'id' => $existingOrganisation->id,
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '3332577',
            'state' => 'APPROVED',
        ]);
    }
}
