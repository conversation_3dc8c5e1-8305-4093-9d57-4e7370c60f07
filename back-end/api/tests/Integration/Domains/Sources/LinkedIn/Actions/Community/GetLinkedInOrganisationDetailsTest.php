<?php

namespace Tests\Integration\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationDetails;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationDetails;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GetLinkedInOrganisationDetailsTest extends TestCase
{
    use RefreshDatabase;

    private string $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponse = file_get_contents(
            base_path().'/tests/Fixtures/LinkedIn/organisation-details-response.json'
        );
    }

    public function test_it_can_fetch_and_update_organisation_details(): void
    {
        Http::fake([
            'api.linkedin.com/rest/organizations/*' => Http::response($this->mockResponse, 200),
        ]);

        $linkedInAccount = LinkedInAccount::factory()->create();
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => '********',
        ]);

        app(GetLinkedInOrganisationDetails::class)->execute($linkedInAccount, $organisation->external_id);

        Http::assertSent(function (Request $request) use ($linkedInAccount, $organisation) {
            return $request->url() === "https://api.linkedin.com/rest/organizations/{$organisation->external_id}" &&
                $request->hasHeader('Authorization', 'Bearer '.$linkedInAccount->token) &&
                $request->hasHeader('LinkedIn-Version', '202501') &&
                $request->hasHeader('X-Restli-Protocol-Version', '2.0.0');
        });

        $this->assertDatabaseHas('linked_in_organisations', [
            'id' => $organisation->id,
            'name' => 'Automotive Academy',
            'website' => 'http://www.automotiveacademy.nl',
        ]);
    }

    public function test_it_handles_missing_token(): void
    {
        $this->mock(RefreshToken::class)
            ->expects('execute')
            ->once()
            ->andReturn(null);

        $accountWithoutToken = LinkedInAccount::factory()->create([
            'token' => null,
        ]);
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $accountWithoutToken->id,
        ]);

        $this->expectException(CannotGetLinkedInOrganisationDetails::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn organisation details for LinkedIn account {$accountWithoutToken->id} because of missing token"
        );

        app(GetLinkedInOrganisationDetails::class)->execute($accountWithoutToken, $organisation->external_id);
    }

    public function test_it_handles_api_error(): void
    {
        $linkedInAccount = LinkedInAccount::factory()->create();
        $organisation = LinkedInOrganisation::factory()->create([
            'linkedin_account_id' => $linkedInAccount->id,
        ]);

        Http::fake([
            'api.linkedin.com/rest/organizations/*' => Http::response([
                'message' => 'Unauthorized',
            ], 401),
        ]);

        $this->expectException(CannotGetLinkedInOrganisationDetails::class);
        $this->expectExceptionMessage(
            "Cannot get LinkedIn organisation details for LinkedIn account {$linkedInAccount->id}. ".
            'Status code: 401. Response body: {"message":"Unauthorized"}'
        );

        app(GetLinkedInOrganisationDetails::class)->execute($linkedInAccount, $organisation->external_id);
    }
}
