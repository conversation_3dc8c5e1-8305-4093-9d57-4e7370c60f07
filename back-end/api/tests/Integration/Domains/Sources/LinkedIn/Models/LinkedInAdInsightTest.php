<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\LinkedIn\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LinkedInAdInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_linked_in_ad_campaign(): void
    {
        $LinkedInAdCampaignInsight = LinkedInAdInsight::factory()
            ->for(LinkedInAdCampaign::factory())
            ->create();

        $this->assertInstanceOf(LinkedInAdCampaign::class, $LinkedInAdCampaignInsight->linkedInAdCampaign);
    }

    public function test_it_has_data_source(): void
    {
        $LinkedInAdCampaignInsight = LinkedInAdInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $LinkedInAdCampaignInsight->linkedInAdCampaign->linkedin_ad_account_id,
                'sourceable_type' => LinkedInAdAccount::class,
            ]);

        $dataSource = $LinkedInAdCampaignInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(LinkedInAdAccount::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $LinkedInAdCampaignInsight = LinkedInAdInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $LinkedInAdCampaignInsight->linkedInAdCampaign->linkedin_ad_account_id,
                'sourceable_type' => LinkedInAdAccount::class,
            ]);

        self::assertContains($LinkedInAdCampaignInsight->id, LinkedInAdInsight::query()->pluck('id'));
        self::assertContains($LinkedInAdCampaignInsight->id, LinkedInAdInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
