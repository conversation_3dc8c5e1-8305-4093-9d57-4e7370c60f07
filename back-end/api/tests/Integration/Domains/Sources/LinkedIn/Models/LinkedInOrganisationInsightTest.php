<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Sources\LinkedIn\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationInsight;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LinkedInOrganisationInsightTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_linked_in_ad_campaign(): void
    {
        $linkedInOrganisationInsight = LinkedInOrganisationInsight::factory()
            ->for(LinkedInOrganisation::factory())
            ->create();

        $this->assertInstanceOf(LinkedInOrganisation::class, $linkedInOrganisationInsight->linkedInOrganisation);
    }

    public function test_it_has_data_source(): void
    {
        $linkedInOrganisationInsight = LinkedInOrganisationInsight::factory()
            ->create();

        DataSource::factory()
            ->create([
                'sourceable_id' => $linkedInOrganisationInsight->linkedin_organisation_id,
                'sourceable_type' => LinkedInOrganisation::class,
            ]);

        $dataSource = $linkedInOrganisationInsight->dataSource;
        $this->assertInstanceOf(DataSource::class, $dataSource);
        $this->assertInstanceOf(LinkedInOrganisation::class, $dataSource->sourceable);
    }

    public function test_it_scopes_data_source(): void
    {
        $linkedInOrganisationInsight = LinkedInOrganisationInsight::factory()
            ->create();

        $dataSource = DataSource::factory()
            ->create([
                'sourceable_id' => $linkedInOrganisationInsight->linkedin_organisation_id,
                'sourceable_type' => LinkedInOrganisation::class,
            ]);

        self::assertContains($linkedInOrganisationInsight->id, LinkedInOrganisationInsight::query()->pluck('id'));
        self::assertContains($linkedInOrganisationInsight->id, LinkedInOrganisationInsight::query()->withDataSources([$dataSource->id])->pluck('id'));
    }
}
