<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Support\Dto;

use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WidgetAnalyticsFilterTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_gives_comparable_date_for_random_period(): void
    {
        Carbon::setTestNow(
            now()->startOfDay()->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now());
        $filter->setEndDate(now()->addDays(2));

        $this->assertNotNull($filter->getComparableStartDate());
        $this->assertNotNull($filter->getComparableEndDate());

        $this->assertEquals(2, $filter->getDaysInFilteredDateRange());
        $this->assertEquals(now()->subDays(2), $filter->getComparableStartDate());
        $this->assertEquals(now()->subSecond(), $filter->getComparableEndDate());
    }

    public function test_it_gives_comparable_date_for_monthly_period(): void
    {
        Carbon::setTestNow(
            now()->setMonth(3)->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->startOfMonth());
        $filter->setEndDate(null);
        $this->assertNull($filter->getComparableEndDate());
        $this->assertNull($filter->getComparableStartDate());
        $this->assertFalse($filter->isMonthlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(null);
        $filter->setEndDate(now()->endOfMonth());
        $this->assertNull($filter->getComparableEndDate());
        $this->assertNull($filter->getComparableStartDate());
        $this->assertFalse($filter->isMonthlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->startOfMonth());
        $filter->setEndDate(now()->endOfMonth());

        $this->assertTrue($filter->isMonthlyPeriod());
        $this->assertSame(
            Carbon::parse('2025-02-01')->toDateTimeString(),
            $filter->getComparableStartDate()->toDateTimeString()
        );
        $this->assertSame(
            Carbon::parse('2025-02-01')->endOfMonth()->toDateTimeString(),
            $filter->getComparableEndDate()->toDateTimeString()
        );
    }

    public function test_it_gives_false_for_incorrect_monthly_periods(): void
    {
        Carbon::setTestNow(
            now()->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subMonthNoOverflow());
        $filter->setEndDate(now());
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subMonthNoOverflow());
        $filter->setEndDate(null);
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(null);
        $filter->setEndDate(now());
        $this->assertFalse($filter->isYearlyPeriod());
    }

    public function test_it_gives_comparable_date_for_yearly_period(): void
    {
        Carbon::setTestNow(
            now()->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->startOfYear());
        $filter->setEndDate(null);
        $this->assertNull($filter->getComparableEndDate());
        $this->assertNull($filter->getComparableStartDate());
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(null);
        $filter->setEndDate(now()->endOfYear());
        $this->assertNull($filter->getComparableEndDate());
        $this->assertNull($filter->getComparableStartDate());
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->startOfYear());
        $filter->setEndDate(now()->endOfYear());

        $this->assertTrue($filter->isYearlyPeriod());
        $this->assertSame(
            now()->subYearNoOverflow()->startOfYear()->toDateTimeString(),
            $filter->getComparableStartDate()->toDateTimeString()
        );
        $this->assertSame(
            now()->subYearNoOverflow()->endOfYear()->toDateTimeString(),
            $filter->getComparableEndDate()->toDateTimeString()
        );
    }

    public function test_it_gives_false_for_incorrect_yearly_periods(): void
    {
        Carbon::setTestNow(
            now()->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subYearNoOverflow());
        $filter->setEndDate(now());
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subYearNoOverflow());
        $filter->setEndDate(null);
        $this->assertFalse($filter->isYearlyPeriod());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(null);
        $filter->setEndDate(now());
        $this->assertFalse($filter->isYearlyPeriod());
    }

    public function test_it_gives_correct_accuracy(): void
    {
        Carbon::setTestNow(
            now()->startOfDay()->setMinute(0)->setSecond(0)
        );

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now());
        $filter->setEndDate(null);
        $this->assertSame(WidgetAccuracy::MONTH, $filter->getAccuracy());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(null);
        $filter->setEndDate(now());
        $this->assertSame(WidgetAccuracy::MONTH, $filter->getAccuracy());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subHour());
        $filter->setEndDate(now());
        $this->assertSame(WidgetAccuracy::DAY, $filter->getAccuracy());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subHours(744));
        $filter->setEndDate(now());
        $this->assertSame(WidgetAccuracy::DAY, $filter->getAccuracy());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subDays(30));
        $filter->setEndDate(now());
        $this->assertSame(WidgetAccuracy::DAY, $filter->getAccuracy());

        $filter = new WidgetAnalyticsFilter;
        $filter->setStartDate(now()->subDays(35));
        $filter->setEndDate(now());
        $this->assertSame(WidgetAccuracy::MONTH, $filter->getAccuracy());

        $filter->setAccuracy(WidgetAccuracy::DAY);
        $this->assertSame(WidgetAccuracy::DAY, $filter->getAccuracy());
    }

    public function test_it_sets_channels(): void
    {
        $filter = new WidgetAnalyticsFilter;
        $filter->setChannels(['1', '2']);

        $this->assertIsArray($filter->getChannels());
        $this->assertContains('1', $filter->getChannels());
        $this->assertContains('2', $filter->getChannels());
    }

    public function test_it_sets_regions(): void
    {
        $filter = new WidgetAnalyticsFilter;
        $filter->setRegions(['1', '2']);

        $this->assertIsArray($filter->getRegions());
        $this->assertContains('1', $filter->getRegions());
        $this->assertContains('2', $filter->getRegions());
    }

    public function test_it_sets_business_units(): void
    {
        $filter = new WidgetAnalyticsFilter;
        $filter->setBusinessUnits(['1', '2']);

        $this->assertIsArray($filter->getBusinessUnits());
        $this->assertContains('1', $filter->getBusinessUnits());
        $this->assertContains('2', $filter->getBusinessUnits());
    }
}
