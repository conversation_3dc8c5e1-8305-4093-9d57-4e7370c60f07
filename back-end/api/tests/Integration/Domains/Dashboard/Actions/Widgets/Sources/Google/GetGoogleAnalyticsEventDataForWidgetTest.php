<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Actions\Widgets\Sources\Google;

use App\Domains\Dashboard\Actions\Widgets\Sources\Google\GetGoogleAnalyticsEventDataForWidget;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use Carbon\CarbonInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class GetGoogleAnalyticsEventDataForWidgetTest extends TestCase
{
    use RefreshDatabase;

    #[DataProvider('widgetTypeProvider')]
    public function test_single_returns_data_points(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now());

        $widget = $this->getWidget($widgetType);
        $attachedDataSource = $this->attachDataSourceToWidget($widget);
        $nonAttachedDataSource = $this->attachDataSourceToWidget($this->getWidget($widgetType));

        $dates = [
            [now()->subDay(), 1000],
            [now(), 2000],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $attachedDataSource,
                date: $date[0]->setTime(12, 0),
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $nonAttachedDataSource,
                date: $date[0]->setTime(12, 0),
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $this->getWidgetAnalyticsFilter());

        $dataPoints = $widgetAnalytics->getData();

        $this->assertCount(2, $dataPoints);

        $this->assertContainsOnlyInstancesOf(WidgetPieAnalyticsDataPoint::class, $dataPoints);
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_calculates_total_for_data_set(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);
        $attachedDataSource = $this->attachDataSourceToWidget($widget);
        $nonAttachedDataSource = $this->attachDataSourceToWidget($this->getWidget($widgetType));

        $dates = [
            [now(), 10],
            [now(), 10],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $attachedDataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $nonAttachedDataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $this->getWidgetAnalyticsFilter());

        $this->assertIsFloat($widgetAnalytics->getTotal());
        $this->assertSame(20.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_ignores_other_data_types(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);

        $dates = [
            [now(), 10],
            [now(), 10],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: ReportType::EVENT
            );
        }

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $this->getWidgetAnalyticsFilter());

        $this->assertIsFloat($widgetAnalytics->getTotal());
        $this->assertSame(20.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_formats_total(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $this->attachDataSourceToWidget($widget),
            date: now(),
            value: 1000000,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $this->getWidgetAnalyticsFilter());

        $this->assertIsString($widgetAnalytics->getTotalFormatted());
        $this->assertSame('1.000.000', $widgetAnalytics->getTotalFormatted());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_calculates_comparison_for_filtered_date_range(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now()->startOfDay());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);

        $baseDate = now()->subDay()->subSecond();
        $dates = [
            [$baseDate->copy()->subDays(2), 1], // Out of comparison range
            [$baseDate->copy()->subHour(), 2], // Within comparison range
            [$baseDate->copy()->subSecond(), 3], // Within comparison range
            [$baseDate->copy(), 4], // Within comparison range
            [$baseDate->copy()->addSecond(), 5], // Out of comparison range, Within normal range
            [$baseDate->copy()->addDay(), 6], // Out of comparison range, Within normal range
            [now()->subDays(2), 7], // Within comparison range
            [now()->subHour(), 8], // Within normal range
            [now()->subSecond(), 9], // Within normal range
            [now(), 10], // Within normal range
            [now()->addSecond(), 11], // Out of normal range
            [now()->addDay(), 12], // Out of normal range
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setStartDate(now()->subDay());
        $filter->setEndDate(now());

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertIsFloat($widgetAnalytics->getComparison());
        $this->assertSame(16.0, $widgetAnalytics->getComparison());
        $this->assertSame(38.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_formats_comparison(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now()->startOfDay());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $dataSource,
            date: now()->subDay()->subHour(),
            value: 50000,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setStartDate(now()->subDay());
        $filter->setEndDate(now());

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertIsFloat($widgetAnalytics->getComparison());
        $this->assertSame(50000.0, $widgetAnalytics->getComparison());
        $this->assertSame('50.000', $widgetAnalytics->getComparisonFormatted());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_filters_data_based_on_start_date(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now()->startOfDay());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);
        $startDate = now()->subWeek();

        $dates = [
            [$startDate->clone()->subSecond(), 1],
            [$startDate->clone()->subDay(), 1],
            [$startDate->clone(), 1],
            [$startDate->clone()->addSecond(), 2000],
            [$startDate->clone()->addDay(), 2000],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setStartDate($startDate);

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $dataPoints = $widgetAnalytics->getData();

        $this->assertCount(3, $dataPoints);
        $this->assertContainsOnlyInstancesOf(WidgetPieAnalyticsDataPoint::class, $dataPoints);

        $this->assertSame(4001.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_filters_data_based_on_end_date(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now()->startOfDay());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);
        $endDate = now()->subWeek();

        $dates = [
            [$endDate->clone()->subSecond(), 1],
            [$endDate->clone()->subDay(), 1],
            [$endDate->clone(), 1],
            [$endDate->clone()->addSecond(), 2000],
            [$endDate->clone()->addDay(), 2000],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setEndDate($endDate);

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $dataPoints = $widgetAnalytics->getData();

        $this->assertCount(3, $dataPoints);
        $this->assertContainsOnlyInstancesOf(WidgetPieAnalyticsDataPoint::class, $dataPoints);

        $this->assertSame(3.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_filters_data_based_on_channels(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);
        $inFilterDataSource = $this->attachDataSourceToWidget($widget);
        $outOfFilterDataSource = $this->attachDataSourceToWidget($widget);

        $inFilterDataSource->channel = 'channel';
        $inFilterDataSource->save();

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $inFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $outOfFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $filter = $this->getWidgetAnalyticsFilter();

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(2.0, $widgetAnalytics->getTotal());

        $filter->setChannels(['channel']);
        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(1.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_filters_data_based_on_regions(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);
        $inFilterDataSource = $this->attachDataSourceToWidget($widget);
        $outOfFilterDataSource = $this->attachDataSourceToWidget($widget);

        $inFilterDataSource->region = 'UK';
        $inFilterDataSource->save();

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $inFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $outOfFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $filter = $this->getWidgetAnalyticsFilter();

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(2.0, $widgetAnalytics->getTotal());

        $filter->setRegions(['UK']);
        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(1.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_single_filters_data_based_on_business_units(WidgetType $widgetType): void
    {
        $widget = $this->getWidget($widgetType);
        $inFilterDataSource = $this->attachDataSourceToWidget($widget);
        $outOfFilterDataSource = $this->attachDataSourceToWidget($widget);

        $inFilterDataSource->business_unit = 'business_unit';
        $inFilterDataSource->save();

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $inFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $this->createGoogleAnalyticsReportForDataSource(
            dataSource: $outOfFilterDataSource,
            date: now(),
            value: 1,
            reportType: $this->widgetTypeToReportType($widgetType)
        );

        $filter = $this->getWidgetAnalyticsFilter();

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(2.0, $widgetAnalytics->getTotal());

        $filter->setBusinessUnits(['business_unit']);
        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $this->assertSame(1.0, $widgetAnalytics->getTotal());
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_handles_month_accuracy_correct(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);

        $startOfMonth = now()->startOfMonth();

        $dates = [
            [$startOfMonth->clone()->addDay(), 1],
            [$startOfMonth->clone()->addDays(2), 2],
            [$startOfMonth->clone()->addDays(3), 3],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setAccuracy(WidgetAccuracy::MONTH);

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $dataPoints = $widgetAnalytics->getData();

        $this->assertCount(3, $dataPoints);
        $this->assertContainsOnlyInstancesOf(WidgetPieAnalyticsDataPoint::class, $dataPoints);
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_handles_day_accuracy_correct(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now());

        $widget = $this->getWidget($widgetType);
        $dataSource = $this->attachDataSourceToWidget($widget);

        $startOfMonth = now()->startOfMonth();

        $dates = [
            [$startOfMonth->clone()->addDay(), 1],
            [$startOfMonth->clone()->addDays(2), 2],
            [$startOfMonth->clone()->addDays(3), 3],
        ];

        foreach ($dates as $date) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: $date[0],
                value: $date[1],
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $filter = $this->getWidgetAnalyticsFilter();
        $filter->setAccuracy(WidgetAccuracy::DAY);

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $filter);

        $dataPoints = $widgetAnalytics->getData();

        $this->assertCount(3, $dataPoints);
        $this->assertContainsOnlyInstancesOf(WidgetPieAnalyticsDataPoint::class, $dataPoints);
    }

    #[DataProvider('widgetTypeProvider')]
    public function test_detail_groups_data_per_widget_detail_scope(WidgetType $widgetType): void
    {
        Carbon::setTestNow(now());

        $widget = $this->getWidget($widgetType);

        $firstRegion = $this->attachDataSourceToWidget($widget);
        $firstRegion->region = 'UK';
        $firstRegion->save();

        $secondRegion = $this->attachDataSourceToWidget($widget);
        $secondRegion->region = 'US';
        $secondRegion->save();

        $firstBusinessUnit = $this->attachDataSourceToWidget($widget);
        $firstBusinessUnit->business_unit = 'Unit 1';
        $firstBusinessUnit->save();

        $secondBusinessUnit = $this->attachDataSourceToWidget($widget);
        $secondBusinessUnit->business_unit = 'Unit 2';
        $secondBusinessUnit->save();

        $firstDataSource = $this->attachDataSourceToWidget($widget, 'First');
        $secondDataSource = $this->attachDataSourceToWidget($widget, 'Second');

        $dataSources = [
            $firstRegion,
            $secondRegion,
            $firstBusinessUnit,
            $secondBusinessUnit,
            $firstDataSource,
            $secondDataSource,
        ];

        foreach ($dataSources as $dataSource) {
            $this->createGoogleAnalyticsReportForDataSource(
                dataSource: $dataSource,
                date: now(),
                value: 3,
                reportType: $this->widgetTypeToReportType($widgetType)
            );
        }

        $widgetAnalytics = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->single($widget, $this->getWidgetAnalyticsFilter());

        // Single total value should equal all data sources and therefor is equal to the count * 3
        self::assertEquals(3 * count($dataSources), $widgetAnalytics->getTotal());

        // Region scope
        $widgetAnalyticsPerRegion = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->detail($widget, $this->getWidgetAnalyticsFilter(), WidgetDetailScope::REGION);

        self::assertCount(3, $widgetAnalyticsPerRegion);  // Has 3 regions, base factory also introduces a region
        self::assertEquals(12, $widgetAnalyticsPerRegion['NL']->getTotal());
        self::assertEquals(3, $widgetAnalyticsPerRegion['US']->getTotal());
        self::assertEquals(3, $widgetAnalyticsPerRegion['UK']->getTotal());
        self::assertContainsOnlyInstancesOf(WidgetPieAnalytics::class, $widgetAnalyticsPerRegion);

        // Business unit scope
        $widgetAnalyticsPerBusinessUnit = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->detail($widget, $this->getWidgetAnalyticsFilter(), WidgetDetailScope::BUSINESS_UNIT);

        self::assertCount(3, $widgetAnalyticsPerBusinessUnit);  // Has 3 units, base factory also introduces a unit
        self::assertEquals(12, $widgetAnalyticsPerBusinessUnit['Global']->getTotal());
        self::assertEquals(3, $widgetAnalyticsPerBusinessUnit['Unit 1']->getTotal());
        self::assertEquals(3, $widgetAnalyticsPerBusinessUnit['Unit 2']->getTotal());
        self::assertContainsOnlyInstancesOf(WidgetPieAnalytics::class, $widgetAnalyticsPerBusinessUnit);

        // Data source
        $widgetAnalyticsPerDataSource = app(GetGoogleAnalyticsEventDataForWidget::class)
            ->detail($widget, $this->getWidgetAnalyticsFilter(), WidgetDetailScope::DATASOURCE);

        self::assertCount(6, $widgetAnalyticsPerDataSource);  // Has 6 sources, base factory creates a unique title
        self::assertEquals(3, $widgetAnalyticsPerDataSource['First']->getTotal());
        self::assertEquals(3, $widgetAnalyticsPerDataSource['Second']->getTotal());
        self::assertContainsOnlyInstancesOf(WidgetPieAnalytics::class, $widgetAnalyticsPerDataSource);
    }

    protected function getWidget(WidgetType $widgetType): Widget
    {
        $dashboard = Dashboard::factory()->create();
        $section = Section::factory()
            ->for($dashboard)
            ->create();

        return Widget::factory()
            ->asType($widgetType)
            ->for($section)
            ->create();
    }

    protected function attachDataSourceToWidget(Widget $widget, ?string $title = null): DataSource
    {
        $googleAccount = GoogleAccount::factory()->create();
        $googleAnalyticsAccount = GoogleAnalyticsAccount::factory()
            ->for($googleAccount)
            ->create();
        $googleAnalyticsProperty = GoogleAnalyticsProperty::factory()
            ->for($googleAnalyticsAccount)
            ->create();

        $attributes = [];

        if ($title) {
            $attributes['title'] = $title;
        }

        return DataSource::factory()
            ->withDashboard($widget->section->dashboard)
            ->forSourceable($googleAnalyticsProperty)
            ->create($attributes);
    }

    protected function createGoogleAnalyticsReportForDataSource(
        DataSource $dataSource,
        CarbonInterface $date,
        int $value,
        ReportType $reportType
    ): void {
        GoogleAnalyticsReport::factory()
            ->asType($reportType)
            ->withName()
            ->for($dataSource->sourceable)
            ->create([
                'date' => $date,
                'value' => $value,
            ]);
    }

    protected function getWidgetAnalyticsFilter(): WidgetAnalyticsFilter
    {
        $filter = new WidgetAnalyticsFilter;
        $filter->setAccuracy(WidgetAccuracy::DAY);

        return $filter;
    }

    public function widgetTypeToReportType(WidgetType $widgetType): ReportType
    {
        return match ($widgetType) {
            WidgetType::GOOGLE_ANALYTICS_CHANNEL_GROUPS => ReportType::CHANNEL_GROUP,
        };
    }

    public static function widgetTypeProvider(): array
    {
        return [
            [WidgetType::GOOGLE_ANALYTICS_CHANNEL_GROUPS],
        ];
    }
}
