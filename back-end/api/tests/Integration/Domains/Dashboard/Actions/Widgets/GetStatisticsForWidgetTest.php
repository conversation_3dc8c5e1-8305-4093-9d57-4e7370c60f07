<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Actions\Widgets;

use App\Domains\Dashboard\Actions\Widgets\GetStatisticsForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Google\GetGoogleAnalyticsDataForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class GetStatisticsForWidgetTest extends TestCase
{
    use RefreshDatabase;

    #[DataProvider('dataTypes')]
    public function test_single_returns_correct_type(WidgetType $widgetType, string $action): void
    {
        $widget = Widget::factory()
            ->asType($widgetType)
            ->create();
        $widgetAnalyticsFilter = new WidgetAnalyticsFilter;

        $this->mock($action)
            ->shouldReceive('single')
            ->withArgs([$widget, $widgetAnalyticsFilter])
            ->once();

        app(GetStatisticsForWidget::class)
            ->single($widget, $widgetAnalyticsFilter);
    }

    #[DataProvider('dataTypes')]
    public function test_detail_returns_correct_type(WidgetType $widgetType, string $action): void
    {
        $widget = Widget::factory()
            ->asType($widgetType)
            ->create();
        $widgetAnalyticsFilter = new WidgetAnalyticsFilter;

        foreach (WidgetDetailScope::cases() as $detailScope) {
            $this->mock($action)
                ->shouldReceive('detail')
                ->withArgs([$widget, $widgetAnalyticsFilter, $detailScope])
                ->once();

            app(GetStatisticsForWidget::class)
                ->detail($widget, $widgetAnalyticsFilter, $detailScope);
        }
    }

    public static function dataTypes(): array
    {
        return [
            [WidgetType::GOOGLE_ANALYTICS_SESSION, GetGoogleAnalyticsDataForWidget::class],
            [WidgetType::GOOGLE_ANALYTICS_BOOKINGS, GetGoogleAnalyticsDataForWidget::class],
        ];
    }
}
