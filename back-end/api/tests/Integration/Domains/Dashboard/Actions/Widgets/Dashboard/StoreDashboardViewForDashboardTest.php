<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Actions\Widgets\Dashboard;

use App\Domains\Dashboard\Actions\Dashboard\StoreDashboardViewForDashboard;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreDashboardViewForDashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_execute_returns_correct_type(): void
    {
        $dashboard = Dashboard::factory()->create();
        $widgetAnalyticsFilter = Mockery::mock(WidgetAnalyticsFilter::class);

        $widgetAnalyticsFilter
            ->shouldReceive('getStartDate')
            ->andReturn(now()->subDay())
            ->once();

        $widgetAnalyticsFilter
            ->shouldReceive('getEndDate')
            ->andReturn(now())
            ->once();

        $widgetAnalyticsFilter
            ->shouldReceive('getChannels')
            ->andReturn(['channel'])
            ->once();

        $widgetAnalyticsFilter
            ->shouldReceive('getRegions')
            ->andReturn(['BE'])
            ->once();

        $widgetAnalyticsFilter
            ->shouldReceive('getBusinessUnits')
            ->andReturn(['business_unit'])
            ->once();

        $view = app(StoreDashboardViewForDashboard::class)
            ->execute(
                dashboard: $dashboard,
                name: 'Name',
                widgetAnalyticsFilter: $widgetAnalyticsFilter
            );

        $this->assertInstanceOf(DashboardView::class, $view);

        $this->assertDatabaseHas('dashboard_views', [
            'dashboard_id' => $dashboard->id,
            'name' => 'Name',
            'start_date' => now()->subDay()->toDateTimeString(),
            'end_date' => now()->toDateTimeString(),
            'channels' => json_encode(['channel']),
            'regions' => json_encode(['BE']),
            'business_units' => json_encode(['business_unit']),
        ]);
    }
}
