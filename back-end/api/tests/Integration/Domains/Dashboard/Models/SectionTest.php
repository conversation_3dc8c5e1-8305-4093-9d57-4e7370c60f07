<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SectionTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_dashboard(): void
    {
        $section = Section::factory()
            ->for(Dashboard::factory())
            ->create();

        $this->assertInstanceOf(Dashboard::class, $section->dashboard);
    }

    public function test_it_has_many_widgets(): void
    {
        $section = Section::factory()
            ->has(Widget::factory()->count(3))
            ->create();

        $this->assertCount(3, $section->widgets);
        $this->assertInstanceOf(Widget::class, $section->widgets->first());
    }
}
