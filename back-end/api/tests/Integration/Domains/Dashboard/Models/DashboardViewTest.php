<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DashboardViewTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_dashboard(): void
    {
        $view = DashboardView::factory()
            ->for(Dashboard::factory())
            ->create();

        $this->assertInstanceOf(Dashboard::class, $view->dashboard);
    }
}
