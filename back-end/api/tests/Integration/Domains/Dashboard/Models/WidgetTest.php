<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDataType;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WidgetTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_casts_type(): void
    {
        $widget = Widget::factory()
            ->create();

        $this->assertInstanceOf(WidgetType::class, $widget->type);
    }

    public function test_it_casts_data_type(): void
    {
        $widget = Widget::factory()
            ->create();

        $this->assertInstanceOf(WidgetDataType::class, $widget->data_type);
    }

    public function test_it_belongs_to_section(): void
    {
        $widget = Widget::factory()
            ->for(Section::factory())
            ->create();

        $this->assertInstanceOf(Section::class, $widget->section);
    }
}
