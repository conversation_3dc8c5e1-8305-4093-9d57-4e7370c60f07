<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Models;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Dashboard\Models\Section;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_limits_dashboards_to_user_dashboards(): void
    {
        $dashboard = Dashboard::factory()->create();

        $user = User::factory()->create();
        self::assertEmpty($user->dashboards);

        Sanctum::actingAs($user);

        self::assertCount(0, Dashboard::query()->get());
        self::assertCount(1, Dashboard::query()->withoutGlobalScope('user_dashboards')->get());

        $user->dashboards = [$dashboard->id];
        $user->save();

        self::assertCount(1, Dashboard::query()->get());
    }

    public function test_it_has_many_data_sources(): void
    {
        $dashboard = Dashboard::factory()
            ->has(DataSource::factory()->count(3))
            ->create();

        $this->assertCount(3, $dashboard->dataSources);
        $this->assertInstanceOf(DataSource::class, $dashboard->dataSources->first());
    }

    public function test_it_has_many_sections(): void
    {
        $dashboard = Dashboard::factory()
            ->has(Section::factory()->count(3))
            ->create();

        $this->assertCount(3, $dashboard->sections);
        $this->assertInstanceOf(Section::class, $dashboard->sections->first());
    }

    public function test_it_has_many_dashboard_views(): void
    {
        $dashboard = Dashboard::factory()
            ->has(DashboardView::factory()->count(3))
            ->create();

        $this->assertCount(3, $dashboard->dashboardViews);
        $this->assertInstanceOf(DashboardView::class, $dashboard->dashboardViews->first());
    }
}
