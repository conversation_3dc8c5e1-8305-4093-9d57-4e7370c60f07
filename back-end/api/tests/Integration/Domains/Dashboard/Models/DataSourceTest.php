<?php

declare(strict_types=1);

namespace Tests\Integration\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Arr;
use Tests\TestCase;

class DataSourceTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_many_dashboards(): void
    {
        $dataSource = DataSource::factory()
            ->has(Dashboard::factory()->count(3))
            ->create();

        $this->assertCount(3, $dataSource->dashboards);
        $this->assertInstanceOf(Dashboard::class, $dataSource->dashboards->first());
    }

    public function test_it_belongs_to_google_analytics_property(): void
    {
        $dataSource = DataSource::factory()
            ->forSourceable(GoogleAnalyticsProperty::factory()->create())
            ->create();

        $this->assertInstanceOf(GoogleAnalyticsProperty::class, $dataSource->sourceable);
    }

    public function test_it_casts_options(): void
    {
        $dataSource = DataSource::factory()
            ->create();

        $this->assertNull($dataSource->options);

        $dataSource = DataSource::factory()
            ->create([
                'options' => ['key' => ['child' => 'value']],
            ]);

        $this->assertIsArray($dataSource->options);
        $this->assertNotNull(Arr::get($dataSource->options, 'key.child'));

        $dataSource->options = ['test' => 1];

        $this->assertIsArray($dataSource->options);
        $this->assertSame(1, Arr::get($dataSource->options, 'test'));
    }
}
