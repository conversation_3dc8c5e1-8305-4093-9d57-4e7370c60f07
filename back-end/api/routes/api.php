<?php

use App\Domains\Authentication\Support\Enums\Role;
use App\Http\Authentication\Controllers\LoginController;
use App\Http\Authentication\Controllers\PasswordController;
use App\Http\Authentication\Controllers\TwoFactorController;
use App\Http\Authentication\Controllers\UserController;
use App\Http\Dashboard\Controllers\DashboardController;
use App\Http\Dashboard\Controllers\DashboardViewController;
use App\Http\Dashboard\Controllers\FileController;
use App\Http\Dashboard\Controllers\PostAnalyticsController;
use App\Http\Dashboard\Controllers\TargetController;
use App\Http\Dashboard\Controllers\WidgetController;
use App\Http\Dashboard\Controllers\WidgetDetailController;
use App\Http\Settings\Controllers\FacebookConnectionController;
use App\Http\Settings\Controllers\GoogleConnectionController;
use App\Http\Settings\Controllers\HelpdeskController;
use App\Http\Settings\Controllers\LinkedInAdsConnectionController;
use App\Http\Settings\Controllers\LinkedInCommunityConnectionController;
use App\Http\Settings\Controllers\MailchimpConnectionController;
use App\Http\Settings\Controllers\TikTokBusinessConnectionController;
use App\Http\Settings\Controllers\TikTokConnectionController;
use App\Http\Settings\Controllers\YoutubeConnectionController;
use App\Http\Sources\Facebook\Controllers\AuthenticationController as FacebookAuthenticationController;
use App\Http\Sources\Google\Controllers\AuthenticationController as GoogleAuthenticationController;
use App\Http\Sources\LinkedIn\Controllers\AuthenticationController as LinkedInAuthenticationController;
use App\Http\Sources\TikTok\Controllers\AuthenticationController as TikTokAuthenticationController;
use App\Http\Sources\TikTokBusiness\Controllers\AuthenticationController as TikTokBusinessAuthenticationController;
use App\Http\Sources\Youtube\Controllers\AuthenticationController as YoutubeAuthenticationController;
use App\Support\Middleware\EnsureTwoFactorAuth;
use App\Support\Middleware\RoleMiddleware;
use Illuminate\Support\Facades\Route;

// Authentication
Route::prefix('auth')
    ->name('auth.')
    ->group(function () {
        Route::middleware('auth:sanctum')
            ->group(function () {
                Route::get('me', [LoginController::class, 'me'])->name('me');
                Route::get('logout', [LoginController::class, 'logout'])->name('logout');
                Route::post('2fa', [TwoFactorController::class, 'verify'])->name('verify');
            });

        Route::post('login', [LoginController::class, 'login'])->name('login');

        Route::prefix('password')
            ->name('password.')
            ->group(function () {
                Route::post('request', [PasswordController::class, 'request'])->name('request');
                Route::post('reset', [PasswordController::class, 'reset'])->name('reset');
            });
    });

// Sources
Route::prefix('sources')
    ->name('sources.')
    ->group(function () {
        // Google
        Route::prefix('google')
            ->name('google.')
            ->group(function () {
                Route::get('connect', [GoogleAuthenticationController::class, 'connect'])->name('connect');
                Route::get('callback', [GoogleAuthenticationController::class, 'callback'])->name('callback');
            });

        // Youtube
        Route::prefix('youtube')
            ->name('youtube.')
            ->group(function () {
                Route::get('connect', [YoutubeAuthenticationController::class, 'connect'])->name('connect');
                Route::get('callback', [YoutubeAuthenticationController::class, 'callback'])->name('callback');
            });

        // LinkedIn
        Route::prefix('linkedin')
            ->name('linkedin.')
            ->group(function () {
                Route::get('connectCommunity', [LinkedInAuthenticationController::class, 'connectCommunity'])->name('connectCommunity');
                Route::get('callbackCommunity', [LinkedInAuthenticationController::class, 'callbackCommunity'])->name('callbackCommunity');
                Route::get('connectAds', [LinkedInAuthenticationController::class, 'connectAds'])->name('connectAds');
                Route::get('callbackAds', [LinkedInAuthenticationController::class, 'callbackAds'])->name('callbackAds');
            });

        // Facebook
        Route::prefix('facebook')
            ->name('facebook.')
            ->group(function () {
                Route::get('connect', [FacebookAuthenticationController::class, 'connect'])->name('connect');
                Route::get('callback', [FacebookAuthenticationController::class, 'callback'])->name('callback');
            });

        // TikTok
        Route::prefix('tiktok')
            ->name('tiktok.')
            ->group(function () {
                Route::get('connect', [TikTokAuthenticationController::class, 'connect'])->name('connect');
                Route::get('callback', [TikTokAuthenticationController::class, 'callback'])->name('callback');
            });

        // TikTok Business
        Route::prefix('tiktok-business')
            ->name('tiktok-business.')
            ->group(function () {
                Route::get('connect', [TikTokBusinessAuthenticationController::class, 'connect'])->name('connect');
                Route::get('callback', [TikTokBusinessAuthenticationController::class, 'callback'])->name('callback');
            });
    });

// Dashboard
Route::prefix('dashboard')
    ->name('dashboard.')
    ->middleware(['auth:sanctum', EnsureTwoFactorAuth::class])
    ->group(function () {
        // Dashboard
        Route::get('', [DashboardController::class, 'index'])->name('index');

        Route::prefix('{dashboard}')
            ->group(function () {
                Route::get('', [DashboardController::class, 'show'])->name('show');

                // Dashboard View
                Route::prefix('/view')
                    ->name('view.')
                    ->group(function () {
                        Route::post('', [DashboardViewController::class, 'store'])->name('store');
                        Route::delete('{dashboard_view}', [DashboardViewController::class, 'destroy'])->name('destroy');
                    });
            });

        // Widget
        Route::prefix('widget/{widget}')
            ->name('widget.')
            ->group(function () {
                Route::get('statistics', [WidgetController::class, 'statistics'])->name('statistics');

                // Target
                Route::prefix('/target')
                    ->name('target.')
                    ->group(function () {
                        Route::patch('', [TargetController::class, 'update'])->name('update');
                        Route::delete('', [TargetController::class, 'destroy'])->name('destroy');
                    });

                // Detail
                Route::prefix('/detail')
                    ->name('detail.')
                    ->group(function () {
                        Route::get('statistics/{widgetDetailScope}', [WidgetDetailController::class, 'statistics'])->name('statistics');
                        Route::get('posts/{postId}/statistics', [PostAnalyticsController::class, 'statistics'])->name('posts.statistics');
                    });
            });

        // File
        Route::prefix('file}')
            ->name('file.')
            ->group(function () {
                Route::get('{path}', [FileController::class, 'show'])
                    ->where('path', '.*')
                    ->name('show');
            });
    });

// Users
Route::prefix('user')
    ->name('user.')
    ->middleware(['auth:sanctum', EnsureTwoFactorAuth::class, RoleMiddleware::init(Role::ADMIN)])
    ->group(function () {
        Route::get('', [UserController::class, 'index'])->name('index');
        Route::get('{user}', [UserController::class, 'show'])->name('show');
        Route::post('', [UserController::class, 'store'])->name('store');
        Route::patch('{user}', [UserController::class, 'update'])->name('update');
        Route::delete('{user}', [UserController::class, 'destroy'])->name('destroy');
    });

// Settings
Route::prefix('settings')
    ->name('settings.')
    ->middleware(['auth:sanctum', EnsureTwoFactorAuth::class, RoleMiddleware::init(Role::ADMIN)])
    ->group(function () {
        // Helpdesk
        Route::prefix('/helpdesk')
            ->name('helpdesk.')
            ->group(function () {
                Route::post('request', [HelpdeskController::class, 'store'])->name('store');
            });

        // Google
        Route::prefix('/connections/google')
            ->name('connections.google.')
            ->group(function () {
                Route::get('', [GoogleConnectionController::class, 'index'])->name('index');
                Route::get('{googleAccount}', [GoogleConnectionController::class, 'show'])->name('show');
                Route::delete('{googleAccount}', [GoogleConnectionController::class, 'destroy'])->name('destroy');
            });

        // Youtube
        Route::prefix('/connections/youtube')
            ->name('connections.youtube.')
            ->group(function () {
                Route::get('', [YoutubeConnectionController::class, 'index'])->name('index');
                Route::get('{youtubeGoogleAccount}', [YoutubeConnectionController::class, 'show'])->name('show');
                Route::delete('{youtubeGoogleAccount}', [YoutubeConnectionController::class, 'destroy'])->name('destroy');
            });

        // Facebook
        Route::prefix('/connections/facebook')
            ->name('connections.facebook.')
            ->group(function () {
                Route::get('', [FacebookConnectionController::class, 'index'])->name('index');
                Route::get('{facebookAccount}', [FacebookConnectionController::class, 'show'])->name('show');
                Route::delete('{facebookAccount}', [FacebookConnectionController::class, 'destroy'])->name('destroy');
            });

        // MailChimp
        Route::prefix('/connections/mailchimp')
            ->name('connections.mailchimp.')
            ->group(function () {
                Route::get('', [MailchimpConnectionController::class, 'index'])->name('index');
                Route::delete('{mailchimpAccount}', [MailchimpConnectionController::class, 'destroy'])->name('destroy');
            });

        // LinkedIn Ads
        Route::prefix('/connections/linked_in/ads')
            ->name('connections.linked_in.ads.')
            ->group(function () {
                Route::get('', [LinkedInAdsConnectionController::class, 'index'])->name('index');
                Route::get('{linkedInAccount}', [LinkedInAdsConnectionController::class, 'show'])->name('show');
                Route::delete('{linkedInAccount}', [LinkedInAdsConnectionController::class, 'destroy'])->name('destroy');
            });

        // LinkedIn Community
        Route::prefix('/connections/linked_in/community')
            ->name('connections.linked_in.community.')
            ->group(function () {
                Route::get('', [LinkedInCommunityConnectionController::class, 'index'])->name('index');
                Route::get('{linkedInAccount}', [LinkedInCommunityConnectionController::class, 'show'])->name('show');
                Route::delete('{linkedInAccount}', [LinkedInCommunityConnectionController::class, 'destroy'])->name('destroy');
            });

        // TikTok Organic
        Route::prefix('/connections/tiktok/organic')
            ->name('connections.tiktok.organic.')
            ->group(function () {
                Route::get('', [TikTokConnectionController::class, 'index'])->name('index');
                Route::delete('{tikTokAccount}', [TikTokConnectionController::class, 'destroy'])->name('destroy');
            });

        // TikTok Business
        Route::prefix('/connections/tiktok/business')
            ->name('connections.tiktok.business.')
            ->group(function () {
                Route::get('', [TikTokBusinessConnectionController::class, 'index'])->name('index');
                Route::delete('{tiktokBusinessAccount}', [TikTokBusinessConnectionController::class, 'destroy'])->name('destroy');
            });
    });
