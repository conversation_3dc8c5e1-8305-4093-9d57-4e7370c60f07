<?php

declare(strict_types=1);

use App\Domains\Sources\Google\Jobs\SyncGoogleAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAdCampaigns;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperties;
use App\Domains\Sources\Google\Jobs\VerifyScopesForGoogleAccounts;
use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAdAccounts;
use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Jobs\VerifyUnSyncedGoogleAnalyticProperties;
use App\Domains\Sources\LinkedIn\Jobs\SyncIncompleteLinkedInOrganisations;
use App\Domains\Sources\LinkedIn\Jobs\SyncLinkedInAccounts;
use App\Domains\Sources\LinkedIn\Jobs\SyncLinkedInAdAccounts;
use App\Domains\Sources\LinkedIn\Jobs\SyncLinkedInOrganisations;
use App\Domains\Sources\Localium\Jobs\SyncGoogleBusinessProfileReports;
use App\Domains\Sources\Localium\Jobs\SyncLocaliumAccounts;
use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccounts;
use App\Domains\Sources\Mailchimp\Jobs\VerifyUnconnectedMailchimpAudiences;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramAccountInsightTotalInteractions;
use App\Domains\Sources\Meta\Jobs\Page\SyncFacebookPosts;
use App\Domains\Sources\Meta\Jobs\SyncFacebookAccounts;
use App\Domains\Sources\Meta\Jobs\SyncFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\SyncFacebookBusinesses;
use App\Domains\Sources\Meta\Jobs\SyncFacebookPages;
use App\Domains\Sources\Meta\Jobs\SyncInstagramAccounts;
use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookBusinesses;
use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookPageInsights;
use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedFacebookPages;
use App\Domains\Sources\Meta\Jobs\VerifyUnSyncedInstagramAccountInsights;
use App\Domains\Sources\TikTok\Jobs\SyncTikTokAccounts;
use App\Domains\Sources\Youtube\Jobs\DispatchYoutubeVideoSync;
use App\Domains\Sources\Youtube\Jobs\SyncYoutubeChannels;
use App\Domains\Sources\Youtube\Jobs\SyncYoutubeGoogleAccounts;
use App\Domains\Sources\Youtube\Jobs\VerifyScopesForYoutubeGoogleAccounts;

// Auth
Schedule::command('auth:clear-resets')->daily();

// Sources: Google
Schedule::job(new VerifyScopesForGoogleAccounts)->daily();
Schedule::job(new VerifyUnSyncedGoogleAnalyticAccounts)->daily();
Schedule::job(new VerifyUnSyncedGoogleAdAccounts)->daily();
Schedule::job(new VerifyUnSyncedGoogleAnalyticProperties)->daily();
Schedule::job(new SyncGoogleAccounts)->dailyAt('02:00');
Schedule::job(new SyncGoogleAnalyticAccounts)->dailyAt('03:00');
Schedule::job(new SyncGoogleAnalyticProperties)->dailyAt('04:00');
Schedule::job(new SyncGoogleAdCampaigns)->dailyAt('05:00');

// Sources: YouTube
Schedule::job(new VerifyScopesForYoutubeGoogleAccounts)->daily();
Schedule::job(new SyncYoutubeGoogleAccounts)->dailyAt('01:00');
Schedule::job(new SyncYoutubeChannels)->dailyAt('01:30');
Schedule::job(new DispatchYoutubeVideoSync)->dailyAt('02:30');

// Sources: LinkedIn
Schedule::job(new SyncLinkedInAccounts)->dailyAt('01:00');
Schedule::job(new SyncIncompleteLinkedInOrganisations)->dailyAt('02:00');
Schedule::job(new SyncLinkedInAdAccounts)->dailyAt('02:00');
Schedule::job(new SyncLinkedInOrganisations)->dailyAt('02:00');

// Sources: Mailchimp
Schedule::job(new SyncMailchimpAccounts)->dailyAt('02:00');
Schedule::job(new VerifyUnconnectedMailchimpAudiences)->dailyAt('04:00');

// Sources: TikTok - Organic
Schedule::job(new SyncTikTokAccounts)->dailyAt('02:00');

// Sources: Localium
Schedule::job(new SyncLocaliumAccounts)->dailyAt('01:00');
Schedule::job(new SyncGoogleBusinessProfileReports)->dailyAt('01:30');

// Sources: Facebook
Schedule::job(new VerifyUnSyncedFacebookBusinesses)->daily();
Schedule::job(new VerifyUnSyncedFacebookPages)->daily();
Schedule::job(new VerifyUnSyncedFacebookPageInsights)->daily();
Schedule::job(new VerifyUnSyncedFacebookAdAccounts)->daily();
Schedule::job(new VerifyUnSyncedInstagramAccountInsights)->daily();
Schedule::job(new SyncFacebookAccounts)->dailyAt('01:00');
Schedule::job(new SyncFacebookBusinesses)->dailyAt('02:00');
Schedule::job(new SyncFacebookPages)->dailyAt('03:00');
Schedule::job(new SyncFacebookPosts)->dailyAt('03:30');
Schedule::job(new SyncFacebookAdAccounts)->dailyAt('03:30');
Schedule::job(new SyncInstagramAccounts)->dailyAt('03:30');
Schedule::job(new SyncInstagramAccountInsightTotalInteractions)->dailyAt('05:00');
