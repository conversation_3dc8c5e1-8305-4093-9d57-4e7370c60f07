<?php

declare(strict_types=1);

use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('mailchimp_campaigns', function (Blueprint $table) {
            $table->longText('title')->after('external_id');
            $table->timestamp('send_time')->nullable()->after('status');
        });

        MailchimpReport::query()->delete();
        MailchimpCampaign::query()->delete();
    }

    public function down(): void
    {
        Schema::table('mailchimp_campaigns', function (Blueprint $table) {
            $table->dropColumn(['title', 'send_time']);
        });
    }
};
