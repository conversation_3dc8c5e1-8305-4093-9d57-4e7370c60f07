<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('dashboard_views', function (Blueprint $table) {
            $table->id();

            $table->string('name');

            $table->foreignId('dashboard_id')
                ->constrained('dashboards')
                ->cascadeOnDelete();

            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->json('regions')->nullable();
            $table->json('channels')->nullable();
            $table->json('business_units')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('dashboard_views');
    }
};
