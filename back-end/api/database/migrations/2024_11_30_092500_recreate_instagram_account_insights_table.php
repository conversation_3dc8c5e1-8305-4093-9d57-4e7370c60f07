<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('instagram_account_insights');

        Schema::create('instagram_account_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('instagram_account_id')
                ->constrained('instagram_accounts')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('followers')->default(0);
            $table->integer('reach')->default(0);
            $table->integer('impressions')->default(0);
            $table->integer('total_interactions')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instagram_account_insights');

        Schema::create('instagram_account_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('instagram_account_id')
                ->constrained('instagram_accounts')
                ->cascadeOnDelete();

            $table->string('name');
            $table->timestamp('date');
            $table->integer('value');

            $table->timestamps();
            $table->softDeletes();
        });
    }
};
