<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_page_post_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_page_post_id')
                ->constrained('facebook_page_posts')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('post_clicks')->default(0);
            $table->integer('post_impressions_unique')->default(0);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_page_post_insights');
    }
};
