<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('mailchimp_audiences', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('mailchimp_audiences', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });
    }
};
