<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('data_sources', function (Blueprint $table) {
            $table->id();

            $table->string('title');

            $table->unsignedBigInteger('sourceable_id')->nullable();
            $table->string('sourceable_type')->nullable();
            $table->index(['sourceable_id', 'sourceable_type']);

            $table->json('options')->nullable();
            $table->string('region', 5);
            $table->string('business_unit');
            $table->string('channel');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('data_sources');
    }
};
