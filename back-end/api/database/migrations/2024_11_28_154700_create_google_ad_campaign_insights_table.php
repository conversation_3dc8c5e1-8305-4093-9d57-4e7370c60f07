<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_ad_campaign_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('google_ad_campaign_id')
                ->constrained('google_ad_campaigns')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('clicks')->default(0);
            $table->integer('video_views')->default(0);
            $table->integer('ctr')->default(0);
            $table->integer('impressions')->default(0);
            $table->string('average_cpc')->default('0');
            $table->string('average_cpm')->default('0');
            $table->string('spend')->default('0');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_campaign_insights');
    }
};
