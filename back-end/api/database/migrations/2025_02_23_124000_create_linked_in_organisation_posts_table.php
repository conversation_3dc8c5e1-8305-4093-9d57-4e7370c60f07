<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('linked_in_organisation_posts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('linkedin_organisation_id')
                ->constrained('linked_in_organisations')
                ->onDelete('cascade');

            $table->timestamp('date');
            $table->string('external_id');
            $table->string('state');
            $table->string('visibility');
            $table->text('commentary_excerpt');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_organisation_posts');
    }
};
