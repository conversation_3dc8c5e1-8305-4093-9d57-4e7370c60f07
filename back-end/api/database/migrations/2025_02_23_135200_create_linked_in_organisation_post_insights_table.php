<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('linked_in_organisation_post_insights', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('linkedin_organisation_post_id');

            $table->timestamp('date');
            $table->integer('unique_impressions_count')->default(0);
            $table->integer('share_count')->default(0);
            $table->decimal('engagement', 8, 4)->default(0);
            $table->integer('click_count')->default(0);
            $table->integer('like_count')->default(0);
            $table->integer('impression_count')->default(0);
            $table->integer('comment_count')->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('linkedin_organisation_post_id', 'li_org_reports_profile_id_foreign')
                ->references('id')
                ->on('linked_in_organisation_posts')
                ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_organisation_post_insights');
    }
};
