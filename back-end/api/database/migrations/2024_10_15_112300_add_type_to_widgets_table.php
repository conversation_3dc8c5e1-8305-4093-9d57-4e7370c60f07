<?php

declare(strict_types=1);

use App\Domains\Dashboard\Support\Enums\Widget\WidgetDataType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            $table->string('data_type')
                ->after('id')
                ->default(WidgetDataType::COLUMN_CHART);
        });
    }

    public function down(): void
    {
        Schema::table('widgets', function (Blueprint $table) {
            $table->dropColumn('data_type');
        });
    }
};
