<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mailchimp_campaigns', function (Blueprint $table) {
            $table->id();

            $table->foreignId('mailchimp_account_id')
                ->constrained('mailchimp_accounts')
                ->cascadeOnDelete();

            $table->string('external_id');
            $table->string('status');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mailchimp_campaigns');
    }
};
