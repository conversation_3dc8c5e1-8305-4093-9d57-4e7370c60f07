<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('google_business_profiles', function (Blueprint $table) {
            $table->foreignId('localium_account_id')
                ->nullable()
                ->after('id')
                ->constrained('localium_accounts')
                ->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('google_business_profiles', function (Blueprint $table) {
            $table->dropConstrainedForeignId('localium_account_id');
        });
    }
};
