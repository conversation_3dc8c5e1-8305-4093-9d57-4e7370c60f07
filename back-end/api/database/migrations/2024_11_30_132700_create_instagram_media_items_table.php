<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('instagram_media_items', function (Blueprint $table) {
            $table->id();

            $table->foreignId('instagram_account_id')
                ->constrained('instagram_accounts')
                ->cascadeOnDelete();

            $table->string('external_id');
            $table->timestamp('external_date');

            $table->string('media_type');
            $table->longText('media_url');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instagram_media_items');
    }
};
