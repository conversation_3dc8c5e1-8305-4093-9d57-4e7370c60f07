<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('linked_in_ad_accounts', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('linked_in_organisations', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });
    }

    public function down(): void
    {
        Schema::table('linked_in_ad_accounts', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('linked_in_organisations', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });
    }
};
