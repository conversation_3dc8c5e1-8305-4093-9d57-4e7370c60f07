<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('tik_tok_business_accounts');

        Schema::create('tik_tok_business_accounts', function (Blueprint $table) {
            $table->id();

            $table->string('external_id');
            $table->string('name')->nullable();
            $table->text('email')->nullable();
            $table->text('image_url')->nullable();

            $table->longText('token')->nullable();
            $table->timestamp('token_created_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tik_tok_business_accounts');
    }
};
