<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('linked_in_organisation_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('linkedin_organisation_id')
                ->constrained('linked_in_organisations')
                ->onDelete('cascade');

            $table->timestamp('date');

            $table->integer('clicks')->default(0);
            $table->integer('views')->default(0);
            $table->integer('followers')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_organisation_insights');
    }
};
