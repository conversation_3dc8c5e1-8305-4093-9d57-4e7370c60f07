<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tik_tok_video_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('tik_tok_video_id')
                ->constrained('tik_tok_videos')
                ->onDelete('cascade');

            $table->timestamp('date');
            $table->integer('view_count')->default(0);
            $table->integer('comment_count')->default(0);
            $table->integer('share_count')->default(0);
            $table->integer('like_count')->default(0);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tik_tok_video_insights');
    }
};
