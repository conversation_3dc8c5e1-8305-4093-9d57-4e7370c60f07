<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('linked_in_ad_campaigns', function (Blueprint $table) {
            $table->id();
            $table->foreignId('linkedin_ad_account_id')
                ->constrained('linked_in_ad_accounts')
                ->onDelete('cascade');

            $table->string('external_id');
            $table->string('name');
            $table->string('status');
            $table->string('type');
            $table->string('objective_type')->nullable();
            $table->decimal('total_budget', 15, 2)->nullable();
            $table->string('currency', 3)->nullable();
            $table->decimal('unit_cost', 15, 2)->nullable();
            $table->string('pacing_strategy')->nullable();
            $table->string('optimization_target_type')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->timestamp('last_synced_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_ad_campaigns');
    }
};
