<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_analytics_properties', function (Blueprint $table) {
            $table->id();

            $table->foreignId('google_analytics_account_id')
                ->constrained('google_analytics_accounts')
                ->cascadeOnDelete();

            $table->string('external_id');
            $table->string('name');
            $table->string('industry_category')->nullable();
            $table->string('time_zone');
            $table->string('currency_code');
            $table->string('service_level');
            $table->string('property_type');
            $table->timestamp('external_created_at')->nullable();
            $table->timestamp('external_updated_at')->nullable();
            $table->timestamp('last_synced_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_analytics_properties');
    }
};
