<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tik_tok_videos', function (Blueprint $table) {
            $table->id();

            $table->foreignId('tik_tok_account_id')
                ->constrained('tik_tok_accounts')
                ->onDelete('cascade');

            $table->timestamp('date');
            $table->string('external_id');
            $table->string('title')->nullable();
            $table->string('description')->nullable();
            $table->text('image_url')->nullable();
            $table->integer('duration')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tik_tok_videos');
    }
};
