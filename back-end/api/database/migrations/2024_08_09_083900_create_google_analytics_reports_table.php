<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_analytics_reports', function (Blueprint $table) {
            $table->id();

            $table->foreignId('google_analytics_property_id')
                ->constrained('google_analytics_properties')
                ->cascadeOnDelete();

            $table->dateTime('date');
            $table->string('type');
            $table->string('name')->nullable();
            $table->integer('value');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_analytics_reports');
    }
};
