<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('youtube_video_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('youtube_video_id')
                ->constrained('youtube_videos')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('views')->default(0);
            $table->integer('engagement')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('youtube_video_insights');
    }
};
