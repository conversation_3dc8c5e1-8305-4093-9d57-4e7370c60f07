<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_business_profile_reports', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('google_business_profile_id');

            $table->timestamp('date');

            $table->integer('views_google')->default(0);
            $table->integer('views_maps')->default(0);
            $table->integer('views_directions')->default(0);
            $table->integer('phone_calls')->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('google_business_profile_id', 'gbp_reports_profile_id_foreign')
                ->references('id')
                ->on('google_business_profiles')
                ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_business_profile_reports');
    }
};
