<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::table('instagram_accounts')
            ->delete();

        Schema::table('instagram_accounts', function (Blueprint $table) {
            $table->dropForeign(['facebook_business_id']);
            $table->dropColumn('facebook_business_id');

            $table->foreignId('facebook_page_id')
                ->after('id')
                ->constrained('facebook_pages')
                ->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('instagram_accounts', function (Blueprint $table) {
            $table->dropForeign(['facebook_page_id']);
            $table->dropColumn('facebook_page_id');

            $table->foreignId('facebook_business_id')
                ->after('id')
                ->constrained('facebook_businesses')
                ->cascadeOnDelete();
        });
    }
};
