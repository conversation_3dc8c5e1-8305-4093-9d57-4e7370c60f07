<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('youtube_channel_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('youtube_channel_id')
                ->constrained('youtube_channels')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('views')->default(0);
            $table->integer('engagement')->default(0);
            $table->integer('number_of_videos')->default(0);
            $table->integer('subscriber_count')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('youtube_channel_insights');
    }
};
