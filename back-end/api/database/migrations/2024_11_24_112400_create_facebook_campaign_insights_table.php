<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_campaign_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_campaign_id')
                ->constrained('facebook_campaigns')
                ->cascadeOnDelete();

            $table->string('publisher_platform');
            $table->timestamp('date');

            $table->decimal('ctr', 8, 4);
            $table->decimal('reach', 8, 4);
            $table->decimal('spend', 8, 4);
            $table->integer('clicks');
            $table->decimal('cpm', 8, 4);
            $table->integer('impressions');
            $table->integer('actions');
            $table->json('action_types');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_campaign_insights');
    }
};
