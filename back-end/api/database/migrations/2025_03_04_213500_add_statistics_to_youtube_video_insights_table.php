<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('youtube_video_insights', function (Blueprint $table) {
            $table->integer('likes')->after('engagement');
            $table->integer('dislikes')->after('engagement');
            $table->integer('favorites')->after('engagement');
            $table->integer('comments')->after('engagement');

            $table->dropColumn('deleted_at');
        });
    }

    public function down(): void
    {
        Schema::table('youtube_video_insights', function (Blueprint $table) {
            $table->dropColumn('likes');
            $table->dropColumn('dislikes');
            $table->dropColumn('favorites');
            $table->dropColumn('comments');

            $table->softDeletes();
        });
    }
};
