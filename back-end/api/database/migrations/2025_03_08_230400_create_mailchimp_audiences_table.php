<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mailchimp_audiences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mailchimp_account_id')->constrained('mailchimp_accounts')->cascadeOnDelete();
            $table->string('external_id');
            $table->string('name');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mailchimp_audiences');
    }
};
