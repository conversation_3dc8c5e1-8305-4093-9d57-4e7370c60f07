<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNameColumnToLocaliumAccountsTable extends Migration
{
    public function up(): void
    {
        Schema::table('localium_accounts', function (Blueprint $table) {
            $table->string('name')->default('Unknown')->after('sync_deactivated');
        });
    }

    public function down(): void
    {
        Schema::table('localium_accounts', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
}
