<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $insights = DB::table('instagram_account_insights')->where('followers', '<=', 0)->get();

        foreach ($insights as $insight) {
            $followers = DB::table('instagram_account_insights')
                ->where('instagram_account_id', $insight->instagram_account_id)
                ->where('followers', '>=', 0)
                ->where('date', '<', $insight->date)
                ->orderBy('date', 'DESC')
                ->first()
                ?->followers ?? 0;

            DB::table('instagram_account_insights')
                ->where('id', $insight->id)
                ->update([
                    'followers' => $followers,
                ]);
        }
    }
};
