<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('linked_in_organisations', function (Blueprint $table) {
            $table->id();

            $table->foreignId('linkedin_account_id')
                ->constrained('linked_in_accounts')
                ->onDelete('cascade');

            $table->string('external_id');
            $table->string('state');
            $table->string('name')->nullable();
            $table->longText('website')->nullable();
            $table->timestamp('last_synced_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_organisations');
    }
};
