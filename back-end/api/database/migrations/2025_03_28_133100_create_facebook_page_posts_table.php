<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_page_posts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_page_id')
                ->constrained('facebook_pages')
                ->onDelete('cascade');

            $table->timestamp('date');
            $table->string('external_id');
            $table->text('message')->nullable();
            $table->text('url')->nullable();
            $table->text('media_url')->nullable();
            $table->boolean('is_hidden');
            $table->boolean('is_published');
            $table->timestamp('insights_last_synced_at')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_page_posts');
    }
};
