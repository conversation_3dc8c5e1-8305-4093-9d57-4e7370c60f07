<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_ad_campaigns', function (Blueprint $table) {
            $table->id();

            $table->foreignId('google_ad_account_id')
                ->constrained('google_ad_accounts')
                ->cascadeOnDelete();

            $table->bigInteger('external_id');

            $table->string('name');
            $table->string('advertising_channel_type');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_campaigns');
    }
};
