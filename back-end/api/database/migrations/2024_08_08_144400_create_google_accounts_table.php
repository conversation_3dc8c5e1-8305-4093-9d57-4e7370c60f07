<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_accounts', function (Blueprint $table) {
            $table->id();

            $table->string('external_id');
            $table->string('email');
            $table->string('name')->nullable();
            $table->longText('image_url')->nullable();

            $table->longText('refresh_token')->nullable();
            $table->longText('token')->nullable();
            $table->timestamp('token_created_at')->nullable();
            $table->timestamp('deactivated_at')->nullable();

            $table->json('scopes')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_accounts');
    }
};
