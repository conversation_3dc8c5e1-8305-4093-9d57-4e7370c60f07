<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('localium_accounts', function (Blueprint $table) {
            $table->id();
            $table->boolean('sync_deactivated')->default(false);
            $table->string('user_id');
            $table->text('token');
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('localium_accounts');
    }
};
