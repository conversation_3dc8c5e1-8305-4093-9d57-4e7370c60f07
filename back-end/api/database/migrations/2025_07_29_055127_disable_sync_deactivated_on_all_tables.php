<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tables = [
            'google_ad_accounts',
            'facebook_pages',
            'instagram_accounts',
            'facebook_ad_accounts',
            'google_analytics_accounts',
            'google_analytics_properties',
            'linked_in_ad_accounts',
            'linked_in_organisations',
            'google_business_profiles',
            'localium_accounts',
            'youtube_google_accounts',
            'youtube_channels',
            'mailchimp_audiences',
            'tik_tok_accounts',
            'tik_tok_business_accounts',
        ];

        foreach ($tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->boolean('sync_deactivated')->default(true)->change();
            });
        }

    }
};
