<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('google_ad_accounts', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('facebook_pages', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('instagram_accounts', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('facebook_ad_accounts', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('google_analytics_accounts', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });

        Schema::table('google_analytics_properties', function (Blueprint $table) {
            $table->boolean('sync_deactivated')->after('id')->default(true);
        });
    }

    public function down(): void
    {
        Schema::table('google_ad_accounts', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('facebook_pages', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('instagram_accounts', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('facebook_ad_accounts', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('google_analytics_accounts', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });

        Schema::table('google_analytics_properties', function (Blueprint $table) {
            $table->dropColumn('sync_deactivated');
        });
    }
};
