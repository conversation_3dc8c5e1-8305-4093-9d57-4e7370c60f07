<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $instagramAccounts = DB::table('instagram_accounts')->get();

        foreach ($instagramAccounts as $account) {
            $this->processMetric($account->id, 'followers');
        }
    }

    protected function processMetric(int $accountId, string $metricName): void
    {
        // Get all data points for this account and metric, ordered by date
        $dataPoints = DB::table('instagram_account_insights')
            ->where('instagram_account_id', $accountId)
            ->orderBy('date')
            ->get();

        $runningTotal = 0;

        foreach ($dataPoints as $dataPoint) {
            $runningTotal += $dataPoint->followers;

            DB::table('instagram_account_insights')
                ->where('instagram_account_id', $accountId)
                ->where('date', $dataPoint->date)
                ->update(
                    [
                        $metricName => $runningTotal,
                    ]
                );
        }
    }
};
