<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_ads', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_ad_account_id')
                ->constrained('facebook_ad_accounts')
                ->cascadeOnDelete();

            $table->foreignId('facebook_campaign_id')
                ->constrained('facebook_campaigns')
                ->cascadeOnDelete();

            $table->string('external_id');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_ads');
    }
};
