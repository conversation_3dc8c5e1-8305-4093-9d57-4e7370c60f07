<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('oauth_hash');
            $table->dropColumn('oauth_expires_at');

            $table->string('password')
                ->after('email')
                ->default('');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('oauth_hash')->nullable();
            $table->timestamp('oauth_expires_at')->nullable();

            $table->dropColumn('password');
        });
    }
};
