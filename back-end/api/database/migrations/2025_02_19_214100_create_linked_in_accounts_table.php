<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('linked_in_accounts');

        Schema::create('linked_in_accounts', function (Blueprint $table) {
            $table->id();

            $table->string('type');
            $table->string('external_id');
            $table->string('email')->nullable();
            $table->string('name')->nullable();

            $table->longText('refresh_token')->nullable();
            $table->longText('token')->nullable();
            $table->timestamp('token_created_at')->nullable();
            $table->timestamp('deactivated_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('linked_in_accounts');
    }
};
