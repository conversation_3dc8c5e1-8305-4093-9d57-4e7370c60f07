<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_page_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_page_id')
                ->constrained('facebook_pages')
                ->cascadeOnDelete();

            $table->string('name');
            $table->timestamp('date');
            $table->integer('value');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_page_insights');
    }
};
