<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('instagram_media_item_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('instagram_media_item_id')
                ->constrained('instagram_media_items')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('reach')->default(0);
            $table->integer('impressions')->default(0);
            $table->integer('total_interactions')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instagram_media_item_insights');
    }
};
