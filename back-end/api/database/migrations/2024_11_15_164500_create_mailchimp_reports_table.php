<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mailchimp_reports', function (Blueprint $table) {
            $table->id();

            $table->foreignId('mailchimp_campaign_id')
                ->constrained('mailchimp_campaigns')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('emails_sent');
            $table->integer('clicks_total');
            $table->integer('unique_clicks');
            $table->decimal('click_rate');
            $table->integer('opens_total');
            $table->integer('unique_opens');
            $table->decimal('open_rate');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mailchimp_reports');
    }
};
