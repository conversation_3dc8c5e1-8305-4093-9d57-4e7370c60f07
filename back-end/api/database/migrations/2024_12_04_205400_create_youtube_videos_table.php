<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('youtube_videos', function (Blueprint $table) {
            $table->id();

            $table->foreignId('youtube_channel_id')
                ->constrained('youtube_channels')
                ->cascadeOnDelete();

            $table->string('external_id');
            $table->string('name');
            $table->longText('thumbnail_url');
            $table->timestamp('last_synced_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('youtube_videos');
    }
};
