<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('facebook_ad_accounts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('facebook_business_id')
                ->constrained('facebook_businesses')
                ->cascadeOnDelete();

            $table->string('external_id');
            $table->string('name');

            $table->timestamp('last_synced_at');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('facebook_ad_accounts');
    }
};
