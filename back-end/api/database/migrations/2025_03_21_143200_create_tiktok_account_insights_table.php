<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tik_tok_account_insights', function (Blueprint $table) {
            $table->id();

            $table->foreignId('tik_tok_account_id')
                ->constrained('tik_tok_accounts')
                ->cascadeOnDelete();

            $table->timestamp('date');

            $table->integer('video_count');
            $table->integer('follower_count');
            $table->integer('likes_count');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tik_tok_account_insights');
    }
};
