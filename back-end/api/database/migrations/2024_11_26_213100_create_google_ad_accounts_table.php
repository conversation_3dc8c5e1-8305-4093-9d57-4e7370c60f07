<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('google_ad_accounts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('google_account_id')
                ->constrained('google_accounts')
                ->cascadeOnDelete();

            $table->bigInteger('customer_id')->nullable();
            $table->bigInteger('external_id');

            $table->string('name');
            $table->string('status');
            $table->string('time_zone');
            $table->string('currency');

            $table->integer('level');
            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_manager')->default(false);
            $table->boolean('is_test_account')->default(false);

            $table->timestamp('last_synced_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_accounts');
    }
};
