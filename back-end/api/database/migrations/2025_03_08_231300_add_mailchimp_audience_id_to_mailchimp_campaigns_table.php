<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('mailchimp_campaigns', function (Blueprint $table) {
            $table->foreignId('mailchimp_audience_id')
                ->nullable()
                ->after('mailchimp_account_id')
                ->constrained('mailchimp_audiences')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('mailchimp_campaigns', function (Blueprint $table) {
            $table->dropConstrainedForeignId('mailchimp_audience_id');
        });
    }
};
