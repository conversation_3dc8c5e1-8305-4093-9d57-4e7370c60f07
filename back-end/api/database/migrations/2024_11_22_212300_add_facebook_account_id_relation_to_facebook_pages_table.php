<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('facebook_pages', function (Blueprint $table) {
            $table->dropForeign(['facebook_business_id']);

            $table->foreignId('facebook_business_id')
                ->nullable()
                ->change()
                ->constrained('facebook_businesses')
                ->cascadeOnDelete();

            $table->foreignId('facebook_account_id')
                ->nullable()
                ->after('id')
                ->constrained('facebook_accounts')
                ->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('facebook_pages', function (Blueprint $table) {
            $table->dropForeign(['facebook_business_id']);

            $table->foreignId('facebook_business_id')
                ->nullable(false)
                ->change()
                ->constrained('facebook_businesses')
                ->cascadeOnDelete();

            $table->dropForeign(['facebook_account_id']);
            $table->dropColumn('facebook_account_id');
        });
    }
};
