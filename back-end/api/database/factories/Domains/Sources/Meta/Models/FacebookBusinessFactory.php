<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookBusinessFactory extends Factory
{
    protected $model = FacebookBusiness::class;

    public function definition(): array
    {
        return [
            'facebook_account_id' => FacebookAccount::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'name' => $this->faker->name,
            'last_synced_at' => now(),
        ];
    }
}
