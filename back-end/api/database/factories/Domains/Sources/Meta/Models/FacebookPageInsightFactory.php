<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookPageInsightFactory extends Factory
{
    protected $model = FacebookPageInsight::class;

    public function definition(): array
    {
        return [
            'facebook_page_id' => FacebookPage::factory(),
            'date' => now(),
            'page_post_engagements' => $this->faker->numberBetween(1000, 20000),
            'page_impressions_unique' => $this->faker->numberBetween(1000, 20000),
            'page_fans' => $this->faker->numberBetween(1000, 20000),
            'page_fan_adds' => $this->faker->numberBetween(1000, 20000),
            'page_fan_removes' => $this->faker->numberBetween(1000, 20000),
        ];
    }
}
