<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookCampaignFactory extends Factory
{
    protected $model = FacebookCampaign::class;

    public function definition(): array
    {
        return [
            'facebook_ad_account_id' => FacebookAdAccount::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'name' => $this->faker->name,
        ];
    }
}
