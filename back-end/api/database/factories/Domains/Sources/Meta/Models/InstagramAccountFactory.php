<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstagramAccountFactory extends Factory
{
    protected $model = InstagramAccount::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'facebook_page_id' => FacebookPage::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'username' => $this->faker->name,
        ];
    }
}
