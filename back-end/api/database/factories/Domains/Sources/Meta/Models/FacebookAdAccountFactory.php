<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookAdAccountFactory extends Factory
{
    protected $model = FacebookAdAccount::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'facebook_business_id' => FacebookBusiness::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'name' => $this->faker->name,
            'last_synced_at' => now(),
        ];
    }
}
