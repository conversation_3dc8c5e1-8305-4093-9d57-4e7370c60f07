<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstagramAccountInsightFactory extends Factory
{
    protected $model = InstagramAccountInsight::class;

    public function definition(): array
    {
        return [
            'instagram_account_id' => InstagramAccount::factory(),
            'date' => now(),
            'followers' => $this->faker->numberBetween(1, 20000),
            'reach' => $this->faker->numberBetween(1, 20000),
            'impressions' => $this->faker->numberBetween(1, 20000),
            'total_interactions' => $this->faker->numberBetween(1, 20000),
        ];
    }
}
