<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookPageFactory extends Factory
{
    protected $model = FacebookPage::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'facebook_business_id' => FacebookBusiness::factory(),
            'external_id' => (string) $this->faker->numberBetween(10000000, 20000000),
            'name' => $this->faker->name,
            'token' => 'secret',
            'token_created_at' => now(),
            'last_synced_at' => now(),
        ];
    }
}
