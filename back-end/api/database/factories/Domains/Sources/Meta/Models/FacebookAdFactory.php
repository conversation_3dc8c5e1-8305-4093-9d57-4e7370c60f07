<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookAdFactory extends Factory
{
    protected $model = FacebookAd::class;

    public function definition(): array
    {
        return [
            'facebook_ad_account_id' => FacebookAdAccount::factory(),
            'facebook_campaign_id' => FacebookCampaign::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
        ];
    }
}
