<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class FacebookAccountFactory extends Factory
{
    protected $model = FacebookAccount::class;

    public function definition(): array
    {
        return [
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'email' => $this->faker->safeEmail,
            'name' => $this->faker->name,
            'image_url' => $this->faker->imageUrl,
            'token' => Str::random(),
            'token_created_at' => now(),
        ];
    }
}
