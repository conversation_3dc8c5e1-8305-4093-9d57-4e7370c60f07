<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstagramMediaItemInsightFactory extends Factory
{
    protected $model = InstagramMediaItemInsight::class;

    public function definition(): array
    {
        return [
            'instagram_media_item_id' => InstagramMediaItem::factory(),
            'date' => now(),
            'reach' => $this->faker->numberBetween(1, 20000),
            'impressions' => $this->faker->numberBetween(1, 20000),
            'total_interactions' => $this->faker->numberBetween(1, 20000),
        ];
    }
}
