<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Support\Enums\Instagram\MediaType;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstagramMediaItemFactory extends Factory
{
    protected $model = InstagramMediaItem::class;

    public function definition(): array
    {
        return [
            'instagram_account_id' => InstagramAccount::factory(),
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'external_date' => now(),
            'media_type' => $this->faker->randomElement(MediaType::cases())->value,
            'media_url' => $this->faker->imageUrl(),
        ];
    }
}
