<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Meta\Models;

use App\Domains\Sources\Meta\Models\FacebookCampaign;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class FacebookCampaignInsightFactory extends Factory
{
    protected $model = FacebookCampaignInsight::class;

    public function definition(): array
    {
        return [
            'facebook_campaign_id' => FacebookCampaign::factory(),
            'publisher_platform' => 'facebook',
            'date' => now(),
            'ctr' => $this->faker->numberBetween(1, 100),
            'reach' => $this->faker->numberBetween(1, 100),
            'spend' => $this->faker->numberBetween(1, 100),
            'clicks' => $this->faker->numberBetween(1, 100),
            'cpm' => $this->faker->numberBetween(1, 100),
            'impressions' => $this->faker->numberBetween(1, 100),
            'actions' => $this->faker->numberBetween(1, 100),
            'action_types' => [
                'likes' => 1,
                'post_engagement' => 1,
                'page_engagement' => 1,
                'comments' => 1,
                'link_click' => 1,
                'post_reaction' => 1,
            ],
        ];
    }
}
