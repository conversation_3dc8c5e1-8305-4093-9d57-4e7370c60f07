<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInAdCampaignFactory extends Factory
{
    protected $model = LinkedInAdCampaign::class;

    public function definition(): array
    {
        return [
            'linkedin_ad_account_id' => LinkedInAdAccount::factory(),
            'external_id' => (string) $this->faker->numberBetween(*********, *********),
            'name' => $this->faker->sentence(3),
            'status' => $this->faker->randomElement(['ACTIVE', 'PAUSED', 'COMPLETED', 'DRAFT']),
            'type' => 'SPONSORED_UPDATES',
            'objective_type' => $this->faker->randomElement(['ENGAGEMENT', 'WEBSITE_VISIT']),
            'total_budget' => $this->faker->randomFloat(2, 100, 10000),
            'currency' => 'EUR',
            'unit_cost' => $this->faker->randomFloat(2, 1, 100),
            'pacing_strategy' => $this->faker->randomElement(['LIFETIME', 'DAILY']),
            'optimization_target_type' => $this->faker->randomElement(['MAX_CLICK', 'MAX_IMPRESSION']),
            'start_date' => now(),
            'end_date' => now()->addDays(30),
            'last_synced_at' => now(),
        ];
    }
}
