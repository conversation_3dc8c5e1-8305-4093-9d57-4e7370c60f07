<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInOrganisationPostInsightFactory extends Factory
{
    protected $model = LinkedInOrganisationPost::class;

    public function definition(): array
    {
        return [
            'linkedin_organisation_id' => LinkedInOrganisation::factory(),
            'date' => now(),
        ];
    }
}
