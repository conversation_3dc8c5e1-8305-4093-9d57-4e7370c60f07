<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInOrganisationInsightFactory extends Factory
{
    protected $model = LinkedInOrganisationInsight::class;

    public function definition(): array
    {
        return [
            'linkedin_organisation_id' => LinkedInOrganisation::factory(),
            'date' => now(),
            'clicks' => $this->faker->numberBetween(2, 50),
            'views' => $this->faker->numberBetween(2, 50),
            'followers' => $this->faker->numberBetween(2, 50),
        ];
    }
}
