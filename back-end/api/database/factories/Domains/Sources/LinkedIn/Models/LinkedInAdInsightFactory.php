<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInAdInsightFactory extends Factory
{
    protected $model = LinkedInAdInsight::class;

    public function definition(): array
    {
        return [
            'linkedin_ad_campaign_id' => LinkedInAdCampaign::factory(),
            'date' => now(),
            'ctr' => $this->faker->randomFloat(2, 1, 100),
            'reach' => $this->faker->randomFloat(2, 100, 10000),
            'spend' => $this->faker->randomFloat(2, 100, 10000),
            'clicks' => $this->faker->numberBetween(2, 50),
            'cpm' => $this->faker->randomFloat(2, 100, 10000),
            'impressions' => $this->faker->numberBetween(2, 50),
        ];
    }
}
