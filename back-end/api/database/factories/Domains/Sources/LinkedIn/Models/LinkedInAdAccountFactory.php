<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInAdAccountFactory extends Factory
{
    protected $model = LinkedInAdAccount::class;

    public function definition(): array
    {
        return [
            'linkedin_account_id' => LinkedInAccount::factory(),
            'sync_deactivated' => false,
            'external_id' => (string) $this->faker->numberBetween(*********, *********),
            'name' => $this->faker->company.' Advertentieaccount',
            'currency' => 'EUR',
            'status' => 'ACTIVE',
            'type' => 'BUSINESS',
            'reference' => 'urn:li:organization:'.$this->faker->numberBetween(100000, 999999),
            'test' => false,
            'last_synced_at' => now(),
        ];
    }
}
