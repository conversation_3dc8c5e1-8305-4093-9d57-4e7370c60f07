<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInOrganisationFactory extends Factory
{
    protected $model = LinkedInOrganisation::class;

    public function definition(): array
    {
        return [
            'linkedin_account_id' => LinkedInAccount::factory(),
            'sync_deactivated' => false,
            'external_id' => 'urn:li:organization:'.$this->faker->numberBetween(100000, 999999),
            'state' => 'APPROVED',
            'name' => $this->faker->company.' Page',
            'website' => $this->faker->url,
            'last_synced_at' => now(),
        ];
    }
}
