<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class LinkedInAccountFactory extends Factory
{
    protected $model = LinkedInAccount::class;

    public function definition(): array
    {
        return [
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'type' => LinkedInAccountType::ADS,
            'email' => $this->faker->email,
            'name' => $this->faker->name,
            'refresh_token' => Str::random(),
            'token' => Str::random(),
            'token_created_at' => now(),
        ];
    }
}
