<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use Illuminate\Database\Eloquent\Factories\Factory;

class LinkedInOrganisationPostFactory extends Factory
{
    protected $model = LinkedInOrganisationPost::class;

    public function definition(): array
    {
        return [
            'linkedin_organisation_id' => LinkedInOrganisation::factory(),
            'date' => now(),
            'external_id' => 'urn:li:ugcPost:'.$this->faker->numberBetween(100000, 999999),
            'state' => 'PUBLISHED',
            'visibility' => 'PUBLIC',
            'commentary_excerpt' => 'Daarom startte LKQ in 2024 het project “Inspired to Thrive”.',
        ];
    }
}
