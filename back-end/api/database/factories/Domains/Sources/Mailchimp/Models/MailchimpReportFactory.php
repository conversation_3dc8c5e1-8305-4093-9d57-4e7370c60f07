<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Database\Eloquent\Factories\Factory;

class MailchimpReportFactory extends Factory
{
    protected $model = MailchimpReport::class;

    public function definition(): array
    {
        return [
            'mailchimp_campaign_id' => MailchimpCampaign::factory(),
            'date' => now(),
            'emails_sent' => $this->faker->numberBetween(0, 100),
            'clicks_total' => $this->faker->numberBetween(0, 100),
            'unique_clicks' => $this->faker->numberBetween(0, 100),
            'click_rate' => $this->faker->numberBetween(0, 100),
            'opens_total' => $this->faker->numberBetween(0, 100),
            'unique_opens' => $this->faker->numberBetween(0, 100),
            'open_rate' => $this->faker->numberBetween(0, 100),
        ];
    }
}
