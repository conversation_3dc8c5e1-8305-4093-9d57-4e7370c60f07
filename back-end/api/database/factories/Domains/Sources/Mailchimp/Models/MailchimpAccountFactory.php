<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class MailchimpAccountFactory extends Factory
{
    protected $model = MailchimpAccount::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'region' => $this->faker->word,
            'api_key' => $this->faker->word,
        ];
    }
}
