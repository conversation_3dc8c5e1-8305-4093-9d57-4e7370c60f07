<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MailchimpAudienceFactory extends Factory
{
    protected $model = MailchimpAudience::class;

    public function definition(): array
    {
        return [
            'mailchimp_account_id' => MailchimpAccount::factory(),
            'sync_deactivated' => false,
            'external_id' => Str::random(),
            'name' => $this->faker->word,
            'is_active' => true,
        ];
    }
}
