<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Support\Enums\Campaign\Status;
use Illuminate\Database\Eloquent\Factories\Factory;

class MailchimpCampaignFactory extends Factory
{
    protected $model = MailchimpCampaign::class;

    public function definition(): array
    {
        return [
            'mailchimp_account_id' => MailchimpAccount::factory(),
            'mailchimp_audience_id' => MailchimpAudience::factory(),
            'external_id' => $this->faker->uuid(),
            'status' => $this->faker->randomElement(Status::cases())->value,
            'send_time' => now(),
            'title' => $this->faker->word,
        ];
    }
}
