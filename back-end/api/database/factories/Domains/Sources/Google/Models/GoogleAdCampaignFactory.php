<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Support\Enums\Ads\AdvertisingChannelType;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAdCampaignFactory extends Factory
{
    protected $model = GoogleAdCampaign::class;

    public function definition(): array
    {
        return [
            'google_ad_account_id' => GoogleAdAccount::factory(),
            'external_id' => $this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'advertising_channel_type' => $this->faker->randomElement(AdvertisingChannelType::cases())->value,
        ];
    }
}
