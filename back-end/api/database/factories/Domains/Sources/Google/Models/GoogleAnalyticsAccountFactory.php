<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAnalyticsAccountFactory extends Factory
{
    protected $model = GoogleAnalyticsAccount::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'google_account_id' => GoogleAccount::factory(),
            'external_id' => 'accounts/'.$this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'region_code' => 'NL',
            'external_created_at' => now()->toIso8601ZuluString('millisecond'),
            'external_updated_at' => now()->toIso8601ZuluString('millisecond'),
            'last_synced_at' => now(),
        ];
    }
}
