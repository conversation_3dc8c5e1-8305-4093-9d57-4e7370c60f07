<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class YoutubeChannelFactory extends Factory
{
    protected $model = YoutubeChannel::class;

    public function definition(): array
    {
        return [
            'youtube_google_account_id' => YoutubeGoogleAccount::factory(),
            'external_id' => $this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'last_synced_at' => now(),
            'sync_deactivated' => false,
        ];
    }
}
