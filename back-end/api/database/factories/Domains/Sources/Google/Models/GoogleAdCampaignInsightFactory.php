<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAdCampaignInsightFactory extends Factory
{
    protected $model = GoogleAdCampaignInsight::class;

    public function definition(): array
    {
        return [
            'google_ad_campaign_id' => GoogleAdCampaign::factory(),
            'date' => now(),
            'clicks' => $this->faker->numberBetween(100, 2000),
            'video_views' => $this->faker->numberBetween(100, 2000),
            'ctr' => $this->faker->numberBetween(100, 2000),
            'impressions' => $this->faker->numberBetween(100, 2000),
            'average_cpc' => (string) ($this->faker->numberBetween(1000000, 20000000) / 1000),
            'average_cpm' => (string) ($this->faker->numberBetween(1000000, 20000000) / 1000),
            'spend' => (string) ($this->faker->numberBetween(1000000, 20000000) / 1000),
        ];
    }
}
