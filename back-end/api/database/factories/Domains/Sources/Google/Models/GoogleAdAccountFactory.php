<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAdAccountFactory extends Factory
{
    protected $model = GoogleAdAccount::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'google_account_id' => GoogleAccount::factory(),
            'customer_id' => $this->faker->numberBetween(1000, 20000),
            'external_id' => $this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'status' => 'ENABLED',
            'time_zone' => 'Europe/Amsterdam',
            'currency' => 'EUR',
            'level' => 1,
            'is_hidden' => false,
            'is_manager' => false,
            'is_test_account' => false,
            'last_synced_at' => now(),
        ];
    }
}
