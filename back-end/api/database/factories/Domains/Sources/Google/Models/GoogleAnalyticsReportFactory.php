<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAnalyticsReportFactory extends Factory
{
    protected $model = GoogleAnalyticsReport::class;

    public function definition(): array
    {
        return [
            'google_analytics_property_id' => GoogleAnalyticsProperty::factory(),
            'date' => now()->setMinute(0)->setSecond(0),
            'type' => $this->faker->randomElement(ReportType::cases())->value,
            'name' => null,
            'value' => 0,
        ];
    }

    public function asType(ReportType $reportType): self
    {
        return $this->state([
            'type' => $reportType->value,
        ]);
    }

    public function withName(): self
    {
        return $this->state([
            'name' => $this->faker->firstName,
        ]);
    }
}
