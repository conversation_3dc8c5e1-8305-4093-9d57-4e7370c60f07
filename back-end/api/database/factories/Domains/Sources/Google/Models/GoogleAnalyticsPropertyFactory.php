<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Enums\Analytics\IndustryCategory;
use App\Domains\Sources\Google\Support\Enums\Analytics\PropertyType;
use App\Domains\Sources\Google\Support\Enums\Analytics\ServiceLevel;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAnalyticsPropertyFactory extends Factory
{
    protected $model = GoogleAnalyticsProperty::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'google_analytics_account_id' => GoogleAnalyticsAccount::factory(),
            'external_id' => 'accounts/'.$this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'industry_category' => $this->faker->randomElement(IndustryCategory::cases())->value,
            'time_zone' => 'Europe/Amsterdam',
            'currency_code' => 'USD',
            'service_level' => $this->faker->randomElement(ServiceLevel::cases())->value,
            'property_type' => $this->faker->randomElement(PropertyType::cases())->value,
            'external_created_at' => now()->toIso8601ZuluString('millisecond'),
            'external_updated_at' => now()->toIso8601ZuluString('millisecond'),
            'last_synced_at' => now(),
        ];
    }
}
