<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class YoutubeGoogleAccountFactory extends Factory
{
    protected $model = YoutubeGoogleAccount::class;

    public function definition(): array
    {
        return [
            'external_id' => (string) $this->faker->numberBetween(********, ********),
            'email' => $this->faker->email,
            'name' => $this->faker->name,
            'image_url' => $this->faker->imageUrl,
            'refresh_token' => Str::random(),
            'token' => Str::random(),
            'token_created_at' => now(),
            'sync_deactivated' => false,
        ];
    }
}
