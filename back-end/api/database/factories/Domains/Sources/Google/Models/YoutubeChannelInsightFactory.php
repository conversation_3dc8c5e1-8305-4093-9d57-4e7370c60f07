<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeChannelInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class YoutubeChannelInsightFactory extends Factory
{
    protected $model = YoutubeChannelInsight::class;

    public function definition(): array
    {
        return [
            'youtube_channel_id' => YoutubeChannel::factory(),
            'date' => now(),
            'views' => $this->faker->numberBetween(100, 2000),
            'engagement' => $this->faker->numberBetween(100, 2000),
            'number_of_videos' => $this->faker->numberBetween(100, 2000),
            'subscriber_count' => $this->faker->numberBetween(100, 2000),
        ];
    }
}
