<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use Illuminate\Database\Eloquent\Factories\Factory;

class YoutubeVideoFactory extends Factory
{
    protected $model = YoutubeVideo::class;

    public function definition(): array
    {
        return [
            'youtube_channel_id' => YoutubeChannel::factory(),
            'external_id' => $this->faker->numberBetween(1000, 20000),
            'name' => $this->faker->company,
            'thumbnail_url' => $this->faker->url,
            'last_synced_at' => now(),
        ];
    }
}
