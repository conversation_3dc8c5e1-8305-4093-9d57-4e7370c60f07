<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class YoutubeVideoInsightFactory extends Factory
{
    protected $model = YoutubeVideoInsight::class;

    public function definition(): array
    {
        return [
            'youtube_video_id' => YoutubeVideo::factory(),
            'date' => now(),
            'views' => $this->faker->numberBetween(100, 2000),
            'likes' => $this->faker->numberBetween(100, 2000),
            'dislikes' => $this->faker->numberBetween(100, 2000),
            'comments' => $this->faker->numberBetween(100, 2000),
            'favorites' => $this->faker->numberBetween(100, 2000),
            'engagement' => $this->faker->numberBetween(100, 2000),
        ];
    }
}
