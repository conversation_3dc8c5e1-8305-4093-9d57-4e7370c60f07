<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Localium\Models;

use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleBusinessProfileFactory extends Factory
{
    protected $model = GoogleBusinessProfile::class;

    public function definition(): array
    {
        return [
            'sync_deactivated' => false,
            'external_id' => $this->faker->unique()->numerify('gbp_########'),
            'name' => $this->faker->company(),
            'localium_account_id' => LocaliumAccount::factory(),
            'last_synced_at' => $this->faker->optional()->dateTimeThisMonth(),
            'created_at' => $this->faker->dateTimeThisYear(),
            'updated_at' => $this->faker->dateTimeThisYear(),
        ];
    }
}
