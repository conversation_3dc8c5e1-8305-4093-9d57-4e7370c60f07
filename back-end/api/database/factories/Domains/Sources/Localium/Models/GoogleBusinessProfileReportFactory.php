<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Sources\Localium\Models;

use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfileReport;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleBusinessProfileReportFactory extends Factory
{
    protected $model = GoogleBusinessProfileReport::class;

    public function definition(): array
    {
        return [
            'google_business_profile_id' => GoogleBusinessProfile::factory(),
            'date' => $this->faker->dateTimeThisYear(),
            'views_google' => $this->faker->numberBetween(0, 1000),
            'views_maps' => $this->faker->numberBetween(0, 500),
            'views_directions' => $this->faker->numberBetween(0, 200),
            'phone_calls' => $this->faker->numberBetween(0, 50),
            'created_at' => $this->faker->dateTimeThisYear(),
            'updated_at' => $this->faker->dateTimeThisYear(),
        ];
    }
}
