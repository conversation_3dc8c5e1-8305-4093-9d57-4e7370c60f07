<?php

namespace Database\Factories\Domains\Sources\Localium\Models;

use App\Domains\Sources\Localium\Models\LocaliumAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class LocaliumAccountFactory extends Factory
{
    protected $model = LocaliumAccount::class;

    public function definition(): array
    {
        return [
            'token' => $this->faker->uuid(),
            'user_id' => $this->faker->uuid(),
            'sync_deactivated' => false,
            'last_synced_at' => now(),
        ];
    }
}
