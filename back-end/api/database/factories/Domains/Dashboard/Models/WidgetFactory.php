<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDataType;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use Illuminate\Database\Eloquent\Factories\Factory;

class WidgetFactory extends Factory
{
    protected $model = Widget::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->name,
            'type' => $this->faker->randomElement(WidgetType::cases())->value,
            'data_type' => $this->faker->randomElement(WidgetDataType::cases())->value,
            'section_id' => Section::factory(),
            'color' => $this->faker->hexColor,
        ];
    }

    public function asType(WidgetType $type): self
    {
        return $this->state([
            'type' => $type->value,
        ]);
    }
}
