<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\Section;
use Illuminate\Database\Eloquent\Factories\Factory;

class SectionFactory extends Factory
{
    protected $model = Section::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->name,
            'dashboard_id' => Dashboard::factory(),
        ];
    }
}
