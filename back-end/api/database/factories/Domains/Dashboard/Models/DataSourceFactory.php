<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

class DataSourceFactory extends Factory
{
    protected $model = DataSource::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->name,
            'business_unit' => 'Global',
            'region' => 'NL',
            'channel' => 'Generic',
        ];
    }

    public function withDashboard(Dashboard $dashboard): self
    {
        return $this->afterCreating(
            fn (DataSource $dataSource) => $dataSource->dashboards()->sync($dashboard)
        );
    }

    public function forSourceable(Model $model): self
    {
        return $this->state([
            'sourceable_id' => $model->getKey(),
            'sourceable_type' => $model->getMorphClass(),
        ]);
    }
}
