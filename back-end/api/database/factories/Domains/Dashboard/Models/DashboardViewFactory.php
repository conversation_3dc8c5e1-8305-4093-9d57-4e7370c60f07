<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Dashboard\Models;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use Illuminate\Database\Eloquent\Factories\Factory;

class DashboardViewFactory extends Factory
{
    protected $model = DashboardView::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'dashboard_id' => Dashboard::factory(),
        ];
    }
}
