<?php

declare(strict_types=1);

namespace Database\Factories\Domains\Authentication\Models;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->lastName,
            'role' => Role::USER,
            'email' => $this->faker->email,
            'password' => '$2y$04$JMLl4eHp1IYeiPRNWSifKeEFyGO/iP45BgzSzM1ajQxhq6D4sbSUe', // password
        ];
    }

    public function withRole(Role $role): self
    {
        return $this->state([
            'role' => $role->value,
        ]);
    }
}
