# .veracode.yml
profile: default
application: api
sandbox: Development Sandbox

static:
  scan_name_template: "api-{timestamp}"
  auto_scan: true

pipeline:
  fail_on_severity: ["Very High", "High"]
  baseline_file: "baseline.json"

packaging:
  exclude:
    - "node_modules/**"
    - "tests/**"
    - ".git/**"
    - "storage/logs/**"
    - "storage/framework/cache/**"
    - "storage/framework/sessions/**"
