<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'google' => [
        'client_id' => env('OAUTH_GOOGLE_CLIENT_ID'),
        'client_secret' => env('OAUTH_GOOGLE_CLIENT_SECRET'),
        'redirect' => sprintf('%s/api/sources/google/callback', env('APP_URL')),
        'ads_developer_token' => env('OAUTH_GOOGLE_ADS_DEVELOPER_TOKEN'),
    ],

    'youtube' => [
        'client_id' => env('OAUTH_GOOGLE_CLIENT_ID'),
        'client_secret' => env('OAUTH_GOOGLE_CLIENT_SECRET'),
        'redirect' => sprintf('%s/api/sources/youtube/callback', env('APP_URL')),
    ],

    'linkedin-community' => [
        'client_id' => env('OAUTH_LINKEDIN_COMMUNITY_CLIENT_ID'),
        'client_secret' => env('OAUTH_LINKEDIN_COMMUNITY_CLIENT_SECRET'),
        'proxy' => null,
    ],

    'linkedin-ads' => [
        'client_id' => env('OAUTH_LINKEDIN_ADS_CLIENT_ID'),
        'client_secret' => env('OAUTH_LINKEDIN_ADS_CLIENT_SECRET'),
        'proxy' => null,
    ],

    'facebook' => [
        'client_id' => env('OAUTH_FACEBOOK_CLIENT_ID'),
        'client_secret' => env('OAUTH_FACEBOOK_CLIENT_SECRET'),
        'config_id' => env('OAUTH_FACEBOOK_CONFIG_ID'),
        'redirect' => sprintf('%s/api/sources/facebook/callback', env('APP_URL')),
        'proxy' => null,
    ],

    'tiktok' => [
        'client_id' => env('OAUTH_TIKTOK_CLIENT_ID'),
        'client_secret' => env('OAUTH_TIKTOK_CLIENT_SECRET'),
        'redirect' => sprintf('%s/api/sources/tiktok/callback', env('APP_URL')),
    ],

    'tiktok-business' => [
        'client_id' => env('OAUTH_TIKTOK_BUSINESS_CLIENT_ID'),
        'client_secret' => env('OAUTH_TIKTOK_BUSINESS_CLIENT_SECRET'),
        'redirect' => env('OAUTH_TIKTOK_BUSINESS_OVERWRITE_REDIRECT') ?? sprintf('%s/api/sources/tiktok-business/callback', env('APP_URL')),
    ],
];
