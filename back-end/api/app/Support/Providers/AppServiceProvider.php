<?php

declare(strict_types=1);

namespace App\Support\Providers;

use App\Domains\Sources\TikTokBusiness\Support\Providers\TikTokBusinessProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\TikTok\Provider as TikTokProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void {}

    public function boot(): void
    {
        $this->loadSocialite();
        $this->setUrlGenerationParameters();
    }

    protected function loadSocialite(): void
    {
        Event::listen(function (SocialiteWasCalled $event) {
            $event->extendSocialite('tiktok', TikTokProvider::class);
            $event->extendSocialite('tiktok-business', TikTokBusinessProvider::class);
        });
    }

    protected function setUrlGenerationParameters(): void
    {
        URL::forceScheme('https');
        URL::forceRootUrl(Config::get('app.url'));
    }
}
