<?php

declare(strict_types=1);

namespace App\Support\Traits;

use App\Support\Scopes\SyncDeactivatedScope;

trait SyncDeactivatedTrait
{
    public static function bootSyncDeactivatedTrait()
    {
        static::addGlobalScope(new SyncDeactivatedScope);
    }

    public function initializeSyncDeactivatedTrait()
    {
        if (! isset($this->casts['sync_deactivated'])) {
            $this->casts['sync_deactivated'] = 'boolean';
        }
    }

    public function deactivate()
    {
        $this->sync_deactivated = true;
        $this->save();
    }

    public function reactivate()
    {
        $this->sync_deactivated = false;
        $this->save();
    }

    public function isDeactivated(): bool
    {
        return $this->sync_deactivated;
    }
}
