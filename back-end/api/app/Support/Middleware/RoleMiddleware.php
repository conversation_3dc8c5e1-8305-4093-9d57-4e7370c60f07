<?php

declare(strict_types=1);

namespace App\Support\Middleware;

use App\Domains\Authentication\Support\Enums\Role;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, string $roleValue): Response
    {
        $user = Auth::user();
        $role = Role::from($roleValue);

        if ($user?->hasRole($role)) {
            return $next($request);
        }

        return response()->json(null, 403);
    }

    public static function init(Role $role): string
    {
        return sprintf('%s:%s', self::class, $role->value);
    }
}
