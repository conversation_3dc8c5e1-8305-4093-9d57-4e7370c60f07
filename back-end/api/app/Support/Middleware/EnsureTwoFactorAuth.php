<?php

declare(strict_types=1);

namespace App\Support\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class EnsureTwoFactorAuth
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        if ($user && $user->two_factor_enabled && ! Session::get('two_factor_authenticated')) {
            return response()->json(['message' => 'Two-factor authentication required'], 401);
        }

        return $next($request);
    }
}
