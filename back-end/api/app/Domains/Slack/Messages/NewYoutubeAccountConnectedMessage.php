<?php

namespace App\Domains\Slack\Messages;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class NewYoutubeAccountConnectedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly YoutubeGoogleAccount $youtubeGoogleAccount) {}

    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(fn (SectionBlock $block) => $block->text(sprintf('New Youtube account connected: %s', $this->youtubeGoogleAccount->name)));
    }
}
