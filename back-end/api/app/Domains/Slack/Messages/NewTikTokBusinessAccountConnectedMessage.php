<?php

namespace App\Domains\Slack\Messages;

use App\Domains\Sources\TikTokBusiness\Models\TikTokBusinessAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class NewTikTokBusinessAccountConnectedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly TikTokBusinessAccount $tikTokBusinessAccount) {}

    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(fn (SectionBlock $block) => $block->text(sprintf('New TikTok Business account connected: %s', $this->tikTokBusinessAccount->name)));
    }
}
