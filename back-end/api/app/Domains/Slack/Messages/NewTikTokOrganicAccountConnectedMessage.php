<?php

namespace App\Domains\Slack\Messages;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class NewTikTokOrganicAccountConnectedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly TikTokAccount $tikTokAccount) {}

    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(fn (SectionBlock $block) => $block->text(sprintf('New TikTok Organic account connected: %s', $this->tikTokAccount->name)));
    }
}
