<?php

namespace App\Domains\Slack\Messages;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class NewLinkedinCommunityAccountConnectedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly LinkedInAccount $linkedInAccount) {}

    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(fn (SectionBlock $block) => $block->text(sprintf('New LinkedIn Community account connected: %s', $this->linkedInAccount->name)));
    }
}
