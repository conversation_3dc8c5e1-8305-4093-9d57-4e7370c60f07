<?php

namespace App\Domains\Slack\Messages;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class NewMetaAccountConnectedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly FacebookAccount $facebookAccount) {}

    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(fn (SectionBlock $block) => $block->text(sprintf('New Meta account connected: %s', $this->facebookAccount->name)));
    }
}
