<?php

declare(strict_types=1);

namespace App\Domains\Authentication\Models;

use App\Domains\Authentication\Support\Enums\Role;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'role',
    ];

    protected $hidden = [
        'password',
    ];

    protected function casts(): array
    {
        return [
            'dashboards' => 'array',
            'two_factor_expires_at' => 'datetime',
            'role' => Role::class,
        ];
    }

    public function hasRole(Role $role): bool
    {
        return $role === $this->role;
    }
}
