<?php

declare(strict_types=1);

namespace App\Domains\Authentication\Actions;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Mails\TwoFactorMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendTwoFactorCode
{
    public function execute(User $user): void
    {
        $code = Str::random(8);

        $user->two_factor_code = $code;
        $user->two_factor_expires_at = now()->addMinutes(30);
        $user->save();

        $mail = new TwoFactorMail($user->email, $code);
        Mail::send($mail);
    }
}
