<?php

declare(strict_types=1);

namespace App\Domains\Authentication\Support\Mails;

use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class AccountCreatedMail extends Mailable
{
    public function __construct(public string $email) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: $this->email,
            subject: 'Account created',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.authentication.account-created',
            with: [
                'email' => $this->email,
                'url' => $this->getUrl(),
            ],
        );
    }

    protected function getUrl(): string
    {
        $email = urlencode($this->email);

        return sprintf('%s/auth/request?email=%s', config('app.frontend_url'), $email);
    }
}
