<?php

declare(strict_types=1);

namespace App\Domains\Authentication\Support\Mails;

use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class TwoFactorMail extends Mailable
{
    public function __construct(public string $email, public string $code) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: $this->email,
            subject: 'Your code for two-factor authentication',
        );
    }

    public function content(): Content
    {
        return new Content(
            text: 'mail.authentication.two-factor-text',
            markdown: 'mail.authentication.two-factor',
            with: [
                'code' => $this->code,
            ],
        );
    }
}
