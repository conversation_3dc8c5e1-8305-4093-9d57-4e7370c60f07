<?php

declare(strict_types=1);

namespace App\Domains\Authentication\Support\Mails;

use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class PasswordResetMail extends Mailable
{
    public function __construct(public string $email, public string $token) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: $this->email,
            subject: 'Set password',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.authentication.reset-password',
            with: [
                'email' => $this->email,
                'url' => $this->getUrl(),
            ],
        );
    }

    protected function getUrl(): string
    {
        $email = urlencode($this->email);
        $token = urlencode($this->token);

        return sprintf('%s/auth/reset?email=%s&token=%s', config('app.frontend_url'), $email, $token);
    }
}
