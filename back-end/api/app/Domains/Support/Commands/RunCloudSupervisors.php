<?php

declare(strict_types=1);

namespace App\Domains\Support\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RunCloudSupervisors extends Command
{
    protected $signature = 'runcloud:restart_supervisors';

    protected $description = 'Restart relevant RunCloud Supervisors';

    public function handle(): void
    {
        $apiKey = config('runcloud.api.key');
        $apiSecret = config('runcloud.api.secret');
        $server = config('runcloud.server');
        $supervisors = config('runcloud.supervisors');

        if (! $apiKey || ! $apiSecret || ! $server || ! $supervisors) {
            return;
        }

        $supervisors = explode(',', $supervisors);

        foreach ($supervisors as $supervisor) {
            $url = sprintf('https://manage.runcloud.io/api/v2/servers/%s/supervisors/%s/reload', $server, $supervisor);

            $response = Http::withBasicAuth($apiKey, $apiSecret)
                ->acceptJson()
                ->asJson()
                ->post($url);

            if (! $response->successful()) {
                $this->warn('Could not restart supervisor: '.$supervisor);
                Log::critical($response->json());

                continue;
            }

            $this->info('Restarted supervisor: '.$supervisor);
        }
    }
}
