<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Commands;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\Section;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Enums\Widget\Icon;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDataType;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Console\Command;

class AddSectionsAndWidgets extends Command
{
    private const SECTION_ORDER = [
        GoogleAnalyticsProperty::class,
        MailchimpAudience::class,
        FacebookAdAccount::class,
        FacebookPage::class,
        InstagramAccount::class,
        LinkedInAdAccount::class,
        LinkedInOrganisation::class,
        TikTokAccount::class,
        YoutubeChannel::class,
        GoogleAdAccount::class,
        GoogleBusinessProfile::class,
    ];

    protected $signature = 'dashboard:add_sections_and_widgets {dashboard : The ID of the dashboard to add to}';

    protected $description = 'Connects all relevant sections and widgets.';

    public function handle(): void
    {
        $dashboardId = $this->argument('dashboard');
        $dashboard = Dashboard::find($dashboardId);

        if (! $dashboard) {
            $this->error("Dashboard with ID {$dashboardId} not found.");

            return;
        }

        $dataSources = $dashboard->dataSources()->get();

        if ($dataSources->isEmpty()) {
            $this->warn('No data sources found.');

            return;
        }

        $dashboard->sections()->delete();

        $types = $dataSources->pluck('sourceable_type')->unique()->sortBy(function ($type) {
            $position = array_search($type, self::SECTION_ORDER);

            return $position === false ? count(self::SECTION_ORDER) : $position;
        });

        foreach ($types as $type) {
            $this->processType($dashboard, $type);
        }
    }

    protected function processType(Dashboard $dashboard, string $type): void
    {
        switch ($type) {
            case GoogleAnalyticsProperty::class:
                $section = $this->addSection($dashboard, 'Google Analytics', Icon::GOOGLE);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ANALYTICS_SESSION, 'Sessions');
                $this->addWidget($section, WidgetDataType::PIE_CHART, WidgetType::GOOGLE_ANALYTICS_CHANNEL_GROUPS, 'Sources');
                break;
            case MailchimpAudience::class:
                $section = $this->addSection($dashboard, 'MailChimp', Icon::MAILCHIMP);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::MAILCHIMP_SENDS, 'Sends');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::MAILCHIMP_OPEN_RATE, 'Open rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::MAILCHIMP_CLICK_THROUGH_RATE, 'Click through rate');
                break;
            case GoogleAdAccount::class:
                $section = $this->addSection($dashboard, 'Google Ads - Display', Icon::GOOGLE_ADS);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_DISPLAY_CLICKS, 'Clicks');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::GOOGLE_ADS_DISPLAY_CLICK_THROUGH_RATE, 'Click through rate', true);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::GOOGLE_ADS_DISPLAY_COST_PER_CLICK, 'Cost per click', true);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_DISPLAY_IMPRESSIONS, 'Impressions');
                $section = $this->addSection($dashboard, 'Google Ads - Search', Icon::GOOGLE_ADS);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_SEARCH_CLICKS, 'Clicks');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::GOOGLE_ADS_SEARCH_CLICK_THROUGH_RATE, 'Click through rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::GOOGLE_ADS_SEARCH_COST_PER_CLICK, 'Cost per click', true);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_SEARCH_IMPRESSIONS, 'Impressions');
                $section = $this->addSection($dashboard, 'Google Ads - Video', Icon::GOOGLE_ADS);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_YOUTUBE_CLICKS, 'Clicks');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::GOOGLE_ADS_YOUTUBE_CLICK_THROUGH_RATE, 'Click through rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_CLICK, 'Cost per click', true);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::GOOGLE_ADS_YOUTUBE_VIDEO_VIEWS, 'Impressions');
                break;
            case FacebookAdAccount::class:
                $section = $this->addSection($dashboard, 'Facebook Ads', Icon::FACEBOOK);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::META_ADS_FACEBOOK_REACH, 'Reach');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::META_ADS_FACEBOOK_SPEND, 'Spend');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::META_ADS_FACEBOOK_CLICK_THROUGH_RATE, 'Click through rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::META_ADS_FACEBOOK_COST_PER_MILE, 'Cost per mille', true);
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::META_ADS_FACEBOOK_TOP_CAMPAIGN, 'Top campaign (reach)');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::META_ADS_FACEBOOK_BOTTOM_CAMPAIGN, 'Bottom campaign (reach)');
                $section = $this->addSection($dashboard, 'Instagram Ads', Icon::INSTAGRAM);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::META_ADS_INSTAGRAM_REACH, 'Reach');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::META_ADS_INSTAGRAM_SPEND, 'Spend');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::META_ADS_INSTAGRAM_CLICK_THROUGH_RATE, 'Click through rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::META_ADS_INSTAGRAM_COST_PER_MILE, 'Cost per mille', true);
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::META_ADS_INSTAGRAM_TOP_CAMPAIGN, 'Top campaign (reach)');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::META_ADS_INSTAGRAM_BOTTOM_CAMPAIGN, 'Bottom campaign (reach)');
                break;
            case FacebookPage::class:
                $section = $this->addSection($dashboard, 'Facebook Organic', Icon::FACEBOOK);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::FACEBOOK_REACH, 'Reach');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::FACEBOOK_ENGAGEMENT, 'Engagement');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::FACEBOOK_ENGAGEMENT_RATE, 'Engagement rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::FACEBOOK_FOLLOWERS, 'Followers');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::FACEBOOK_TOP_POST, 'Best clicked post');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::FACEBOOK_BOTTOM_POST, 'Least clicked post');
                break;
            case InstagramAccount::class:
                $section = $this->addSection($dashboard, 'Instagram Organic', Icon::INSTAGRAM);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::INSTAGRAM_REACH, 'Reach');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::INSTAGRAM_ENGAGEMENT, 'Engagement');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::INSTAGRAM_ENGAGEMENT_RATE, 'Engagement rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::INSTAGRAM_FOLLOWERS, 'Followers');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::INSTAGRAM_TOP_POST, 'Best engaging post');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::INSTAGRAM_BOTTOM_POST, 'Least engaging post');
                break;
            case YoutubeChannel::class:
                $section = $this->addSection($dashboard, 'Youtube', Icon::YOUTUBE);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::YOUTUBE_VIEWS, 'Views');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::YOUTUBE_ENGAGEMENT, 'Engagement');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::YOUTUBE_ENGAGEMENT_RATE, 'Engagement rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::YOUTUBE_NUMBER_OF_VIDEOS, 'Number of videos');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::YOUTUBE_NUMBER_OF_SUBSCRIBERS, 'Number of subscribers');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::YOUTUBE_TOP_VIDEO, 'Best engaged video');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::YOUTUBE_BOTTOM_VIDEO, 'Least engaged video');
                break;
            case LinkedInAdAccount::class:
                $section = $this->addSection($dashboard, 'LinkedIn Ads', Icon::LINKEDIN);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_ADS_REACH, 'Reach');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::LINKED_IN_ADS_SPEND, 'Spend');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::LINKED_IN_ADS_CLICK_THROUGH_RATE, 'Click through rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_EURO, WidgetType::LINKED_IN_ADS_COST_PER_MILE, 'Cost per mille', true);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_ADS_CLICKS, 'Clicks');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::LINKED_IN_ADS_TOP_CAMPAIGN, 'Top campaign (reach)');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::LINKED_IN_ADS_BOTTOM_CAMPAIGN, 'Bottom campaign (reach)');
                break;
            case LinkedInOrganisation::class:
                $section = $this->addSection($dashboard, 'LinkedIn Profiles', Icon::LINKEDIN);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_COMMUNITY_REACH, 'Reach profile');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_COMMUNITY_POST_REACH, 'Reach posts');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT, 'Engagement');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT_RATE, 'Engagement rate');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_COMMUNITY_FOLLOWERS, 'Followers');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LINKED_IN_COMMUNITY_PROFILE_VIEWS, 'Profile views');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::LINKED_IN_COMMUNITY_TOP_POST, 'Best engaged post');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST, 'Least engaged post');
                break;
            case GoogleBusinessProfile::class:
                $section = $this->addSection($dashboard, 'Google Business Profiles', Icon::GOOGLE);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LOCALIUM_VIEWS_DIRECTIONS, 'Directions');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LOCALIUM_VIEWS_GOOGLE, 'Views Google');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LOCALIUM_VIEWS_MAPS, 'Views Maps');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::LOCALIUM_PHONE_CALLS, 'Phone Calls');
                break;
            case TikTokAccount::class:
                $section = $this->addSection($dashboard, 'TikTok Organic', Icon::TIKTOK);
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::TIKTOK_VIDEO_COUNT, 'Number of videos');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::TIKTOK_FOLLOWER_COUNT, 'Followers');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::TIKTOK_VIDEO_VIEWS, 'Video Views');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART, WidgetType::TIKTOK_VIDEO_ENGAGEMENT, 'Video Engagement');
                $this->addWidget($section, WidgetDataType::COLUMN_CHART_PERCENTAGE, WidgetType::TIKTOK_VIDEO_ENGAGEMENT_RATE, 'Video Engagement Rate');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::TIKTOK_TOP_VIDEO, 'Best engaged video');
                $this->addWidget($section, WidgetDataType::SINGLE_POST, WidgetType::TIKTOK_BOTTOM_VIDEO, 'Least engaged video');
                break;
        }
    }

    protected function addSection(Dashboard $dashboard, string $title, Icon $icon): Section
    {
        $section = new Section;

        $section->dashboard_id = $dashboard->id;
        $section->title = $title;
        $section->icon = $icon->value;

        $section->save();

        return $section;
    }

    protected function addWidget(Section $section, WidgetDataType $dataType, WidgetType $type, string $title, bool $inverseComparisonIndicator = false): Widget
    {
        $widget = new Widget;

        $widget->section_id = $section->id;
        $widget->title = $title;
        $widget->data_type = $dataType;
        $widget->type = $type;
        $widget->color = '#5F9AFF';
        $widget->inverse_comparison_indicator = $inverseComparisonIndicator;

        $widget->save();

        return $widget;
    }
}
