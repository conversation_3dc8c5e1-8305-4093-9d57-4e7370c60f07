<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Commands;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Console\Command;

class Connect extends Command
{
    protected $signature = 'dashboard:connect {dashboard : The ID of the dashboard to connect data sources to}';

    protected $description = 'Connects all Data Sources to the specified dashboard.';

    public function handle(): void
    {
        $dashboardId = $this->argument('dashboard');
        $dashboard = Dashboard::find($dashboardId);

        if (! $dashboard) {
            $this->error("Dashboard with ID {$dashboardId} not found.");

            return;
        }

        $dataSources = DataSource::all();

        if ($dataSources->isEmpty()) {
            $this->warn('No data sources found.');

            return;
        }

        $this->info("Connecting data sources to dashboard '{$dashboard->title}'...");

        $connectedSources = $dashboard->dataSources->pluck('id')->toArray();
        $sourcesToConnect = $dataSources->whereNotIn('id', $connectedSources);

        if ($sourcesToConnect->isEmpty()) {
            $this->line("Dashboard '{$dashboard->title}' is already connected to all data sources.");

            return;
        }

        $dashboard->dataSources()->attach($sourcesToConnect->pluck('id')->toArray());
        $this->info('Connected '.$sourcesToConnect->count()." data source(s) to dashboard '{$dashboard->title}'");
        $this->info("All data sources are now connected to dashboard '{$dashboard->title}'.");
    }
}
