<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Posts\Sources\Facebook;

use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\PostAnalyticsEntry;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\FacebookPagePost;
use App\Domains\Sources\Meta\Models\FacebookPagePostInsight;
use Illuminate\Support\Arr;

class GetFacebookPostStatistics
{
    private Widget $widget;

    private FacebookPagePost $post;

    private WidgetAnalyticsFilter $filter;

    public function execute(Widget $widget, int $postId, WidgetAnalyticsFilter $widgetAnalyticsFilter): PostAnalytics
    {
        $this->widget = $widget;
        $this->post = FacebookPagePost::query()->findOrFail($postId);
        $this->filter = $widgetAnalyticsFilter;

        $postAnalytics = new PostAnalytics;
        $postAnalytics->setUrl($this->post->media_url);
        $postAnalytics->setChannel($this->post->facebookPage->name);

        $entries = [];

        foreach ($this->getAnalyticsEntries() as $key => $entry) {
            $entryDataPoint = new PostAnalyticsEntry;
            $entryDataPoint->setName($key);
            $entryDataPoint->setValue((float) $entry);
            $entryDataPoint->setValueFormatted($this->formatValue((float) $entry));

            $entries[] = $entryDataPoint;
        }

        foreach ($this->getComparisonAnalyticsEntries() as $key => $entry) {
            /** @var PostAnalyticsEntry $entry */
            $entryDataPoint = Arr::first(array_filter($entries, fn (PostAnalyticsEntry $existingEntry) => $existingEntry->getName() === $key));

            $entryDataPoint->setComparison($entry ? (float) $entry : null);
            $entryDataPoint->setComparisonFormatted($entry ? $this->formatValue((float) $entry) : null);
        }

        $postAnalytics->setEntries($entries);

        return $postAnalytics;
    }

    protected function getAnalyticsEntries(): array
    {
        $baseQuery = FacebookPagePostInsight::query()
            ->selectRaw('SUM(post_clicks) as Clicks')
            ->selectRaw('SUM(post_impressions_unique) as Reach')
            ->join('facebook_page_posts', 'facebook_page_posts.id', '=', 'facebook_page_post_insights.facebook_page_post_id')
            ->where('facebook_page_post_insights.facebook_page_post_id', $this->post->id);

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->first()->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getComparisonAnalyticsEntries(): array
    {
        return FacebookPagePostInsight::query()
            ->selectRaw('SUM(post_clicks) as Clicks')
            ->selectRaw('SUM(post_impressions_unique) as Reach')
            ->where('facebook_page_post_id', $this->post->id)
            ->whereBetween('facebook_page_post_insights.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])->first()?->toArray();
    }
}
