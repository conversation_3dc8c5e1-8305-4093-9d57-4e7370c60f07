<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Posts\Sources\Youtube;

use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\PostAnalyticsEntry;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use Illuminate\Support\Arr;

class GetYoutubePostStatistics
{
    private Widget $widget;

    private YoutubeVideo $post;

    private WidgetAnalyticsFilter $filter;

    public function execute(Widget $widget, int $postId, WidgetAnalyticsFilter $widgetAnalyticsFilter): PostAnalytics
    {
        $this->widget = $widget;
        $this->post = YoutubeVideo::query()->findOrFail($postId);
        $this->filter = $widgetAnalyticsFilter;

        $postAnalytics = new PostAnalytics;
        $postAnalytics->setUrl($this->post->thumbnail_url);
        $postAnalytics->setChannel($this->post->youtubeChannel->name);

        $entries = [];

        foreach ($this->getAnalyticsEntries() as $key => $entry) {
            $entryDataPoint = new PostAnalyticsEntry;
            $entryDataPoint->setName($key);
            $entryDataPoint->setValue((float) $entry);
            $entryDataPoint->setValueFormatted($this->formatValue((float) $entry));

            $entries[] = $entryDataPoint;
        }

        foreach ($this->getComparisonAnalyticsEntries() as $key => $entry) {
            /** @var PostAnalyticsEntry $entry */
            $entryDataPoint = Arr::first(array_filter($entries, fn (PostAnalyticsEntry $existingEntry) => $existingEntry->getName() === $key));

            $entryDataPoint->setComparison($entry ? (float) $entry : null);
            $entryDataPoint->setComparisonFormatted($entry ? $this->formatValue((float) $entry) : null);
        }

        $postAnalytics->setEntries($entries);

        return $postAnalytics;
    }

    protected function getAnalyticsEntries(): array
    {
        $baseQuery = YoutubeVideoInsight::query()
            ->selectRaw('SUM(engagement) as Engagement')
            ->selectRaw('SUM(views) as Views')
            ->join('youtube_videos', 'youtube_videos.id', '=', 'youtube_video_insights.youtube_video_id')
            ->where('youtube_video_insights.youtube_video_id', $this->post->id);

        if ($this->filter->getStartDate()) {
            $baseQuery->where('youtube_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('youtube_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::YOUTUBE_TOP_VIDEO => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::YOUTUBE_BOTTOM_VIDEO => $baseQuery->orderBy('engagement', 'ASC'),
        };

        return $baseQuery->first()->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getComparisonAnalyticsEntries(): array
    {
        return
            YoutubeVideoInsight::query()
                ->selectRaw('SUM(engagement) as Engagement')
                ->selectRaw('SUM(views) as Views')
                ->where('youtube_video_insights.youtube_video_id', $this->post->id)
                ->whereBetween('youtube_video_insights.date', [
                    $this->filter->getComparableStartDate(),
                    $this->filter->getComparableEndDate(),
                ])->first()->toArray();
    }
}
