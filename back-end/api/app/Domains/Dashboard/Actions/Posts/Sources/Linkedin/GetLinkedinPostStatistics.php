<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Posts\Sources\Linkedin;

use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\PostAnalyticsEntry;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPostInsight;
use Illuminate\Support\Arr;

class GetLinkedinPostStatistics
{
    private Widget $widget;

    private LinkedInOrganisationPost $post;

    private WidgetAnalyticsFilter $filter;

    public function execute(Widget $widget, int $postId, WidgetAnalyticsFilter $widgetAnalyticsFilter): PostAnalytics
    {
        $this->widget = $widget;
        $this->post = LinkedInOrganisationPost::query()->findOrFail($postId);
        $this->filter = $widgetAnalyticsFilter;

        $postAnalytics = new PostAnalytics;
        $postAnalytics->setText($this->post->commentary_excerpt);
        $postAnalytics->setChannel($this->post->linkedInOrganisation->name);

        $entries = [];

        foreach ($this->getAnalyticsEntries() as $key => $entry) {
            $entryDataPoint = new PostAnalyticsEntry;
            $entryDataPoint->setName($key);
            $entryDataPoint->setValue((float) $entry);
            $entryDataPoint->setValueFormatted($this->formatValue((float) $entry));

            $entries[] = $entryDataPoint;
        }

        foreach ($this->getComparisonAnalyticsEntries() as $key => $entry) {
            /** @var PostAnalyticsEntry $entry */
            $entryDataPoint = Arr::first(array_filter($entries, fn (PostAnalyticsEntry $existingEntry) => $existingEntry->getName() === $key));

            $entryDataPoint->setComparison($entry ? (float) $entry : null);
            $entryDataPoint->setComparisonFormatted($entry ? $this->formatValue((float) $entry) : null);
        }

        $postAnalytics->setEntries($entries);

        return $postAnalytics;
    }

    protected function getAnalyticsEntries(): array
    {
        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as Engagement')
            ->selectRaw('SUM(unique_impressions_count) as Reach')
            ->selectRaw('SUM(impression_count) as Impressions')
            ->join('linked_in_organisation_posts', 'linked_in_organisation_posts.id', '=', 'linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->where('linked_in_organisation_post_insights.linkedin_organisation_post_id', $this->post->id);

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::LINKED_IN_COMMUNITY_TOP_POST => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST => $baseQuery->orderBy('engagement', 'ASC'),
        };

        return $baseQuery->first()->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getComparisonAnalyticsEntries(): array
    {
        return LinkedInOrganisationPostInsight::query()
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as Engagement')
            ->selectRaw('SUM(unique_impressions_count) as Reach')
            ->selectRaw('SUM(impression_count) as Impressions')
            ->where('linked_in_organisation_post_insights.linkedin_organisation_post_id', $this->post->id)
            ->whereBetween('linked_in_organisation_post_insights.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])->first()?->toArray();
    }
}
