<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Posts\Sources\Facebook;

use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\PostAnalyticsEntry;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Support\Arr;

class GetInstagramPostStatistics
{
    private Widget $widget;

    private InstagramMediaItem $post;

    private WidgetAnalyticsFilter $filter;

    public function execute(Widget $widget, int $postId, WidgetAnalyticsFilter $widgetAnalyticsFilter): PostAnalytics
    {
        $this->widget = $widget;
        $this->post = InstagramMediaItem::query()->findOrFail($postId);
        $this->filter = $widgetAnalyticsFilter;

        $postAnalytics = new PostAnalytics;
        $postAnalytics->setUrl($this->post->media_url);
        $postAnalytics->setChannel($this->post->instagramAccount->name);

        $entries = [];

        foreach ($this->getAnalyticsEntries() as $key => $entry) {
            $entryDataPoint = new PostAnalyticsEntry;
            $entryDataPoint->setName($key);
            $entryDataPoint->setValue((float) $entry);
            $entryDataPoint->setValueFormatted($this->formatValue((float) $entry));

            $entries[] = $entryDataPoint;
        }

        foreach ($this->getComparisonAnalyticsEntries() as $key => $entry) {
            /** @var PostAnalyticsEntry $entry */
            $entryDataPoint = Arr::first(array_filter($entries, fn (PostAnalyticsEntry $existingEntry) => $existingEntry->getName() === $key));

            $entryDataPoint->setComparison($entry ? (float) $entry : null);
            $entryDataPoint->setComparisonFormatted($entry ? $this->formatValue((float) $entry) : null);
        }

        $postAnalytics->setEntries($entries);

        return $postAnalytics;
    }

    protected function getAnalyticsEntries(): array
    {
        $baseQuery = InstagramMediaItemInsight::query()
            ->selectRaw('SUM(total_interactions) as Engagement')
            ->selectRaw('SUM(reach) as Reach')
            ->selectRaw('SUM(impressions) as Impressions')
            ->join('instagram_media_items', 'instagram_media_items.id', '=', 'instagram_media_item_insights.instagram_media_item_id')
            ->where('instagram_media_item_insights.instagram_media_item_id', $this->post->id);

        if ($this->filter->getStartDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->first()->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getComparisonAnalyticsEntries(): array
    {
        return InstagramMediaItemInsight::query()
            ->selectRaw('SUM(total_interactions) as Engagement')
            ->selectRaw('SUM(reach) as Reach')
            ->selectRaw('SUM(impressions) as Impressions')
            ->where('instagram_media_item_id', $this->post->id)
            ->whereBetween('instagram_media_item_insights.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])->first()?->toArray();
    }
}
