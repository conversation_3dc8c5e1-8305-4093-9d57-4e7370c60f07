<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Posts;

use App\Domains\Dashboard\Actions\Posts\Sources\Facebook\GetFacebookPostStatistics;
use App\Domains\Dashboard\Actions\Posts\Sources\Facebook\GetInstagramPostStatistics;
use App\Domains\Dashboard\Actions\Posts\Sources\Linkedin\GetLinkedinPostStatistics;
use App\Domains\Dashboard\Actions\Posts\Sources\Youtube\GetYoutubePostStatistics;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;

class GetStatisticsForPost
{
    private Widget $widget;

    public function execute(Widget $widget, int $postId, WidgetAnalyticsFilter $filter): PostAnalytics
    {
        $this->widget = $widget;

        $action = $this->getStatisticsActionForType();

        return $action->execute($widget, $postId, $filter);
    }

    private function getStatisticsActionForType()
    {
        return match ($this->widget->type) {
            WidgetType::FACEBOOK_BOTTOM_POST,
            WidgetType::FACEBOOK_TOP_POST => app(GetFacebookPostStatistics::class),
            WidgetType::INSTAGRAM_BOTTOM_POST,
            WidgetType::INSTAGRAM_TOP_POST => app(GetInstagramPostStatistics::class),
            WidgetType::YOUTUBE_BOTTOM_VIDEO,
            WidgetType::YOUTUBE_TOP_VIDEO => app(GetYoutubePostStatistics::class),
            WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST,
            WidgetType::LINKED_IN_COMMUNITY_TOP_POST => app(GetLinkedinPostStatistics::class)
        };
    }
}
