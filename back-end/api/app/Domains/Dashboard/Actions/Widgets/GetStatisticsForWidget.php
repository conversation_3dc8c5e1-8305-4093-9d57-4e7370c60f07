<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets;

use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetFacebookAdsDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetFacebookPagesDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetFacebookPostDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetInstagramPagesDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetInstagramPostDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Facebook\GetTopMetaCampaignDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Google\GetGoogleAdsDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Google\GetGoogleAnalyticsDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Google\GetGoogleAnalyticsEventDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn\GetLinkedInAdsDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn\GetLinkedInOrganisationDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn\GetLinkedInOrganisationPostDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn\GetLinkedInPostDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn\GetTopLinkedInCampaignDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Localium\GetLocaliumDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Mailchimp\GetMailchimpDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\TikTok\GetTikTokDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\TikTok\GetTikTokProfileDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\TikTok\GetTikTokVideoDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Youtube\GetYoutubeDataForWidget;
use App\Domains\Dashboard\Actions\Widgets\Sources\Youtube\GetYoutubeVideoDataForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Support\Enums\Ads\AdvertisingChannelType;

class GetStatisticsForWidget
{
    protected Widget $widget;

    public function single(Widget $widget, WidgetAnalyticsFilter $widgetAnalyticsFilter): WidgetAnalytics|WidgetPostAnalytics|WidgetPieAnalytics
    {
        $this->widget = $widget;

        $action = $this->getStatisticsActionForType();

        return $action->single($widget, $widgetAnalyticsFilter);
    }

    protected function getStatisticsActionForType()
    {
        return match ($this->widget->type) {
            WidgetType::GOOGLE_ANALYTICS_SESSION,
            WidgetType::GOOGLE_ANALYTICS_BOOKINGS => app(GetGoogleAnalyticsDataForWidget::class),
            WidgetType::GOOGLE_ANALYTICS_CHANNEL_GROUPS => app(GetGoogleAnalyticsEventDataForWidget::class),
            WidgetType::MAILCHIMP_SENDS,
            WidgetType::MAILCHIMP_OPEN_RATE,
            WidgetType::MAILCHIMP_CLICK_THROUGH_RATE => app(GetMailchimpDataForWidget::class),
            WidgetType::GOOGLE_ADS_SEARCH_CLICKS,
            WidgetType::GOOGLE_ADS_SEARCH_CLICK_THROUGH_RATE,
            WidgetType::GOOGLE_ADS_SEARCH_COST_PER_CLICK,
            WidgetType::GOOGLE_ADS_SEARCH_IMPRESSIONS => app(GetGoogleAdsDataForWidget::class, ['advertisingChannelType' => AdvertisingChannelType::SEARCH]),
            WidgetType::GOOGLE_ADS_DISPLAY_CLICKS,
            WidgetType::GOOGLE_ADS_DISPLAY_CLICK_THROUGH_RATE,
            WidgetType::GOOGLE_ADS_DISPLAY_COST_PER_CLICK,
            WidgetType::GOOGLE_ADS_DISPLAY_IMPRESSIONS => app(GetGoogleAdsDataForWidget::class, ['advertisingChannelType' => AdvertisingChannelType::DISPLAY]),
            WidgetType::GOOGLE_ADS_YOUTUBE_CLICKS,
            WidgetType::GOOGLE_ADS_YOUTUBE_CLICK_THROUGH_RATE,
            WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_CLICK,
            WidgetType::GOOGLE_ADS_YOUTUBE_VIDEO_VIEWS,
            WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_MILE => app(GetGoogleAdsDataForWidget::class, ['advertisingChannelType' => AdvertisingChannelType::VIDEO]),
            WidgetType::META_ADS_FACEBOOK_REACH,
            WidgetType::META_ADS_FACEBOOK_SPEND,
            WidgetType::META_ADS_FACEBOOK_CLICK_THROUGH_RATE,
            WidgetType::META_ADS_FACEBOOK_COST_PER_MILE, => app(GetFacebookAdsDataForWidget::class, ['publisherPlatforms' => ['facebook']]),
            WidgetType::META_ADS_FACEBOOK_TOP_CAMPAIGN,
            WidgetType::META_ADS_FACEBOOK_BOTTOM_CAMPAIGN => app(GetTopMetaCampaignDataForWidget::class, ['publisherPlatforms' => ['facebook']]),
            WidgetType::META_ADS_INSTAGRAM_REACH,
            WidgetType::META_ADS_INSTAGRAM_SPEND,
            WidgetType::META_ADS_INSTAGRAM_CLICK_THROUGH_RATE,
            WidgetType::META_ADS_INSTAGRAM_COST_PER_MILE => app(GetFacebookAdsDataForWidget::class, ['publisherPlatforms' => ['instagram']]),
            WidgetType::META_ADS_INSTAGRAM_BOTTOM_CAMPAIGN,
            WidgetType::META_ADS_INSTAGRAM_TOP_CAMPAIGN => app(GetTopMetaCampaignDataForWidget::class, ['publisherPlatforms' => ['instagram']]),
            WidgetType::FACEBOOK_REACH,
            WidgetType::FACEBOOK_ENGAGEMENT,
            WidgetType::FACEBOOK_ENGAGEMENT_RATE,
            WidgetType::FACEBOOK_FOLLOWERS => app(GetFacebookPagesDataForWidget::class),
            WidgetType::FACEBOOK_TOP_POST,
            WidgetType::FACEBOOK_BOTTOM_POST => app(GetFacebookPostDataForWidget::class),
            WidgetType::INSTAGRAM_REACH,
            WidgetType::INSTAGRAM_ENGAGEMENT,
            WidgetType::INSTAGRAM_ENGAGEMENT_RATE,
            WidgetType::INSTAGRAM_FOLLOWERS => app(GetInstagramPagesDataForWidget::class),
            WidgetType::YOUTUBE_VIEWS,
            WidgetType::YOUTUBE_ENGAGEMENT,
            WidgetType::YOUTUBE_ENGAGEMENT_RATE,
            WidgetType::YOUTUBE_NUMBER_OF_VIDEOS,
            WidgetType::YOUTUBE_NUMBER_OF_SUBSCRIBERS => app(GetYoutubeDataForWidget::class),
            WidgetType::INSTAGRAM_TOP_POST,
            WidgetType::INSTAGRAM_BOTTOM_POST => app(GetInstagramPostDataForWidget::class),
            WidgetType::YOUTUBE_TOP_VIDEO,
            WidgetType::YOUTUBE_BOTTOM_VIDEO => app(GetYoutubeVideoDataForWidget::class),
            WidgetType::LINKED_IN_ADS_REACH,
            WidgetType::LINKED_IN_ADS_SPEND,
            WidgetType::LINKED_IN_ADS_CLICK_THROUGH_RATE,
            WidgetType::LINKED_IN_ADS_COST_PER_MILE,
            WidgetType::LINKED_IN_ADS_CLICKS => app(GetLinkedInAdsDataForWidget::class),
            WidgetType::LINKED_IN_ADS_TOP_CAMPAIGN,
            WidgetType::LINKED_IN_ADS_BOTTOM_CAMPAIGN => app(GetTopLinkedInCampaignDataForWidget::class),
            WidgetType::LINKED_IN_COMMUNITY_FOLLOWERS,
            WidgetType::LINKED_IN_COMMUNITY_ENGAGEMENT,
            WidgetType::LINKED_IN_COMMUNITY_ENGAGEMENT_RATE,
            WidgetType::LINKED_IN_COMMUNITY_REACH,
            WidgetType::LINKED_IN_COMMUNITY_PROFILE_VIEWS => app(GetLinkedInOrganisationDataForWidget::class),
            WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT,
            WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT_RATE,
            WidgetType::LINKED_IN_COMMUNITY_POST_REACH => app(GetLinkedInOrganisationPostDataForWidget::class),
            WidgetType::LINKED_IN_COMMUNITY_TOP_POST,
            WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST => app(GetLinkedInPostDataForWidget::class),
            WidgetType::LOCALIUM_VIEWS_DIRECTIONS,
            WidgetType::LOCALIUM_VIEWS_GOOGLE,
            WidgetType::LOCALIUM_VIEWS_MAPS,
            WidgetType::LOCALIUM_PHONE_CALLS => app(GetLocaliumDataForWidget::class),
            WidgetType::TIKTOK_VIDEO_VIEWS,
            WidgetType::TIKTOK_VIDEO_ENGAGEMENT,
            WidgetType::TIKTOK_VIDEO_ENGAGEMENT_RATE => app(GetTikTokDataForWidget::class),
            WidgetType::TIKTOK_TOP_VIDEO,
            WidgetType::TIKTOK_BOTTOM_VIDEO => app(GetTikTokVideoDataForWidget::class),
            WidgetType::TIKTOK_FOLLOWER_COUNT,
            WidgetType::TIKTOK_VIDEO_COUNT => app(GetTikTokProfileDataForWidget::class),
        };
    }

    /**
     * @return array<string, WidgetAnalytics|WidgetPieAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $widgetAnalyticsFilter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;

        $action = $this->getStatisticsActionForType();

        return $action->detail($widget, $widgetAnalyticsFilter, $widgetDetailScope);
    }
}
