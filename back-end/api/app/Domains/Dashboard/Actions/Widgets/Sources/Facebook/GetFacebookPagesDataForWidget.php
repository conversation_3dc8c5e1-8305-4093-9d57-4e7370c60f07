<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Facebook;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetFacebookPagesDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getFacebookPageData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getFacebookPageData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::FACEBOOK_REACH:
                    $value = (float) $dataPoint->reach;
                    $numerator += (float) $dataPoint->reach;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::FACEBOOK_ENGAGEMENT:
                    $value = (float) $dataPoint->engagement;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::FACEBOOK_ENGAGEMENT_RATE:
                    $value = $dataPoint->reach ? ($dataPoint->engagement / $dataPoint->reach) * 100 : 0;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator += (float) $dataPoint->reach;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
                case WidgetType::FACEBOOK_FOLLOWERS:
                    $value = (float) $dataPoint->followers;
                    $numerator = (float) $dataPoint->followers;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getFacebookPageData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = FacebookPageInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(page_impressions_unique) as reach')
            ->selectRaw('SUM(page_post_engagements) as engagement')
            ->selectRaw('SUM(page_fans) as followers')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'facebook_page_insights.facebook_page_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: FacebookPage::class
                        );
                })
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery = FacebookPageInsight::query()
            ->selectRaw('SUM(page_impressions_unique) as reach')
            ->selectRaw('SUM(page_post_engagements) as engagement')
            ->selectRaw('AVG(page_fans) as followers')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey && $detailValue) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'facebook_page_insights.facebook_page_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: FacebookPage::class
                        );
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        $baseQuery
            ->whereBetween('date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])
            ->groupByRaw('facebook_page_insights.facebook_page_id');

        $dataPoints = $baseQuery->get();
        $total = 0;

        if (! $dataPoints) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::FACEBOOK_REACH:
                $total = $dataPoints->sum('reach');
                break;
            case WidgetType::FACEBOOK_ENGAGEMENT:
                $total = $dataPoints->sum('engagement');
                break;
            case WidgetType::FACEBOOK_ENGAGEMENT_RATE:
                $total = $dataPoints->sum('reach') ? ($dataPoints->sum('engagement') / $dataPoints->sum('reach')) * 100 : 0;
                break;
            case WidgetType::FACEBOOK_FOLLOWERS:
                $total = $dataPoints->sum('followers');
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
