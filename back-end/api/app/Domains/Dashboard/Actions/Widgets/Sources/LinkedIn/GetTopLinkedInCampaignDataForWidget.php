<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GetTopLinkedInCampaignDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getCampaignData();
        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getCampaignDetailData($postData['linkedin_ad_campaign_id']);
        $comparisonData = $this->getCampaignComparisonData($postData['linkedin_ad_campaign_id']);

        $aggregatedData = [
            'reach' => [],
            'spend' => [],
            'clicks' => [],
            'impressions' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Spend
            $spendDataPoint = new WidgetAnalyticsDataPoint;
            $spendDataPoint->setDate($detailData['date']);
            $spendDataPoint->setValue((float) $detailData['spend']);
            $spendDataPoint->setValueFormatted($this->formatValue((float) $detailData['spend']));
            $aggregatedData['spend'][] = $spendDataPoint;

            // Clicks
            $clicksDataPoint = new WidgetAnalyticsDataPoint;
            $clicksDataPoint->setDate($detailData['date']);
            $clicksDataPoint->setValue((float) $detailData['clicks']);
            $clicksDataPoint->setValueFormatted($this->formatValue((float) $detailData['clicks']));
            $aggregatedData['clicks'][] = $clicksDataPoint;

            // Reach
            $reachDataPoint = new WidgetAnalyticsDataPoint;
            $reachDataPoint->setDate($detailData['date']);
            $reachDataPoint->setValue((float) $detailData['reach']);
            $reachDataPoint->setValueFormatted($this->formatValue((float) $detailData['reach']));
            $aggregatedData['reach'][] = $reachDataPoint;

            // Impressions
            $impressionsDataPoint = new WidgetAnalyticsDataPoint;
            $impressionsDataPoint->setDate($detailData['date']);
            $impressionsDataPoint->setValue((float) $detailData['impressions']);
            $impressionsDataPoint->setValueFormatted($this->formatValue((float) $detailData['impressions']));
            $aggregatedData['impressions'][] = $impressionsDataPoint;
        }

        // Spend
        $spendComparisonTotal = $comparisonData ? (float) $comparisonData['spend'] : null;
        $spendWidgetAnalytics = new WidgetAnalytics;
        $spendWidgetAnalytics->setData($aggregatedData['spend']);
        $spendWidgetAnalytics->setTotal((float) $postData['spend']);
        $spendWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['spend']));
        $spendWidgetAnalytics->setComparison($spendComparisonTotal ?? null);
        $spendWidgetAnalytics->setComparisonFormatted($spendComparisonTotal ? $this->formatValue($spendComparisonTotal) : null);
        $spendWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Clicks
        $clicksComparisonTotal = $comparisonData ? (float) $comparisonData['clicks'] : null;
        $clicksWidgetAnalytics = new WidgetAnalytics;
        $clicksWidgetAnalytics->setData($aggregatedData['clicks']);
        $clicksWidgetAnalytics->setTotal((float) $postData['clicks']);
        $clicksWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['clicks']));
        $clicksWidgetAnalytics->setComparison($clicksComparisonTotal ?? null);
        $clicksWidgetAnalytics->setComparisonFormatted($clicksComparisonTotal ? $this->formatValue($clicksComparisonTotal) : null);
        $clicksWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Reach
        $reachComparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;
        $reachWidgetAnalytics = new WidgetAnalytics;
        $reachWidgetAnalytics->setData($aggregatedData['reach']);
        $reachWidgetAnalytics->setTotal((float) $postData['reach']);
        $reachWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['reach']));
        $reachWidgetAnalytics->setComparison($reachComparisonTotal ?? null);
        $reachWidgetAnalytics->setComparisonFormatted($reachComparisonTotal ? $this->formatValue($reachComparisonTotal) : null);
        $reachWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $impressionsComparisonTotal = $comparisonData ? (float) $comparisonData['impressions'] : null;
        $impressionsWidgetAnalytics = new WidgetAnalytics;
        $impressionsWidgetAnalytics->setData($aggregatedData['impressions']);
        $impressionsWidgetAnalytics->setTotal((float) $postData['impressions']);
        $impressionsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['impressions']));
        $impressionsWidgetAnalytics->setComparison($impressionsComparisonTotal ?? null);
        $impressionsWidgetAnalytics->setComparisonFormatted($impressionsComparisonTotal ? $this->formatValue($impressionsComparisonTotal) : null);
        $impressionsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $impressionsWidgetAnalytics,
            'Reach' => $reachWidgetAnalytics,
            'Spend' => $spendWidgetAnalytics,
            'Clicks' => $clicksWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getCampaignData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $campaign = LinkedInAdCampaign::query()->findOrFail($post['linkedin_ad_campaign_id']);
        $comparisonData = $this->getCampaignComparisonData($campaign->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;

        $widgetPostAnalytics->setText(Str::limit($post['name']));
        $widgetPostAnalytics->setTotal((float) $post['reach']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['reach']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getCampaignData(): ?array
    {
        $baseQuery = LinkedInAdInsight::query()
            ->selectRaw('linked_in_ad_insights.linkedin_ad_campaign_id')
            ->selectRaw('linked_in_ad_campaigns.name as name')
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('linked_in_ad_campaigns', 'linked_in_ad_campaigns.id', '=', 'linked_in_ad_insights.linkedin_ad_campaign_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('linked_in_ad_insights.linkedin_ad_campaign_id')
            ->groupByRaw('linked_in_ad_campaigns.name');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_ad_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_ad_insights.date', '<=', $this->filter->getEndDate());
        }

        match ($this->widget->type) {
            WidgetType::LINKED_IN_ADS_TOP_CAMPAIGN => $baseQuery->orderBy('clicks', 'DESC'),
            WidgetType::LINKED_IN_ADS_BOTTOM_CAMPAIGN => $baseQuery->orderBy('clicks', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getCampaignDetailData(int $campaignId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(linked_in_ad_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(linked_in_ad_insights.date, "%Y-%m")',
        };

        $baseQuery = LinkedInAdInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('linked_in_ad_campaigns', 'linked_in_ad_campaigns.id', '=', 'linked_in_ad_insights.linkedin_ad_campaign_id')
            ->where('linked_in_ad_insights.linkedin_ad_campaign_id', $campaignId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_ad_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_ad_insights.date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getCampaignComparisonData(int $campaignId): ?array
    {
        $baseQuery = LinkedInAdInsight::query()
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('linked_in_ad_campaigns', 'linked_in_ad_campaigns.id', '=', 'linked_in_ad_insights.linkedin_ad_campaign_id')
            ->where('linked_in_ad_insights.linkedin_ad_campaign_id', $campaignId)
            ->groupByRaw('linked_in_ad_insights.linkedin_ad_campaign_id');

        $baseQuery->whereBetween('linked_in_ad_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
