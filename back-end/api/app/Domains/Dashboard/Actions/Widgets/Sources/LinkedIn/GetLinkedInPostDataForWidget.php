<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPostInsight;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GetLinkedInPostDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getPostData();

        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getDetailPostData($postData['linkedin_organisation_post_id']);
        $comparisonData = $this->getPostComparisonData($postData['linkedin_organisation_post_id']);

        $aggregatedData = [
            'reach' => [],
            'engagement' => [],
            'impressions' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Engagement
            $engagementDataPoint = new WidgetAnalyticsDataPoint;
            $engagementDataPoint->setDate($detailData['date']);
            $engagementDataPoint->setValue((float) $detailData['engagement']);
            $engagementDataPoint->setValueFormatted($this->formatValue((float) $detailData['engagement']));
            $aggregatedData['engagement'][] = $engagementDataPoint;

            // Reach
            $reachDataPoint = new WidgetAnalyticsDataPoint;
            $reachDataPoint->setDate($detailData['date']);
            $reachDataPoint->setValue((float) $detailData['reach']);
            $reachDataPoint->setValueFormatted($this->formatValue((float) $detailData['reach']));
            $aggregatedData['reach'][] = $reachDataPoint;

            // Impressions
            $impressionsDataPoint = new WidgetAnalyticsDataPoint;
            $impressionsDataPoint->setDate($detailData['date']);
            $impressionsDataPoint->setValue((float) $detailData['impressions']);
            $impressionsDataPoint->setValueFormatted($this->formatValue((float) $detailData['impressions']));
            $aggregatedData['impressions'][] = $impressionsDataPoint;
        }

        // Engagement
        $engagementComparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;
        $engagementWidgetAnalytics = new WidgetAnalytics;
        $engagementWidgetAnalytics->setData($aggregatedData['engagement']);
        $engagementWidgetAnalytics->setTotal((float) $postData['engagement']);
        $engagementWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['engagement']));
        $engagementWidgetAnalytics->setComparison($engagementComparisonTotal ?? null);
        $engagementWidgetAnalytics->setComparisonFormatted($engagementComparisonTotal ? $this->formatValue($engagementComparisonTotal) : null);
        $engagementWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Reach
        $reachComparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;
        $reachWidgetAnalytics = new WidgetAnalytics;
        $reachWidgetAnalytics->setData($aggregatedData['reach']);
        $reachWidgetAnalytics->setTotal((float) $postData['reach']);
        $reachWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['reach']));
        $reachWidgetAnalytics->setComparison($reachComparisonTotal ?? null);
        $reachWidgetAnalytics->setComparisonFormatted($reachComparisonTotal ? $this->formatValue($reachComparisonTotal) : null);
        $reachWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $impressionsComparisonTotal = $comparisonData ? (float) $comparisonData['impressions'] : null;
        $impressionsWidgetAnalytics = new WidgetAnalytics;
        $impressionsWidgetAnalytics->setData($aggregatedData['impressions']);
        $impressionsWidgetAnalytics->setTotal((float) $postData['impressions']);
        $impressionsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['impressions']));
        $impressionsWidgetAnalytics->setComparison($impressionsComparisonTotal ?? null);
        $impressionsWidgetAnalytics->setComparisonFormatted($impressionsComparisonTotal ? $this->formatValue($impressionsComparisonTotal) : null);
        $impressionsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        $postsWidgetAnalytics = new WidgetAnalytics;
        $postsWidgetAnalytics->setData($this->getPostIds());
        $postsWidgetAnalytics->setTotal(0);
        $postsWidgetAnalytics->setTotalFormatted('0');
        $postsWidgetAnalytics->setComparison(null);
        $postsWidgetAnalytics->setComparisonFormatted(null);
        $postsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $impressionsWidgetAnalytics,
            'Reach' => $reachWidgetAnalytics,
            'Engagement' => $engagementWidgetAnalytics,
            'post_ids' => $postsWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getPostData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $postModel = LinkedInOrganisationPost::query()->findOrFail($post['linkedin_organisation_post_id']);
        $comparisonData = $this->getPostComparisonData($postModel->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;

        $widgetPostAnalytics->setText(Str::limit($postModel->commentary_excerpt));
        $widgetPostAnalytics->setTotal((float) $post['engagement']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['engagement']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getPostData(): ?array
    {
        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as engagement')
            ->selectRaw('SUM(unique_impressions_count) as reach')
            ->selectRaw('SUM(impression_count) as impressions')
            ->join('linked_in_organisation_posts', 'linked_in_organisation_posts.id', '=', 'linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::LINKED_IN_COMMUNITY_TOP_POST => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST => $baseQuery->orderBy('engagement', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getDetailPostData(int $mediaItemId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(linked_in_organisation_post_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(linked_in_organisation_post_insights.date, "%Y-%m")',
        };

        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as engagement')
            ->selectRaw('SUM(unique_impressions_count) as reach')
            ->selectRaw('SUM(impression_count) as impressions')
            ->join('linked_in_organisation_posts', 'linked_in_organisation_posts.id', '=', 'linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->where('linked_in_organisation_post_insights.linkedin_organisation_post_id', $mediaItemId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->get();
    }

    protected function getPostComparisonData(int $mediaItemId): ?array
    {
        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as engagement')
            ->selectRaw('SUM(unique_impressions_count) as reach')
            ->selectRaw('SUM(impression_count) as impressions')
            ->join('linked_in_organisation_posts', 'linked_in_organisation_posts.id', '=', 'linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->where('linked_in_organisation_post_insights.linkedin_organisation_post_id', $mediaItemId)
            ->groupByRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id');

        $baseQuery->whereBetween('linked_in_organisation_post_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
            $baseQuery->whereBetween('linked_in_organisation_posts.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ]);
        }

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getPostIds(): array
    {
        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->selectRaw('(SUM(share_count) + SUM(click_count) + SUM(like_count) + SUM(comment_count)) as engagement')
            ->join('linked_in_organisation_posts', 'linked_in_organisation_posts.id', '=', 'linked_in_organisation_post_insights.linkedin_organisation_post_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('linked_in_organisation_post_insights.linkedin_organisation_post_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST) {
                $baseQuery->where('linked_in_organisation_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::LINKED_IN_COMMUNITY_TOP_POST => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::LINKED_IN_COMMUNITY_BOTTOM_POST => $baseQuery->orderBy('engagement', 'ASC'),
        };

        return $baseQuery->chunkMap(function (LinkedInOrganisationPostInsight $post) {
            $dto = new WidgetAnalyticsDataPoint;
            $dto->setValue($post->linkedin_organisation_post_id);

            return $dto;
        })->all();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
