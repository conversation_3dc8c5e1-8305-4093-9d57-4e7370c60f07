<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPostInsight;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetLinkedInOrganisationPostDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getLinkedInOrganisationPostData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $linkedInOrganisationData = $this->getLinkedInOrganisationPostData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($linkedInOrganisationData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::LINKED_IN_COMMUNITY_POST_REACH:
                    $value = (float) $dataPoint->reach;
                    $numerator += (float) $dataPoint->reach;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT:
                    $engagement = $dataPoint->share_count + $dataPoint->click_count + $dataPoint->like_count + $dataPoint->comment_count;
                    $value = (float) $engagement;
                    $numerator += (float) $engagement;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT_RATE:
                    $engagement = $dataPoint->share_count + $dataPoint->click_count + $dataPoint->like_count + $dataPoint->comment_count;
                    $value = $dataPoint->reach ? ($engagement / $dataPoint->reach) * 100 : 0;
                    $numerator += (float) $engagement;
                    $denominator += (float) $dataPoint->reach;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getLinkedInOrganisationPostData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(linked_in_organisation_post_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(linked_in_organisation_post_insights.date, "%Y-%m")',
        };

        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(unique_impressions_count) as reach')
            ->selectRaw('SUM(share_count) as share_count')
            ->selectRaw('SUM(click_count) as click_count')
            ->selectRaw('SUM(like_count) as like_count')
            ->selectRaw('SUM(comment_count) as comment_count')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('linked_in_organisation_posts', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'linked_in_organisation_posts.id',
                            operator: '=',
                            second: 'linked_in_organisation_post_insights.linkedin_organisation_post_id'
                        );
                })
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'linked_in_organisation_posts.linkedin_organisation_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: LinkedInOrganisation::class
                        );
                })
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('linked_in_organisation_post_insights.date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = LinkedInOrganisationPostInsight::query()
            ->selectRaw('SUM(unique_impressions_count) as reach')
            ->selectRaw('SUM(share_count) as share_count')
            ->selectRaw('SUM(click_count) as click_count')
            ->selectRaw('SUM(like_count) as like_count')
            ->selectRaw('SUM(comment_count) as comment_count')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('linked_in_organisation_posts', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'linked_in_organisation_posts.id',
                            operator: '=',
                            second: 'linked_in_organisation_post_insights.linkedin_organisation_post_id'
                        );
                })
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'linked_in_organisation_posts.linkedin_organisation_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: LinkedInOrganisation::class
                        );
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('linked_in_organisation_post_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $dataPoint = $baseQuery->first();
        $total = 0;

        if (! $dataPoint) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::LINKED_IN_COMMUNITY_POST_REACH:
                $total = $dataPoint->reach;
                break;
            case WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT:
                $total = $dataPoint->share_count + $dataPoint->click_count + $dataPoint->like_count + $dataPoint->comment_count;
                break;
            case WidgetType::LINKED_IN_COMMUNITY_POST_ENGAGEMENT_RATE:
                $engagement = $dataPoint->share_count + $dataPoint->click_count + $dataPoint->like_count + $dataPoint->comment_count;
                $total = $dataPoint->reach ? ($engagement / $dataPoint->reach) * 100 : 0;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
