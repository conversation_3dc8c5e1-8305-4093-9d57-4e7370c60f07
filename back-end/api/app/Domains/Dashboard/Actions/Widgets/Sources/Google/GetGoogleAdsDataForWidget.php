<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Google;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use App\Domains\Sources\Google\Support\Enums\Ads\AdvertisingChannelType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetGoogleAdsDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct(protected AdvertisingChannelType $advertisingChannelType) {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getGoogleAdsData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getGoogleAdsData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::GOOGLE_ADS_SEARCH_CLICKS:
                case WidgetType::GOOGLE_ADS_DISPLAY_CLICKS:
                case WidgetType::GOOGLE_ADS_YOUTUBE_CLICKS:
                    $value = (float) $dataPoint->clicks;
                    $numerator += (float) $dataPoint->clicks;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::GOOGLE_ADS_SEARCH_CLICK_THROUGH_RATE:
                case WidgetType::GOOGLE_ADS_DISPLAY_CLICK_THROUGH_RATE:
                case WidgetType::GOOGLE_ADS_YOUTUBE_CLICK_THROUGH_RATE:
                    $value = $dataPoint->impressions ? ($dataPoint->clicks / $dataPoint->impressions) * 100 : 0;
                    $numerator += (float) $dataPoint->clicks;
                    $denominator += (float) $dataPoint->impressions;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
                case WidgetType::GOOGLE_ADS_SEARCH_COST_PER_CLICK:
                case WidgetType::GOOGLE_ADS_DISPLAY_COST_PER_CLICK:
                case WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_CLICK:
                    $value = $dataPoint->clicks ? $dataPoint->spend / $dataPoint->clicks : 0;
                    $numerator += (float) $dataPoint->spend;
                    $denominator += (float) $dataPoint->clicks;
                    $multiplier = 1;
                    $decimals = 2;
                    break;
                case WidgetType::GOOGLE_ADS_SEARCH_IMPRESSIONS:
                case WidgetType::GOOGLE_ADS_DISPLAY_IMPRESSIONS:
                    $value = (float) $dataPoint->impressions;
                    $numerator += (float) $dataPoint->impressions;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::GOOGLE_ADS_YOUTUBE_VIDEO_VIEWS:
                    $value = (float) $dataPoint->video_views;
                    $numerator += (float) $dataPoint->video_views;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_MILE:
                    $value = $dataPoint->video_views ? ($dataPoint->spend / $dataPoint->video_views) * 1000 : 0;
                    $numerator += (float) $dataPoint->spend;
                    $denominator += (float) $dataPoint->video_views;
                    $multiplier = 1000;
                    $decimals = 2;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getGoogleAdsData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = GoogleAdCampaignInsight::query()
            ->whereHas('googleAdCampaign', function (Builder $query) {
                $query->where('advertising_channel_type', $this->advertisingChannelType);
            })
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(impressions) as impressions')
            ->selectRaw('SUM(video_views) as video_views')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('google_ad_campaigns', 'google_ad_campaign_insights.google_ad_campaign_id', '=', 'google_ad_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'google_ad_campaigns.google_ad_account_id')
                        ->where('data_sources.sourceable_type', '=', GoogleAdAccount::class);
                })
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->whereHas('googleAdCampaign', function (Builder $query) {
                $query->where('advertising_channel_type', $this->advertisingChannelType);
            })
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(impressions) as impressions')
            ->selectRaw('SUM(video_views) as video_views')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('google_ad_campaigns', 'google_ad_campaign_insights.google_ad_campaign_id', '=', 'google_ad_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'google_ad_campaigns.google_ad_account_id')
                        ->where('data_sources.sourceable_type', '=', GoogleAdAccount::class);
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $dataPoint = $baseQuery->first();
        $total = 0;

        if (! $dataPoint) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::GOOGLE_ADS_SEARCH_CLICKS:
            case WidgetType::GOOGLE_ADS_DISPLAY_CLICKS:
            case WidgetType::GOOGLE_ADS_YOUTUBE_CLICKS:
                $total = $dataPoint->clicks;
                break;
            case WidgetType::GOOGLE_ADS_SEARCH_CLICK_THROUGH_RATE:
            case WidgetType::GOOGLE_ADS_DISPLAY_CLICK_THROUGH_RATE:
            case WidgetType::GOOGLE_ADS_YOUTUBE_CLICK_THROUGH_RATE:
                $total = $dataPoint->impressions ? ($dataPoint->clicks / $dataPoint->impressions) * 100 : 0;
                break;
            case WidgetType::GOOGLE_ADS_SEARCH_COST_PER_CLICK:
            case WidgetType::GOOGLE_ADS_DISPLAY_COST_PER_CLICK:
            case WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_CLICK:
                $total = $dataPoint->clicks ? $dataPoint->spend / $dataPoint->clicks : 0;
                break;
            case WidgetType::GOOGLE_ADS_SEARCH_IMPRESSIONS:
            case WidgetType::GOOGLE_ADS_DISPLAY_IMPRESSIONS:
                $total = $dataPoint->impressions;
                break;
            case WidgetType::GOOGLE_ADS_YOUTUBE_VIDEO_VIEWS:
                $total = $dataPoint->video_views;
                break;
            case WidgetType::GOOGLE_ADS_YOUTUBE_COST_PER_MILE:
                $total = $dataPoint->video_views ? $dataPoint->spend / $dataPoint->video_views : 0;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
