<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Facebook;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Illuminate\Support\Collection;

class GetInstagramPostDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getInstagramPostData();
        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getInstagramDetailPostData($postData['instagram_media_item_id']);
        $comparisonData = $this->getInstagramPostComparisonData($postData['instagram_media_item_id']);

        $aggregatedData = [
            'reach' => [],
            'engagement' => [],
            'impressions' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Engagement
            $engagementDataPoint = new WidgetAnalyticsDataPoint;
            $engagementDataPoint->setDate($detailData['date']);
            $engagementDataPoint->setValue((float) $detailData['engagement']);
            $engagementDataPoint->setValueFormatted($this->formatValue((float) $detailData['engagement']));
            $aggregatedData['engagement'][] = $engagementDataPoint;

            // Reach
            $reachDataPoint = new WidgetAnalyticsDataPoint;
            $reachDataPoint->setDate($detailData['date']);
            $reachDataPoint->setValue((float) $detailData['reach']);
            $reachDataPoint->setValueFormatted($this->formatValue((float) $detailData['reach']));
            $aggregatedData['reach'][] = $reachDataPoint;

            // Impressions
            $impressionsDataPoint = new WidgetAnalyticsDataPoint;
            $impressionsDataPoint->setDate($detailData['date']);
            $impressionsDataPoint->setValue((float) $detailData['impressions']);
            $impressionsDataPoint->setValueFormatted($this->formatValue((float) $detailData['impressions']));
            $aggregatedData['impressions'][] = $impressionsDataPoint;
        }

        // Engagement
        $engagementComparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;
        $engagementWidgetAnalytics = new WidgetAnalytics;
        $engagementWidgetAnalytics->setData($aggregatedData['engagement']);
        $engagementWidgetAnalytics->setTotal((float) $postData['engagement']);
        $engagementWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['engagement']));
        $engagementWidgetAnalytics->setComparison($engagementComparisonTotal ?? null);
        $engagementWidgetAnalytics->setComparisonFormatted($engagementComparisonTotal ? $this->formatValue($engagementComparisonTotal) : null);
        $engagementWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Reach
        $reachComparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;
        $reachWidgetAnalytics = new WidgetAnalytics;
        $reachWidgetAnalytics->setData($aggregatedData['reach']);
        $reachWidgetAnalytics->setTotal((float) $postData['reach']);
        $reachWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['reach']));
        $reachWidgetAnalytics->setComparison($reachComparisonTotal ?? null);
        $reachWidgetAnalytics->setComparisonFormatted($reachComparisonTotal ? $this->formatValue($reachComparisonTotal) : null);
        $reachWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $impressionsComparisonTotal = $comparisonData ? (float) $comparisonData['impressions'] : null;
        $impressionsWidgetAnalytics = new WidgetAnalytics;
        $impressionsWidgetAnalytics->setData($aggregatedData['impressions']);
        $impressionsWidgetAnalytics->setTotal((float) $postData['impressions']);
        $impressionsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['impressions']));
        $impressionsWidgetAnalytics->setComparison($impressionsComparisonTotal ?? null);
        $impressionsWidgetAnalytics->setComparisonFormatted($impressionsComparisonTotal ? $this->formatValue($impressionsComparisonTotal) : null);
        $impressionsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        $postsWidgetAnalytics = new WidgetAnalytics;
        $postsWidgetAnalytics->setData($this->getPostIds());
        $postsWidgetAnalytics->setTotal(0);
        $postsWidgetAnalytics->setTotalFormatted('0');
        $postsWidgetAnalytics->setComparison(null);
        $postsWidgetAnalytics->setComparisonFormatted(null);
        $postsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $impressionsWidgetAnalytics,
            'Reach' => $reachWidgetAnalytics,
            'Engagement' => $engagementWidgetAnalytics,
            'post_ids' => $postsWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getInstagramPostData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $instagramMediaItem = InstagramMediaItem::query()->findOrFail($post['instagram_media_item_id']);
        $comparisonData = $this->getInstagramPostComparisonData($instagramMediaItem->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;

        $widgetPostAnalytics->setUrl($instagramMediaItem->media_url);
        $widgetPostAnalytics->setTotal((float) $post['engagement']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['engagement']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getInstagramPostData(): ?array
    {
        $baseQuery = InstagramMediaItemInsight::query()
            ->selectRaw('instagram_media_item_insights.instagram_media_item_id')
            ->selectRaw('SUM(total_interactions) as engagement')
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('instagram_media_items', 'instagram_media_items.id', '=', 'instagram_media_item_insights.instagram_media_item_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('instagram_media_item_insights.instagram_media_item_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::INSTAGRAM_TOP_POST => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::INSTAGRAM_BOTTOM_POST => $baseQuery->orderBy('engagement', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getInstagramDetailPostData(int $mediaItemId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(instagram_media_item_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(instagram_media_item_insights.date, "%Y-%m")',
        };

        $baseQuery = InstagramMediaItemInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(total_interactions) as engagement')
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('instagram_media_items', 'instagram_media_items.id', '=', 'instagram_media_item_insights.instagram_media_item_id')
            ->where('instagram_media_item_insights.instagram_media_item_id', $mediaItemId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->get();
    }

    protected function getInstagramPostComparisonData(int $mediaItemId): ?array
    {
        $baseQuery = InstagramMediaItemInsight::query()
            ->selectRaw('instagram_media_item_insights.instagram_media_item_id')
            ->selectRaw('SUM(total_interactions) as engagement')
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('instagram_media_items', 'instagram_media_items.id', '=', 'instagram_media_item_insights.instagram_media_item_id')
            ->where('instagram_media_item_insights.instagram_media_item_id', $mediaItemId)
            ->groupByRaw('instagram_media_item_insights.instagram_media_item_id');

        $baseQuery->whereBetween('instagram_media_item_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
            $baseQuery->whereBetween('instagram_media_items.external_date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ]);
        }

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getPostIds(): array
    {
        $baseQuery = InstagramMediaItemInsight::query()
            ->selectRaw('instagram_media_item_insights.instagram_media_item_id')
            ->selectRaw('SUM(total_interactions) as engagement')
            ->join('instagram_media_items', 'instagram_media_items.id', '=', 'instagram_media_item_insights.instagram_media_item_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('instagram_media_item_insights.instagram_media_item_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('instagram_media_item_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::INSTAGRAM_BOTTOM_POST) {
                $baseQuery->where('instagram_media_items.external_date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::INSTAGRAM_TOP_POST => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::INSTAGRAM_BOTTOM_POST => $baseQuery->orderBy('engagement', 'ASC'),
        };

        return $baseQuery->chunkMap(function (InstagramMediaItemInsight $post) {
            $dto = new WidgetAnalyticsDataPoint;
            $dto->setValue($post->instagram_media_item_id);

            return $dto;
        })->all();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
