<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Facebook;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GetTopMetaCampaignDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct(protected $publisherPlatforms) {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getCampaignData();
        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getCampaignDetailData($postData['facebook_campaign_id']);
        $comparisonData = $this->getCampaignComparisonData($postData['facebook_campaign_id']);

        $aggregatedData = [
            'reach' => [],
            'spend' => [],
            'clicks' => [],
            'impressions' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Spend
            $spendDataPoint = new WidgetAnalyticsDataPoint;
            $spendDataPoint->setDate($detailData['date']);
            $spendDataPoint->setValue((float) $detailData['spend']);
            $spendDataPoint->setValueFormatted($this->formatValue((float) $detailData['spend']));
            $aggregatedData['spend'][] = $spendDataPoint;

            // Clicks
            $clicksDataPoint = new WidgetAnalyticsDataPoint;
            $clicksDataPoint->setDate($detailData['date']);
            $clicksDataPoint->setValue((float) $detailData['clicks']);
            $clicksDataPoint->setValueFormatted($this->formatValue((float) $detailData['clicks']));
            $aggregatedData['clicks'][] = $clicksDataPoint;

            // Reach
            $reachDataPoint = new WidgetAnalyticsDataPoint;
            $reachDataPoint->setDate($detailData['date']);
            $reachDataPoint->setValue((float) $detailData['reach']);
            $reachDataPoint->setValueFormatted($this->formatValue((float) $detailData['reach']));
            $aggregatedData['reach'][] = $reachDataPoint;

            // Impressions
            $impressionsDataPoint = new WidgetAnalyticsDataPoint;
            $impressionsDataPoint->setDate($detailData['date']);
            $impressionsDataPoint->setValue((float) $detailData['impressions']);
            $impressionsDataPoint->setValueFormatted($this->formatValue((float) $detailData['impressions']));
            $aggregatedData['impressions'][] = $impressionsDataPoint;
        }

        // Spend
        $spendComparisonTotal = $comparisonData ? (float) $comparisonData['spend'] : null;
        $spendWidgetAnalytics = new WidgetAnalytics;
        $spendWidgetAnalytics->setData($aggregatedData['spend']);
        $spendWidgetAnalytics->setTotal((float) $postData['spend']);
        $spendWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['spend']));
        $spendWidgetAnalytics->setComparison($spendComparisonTotal ?? null);
        $spendWidgetAnalytics->setComparisonFormatted($spendComparisonTotal ? $this->formatValue($spendComparisonTotal) : null);
        $spendWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Clicks
        $clicksComparisonTotal = $comparisonData ? (float) $comparisonData['clicks'] : null;
        $clicksWidgetAnalytics = new WidgetAnalytics;
        $clicksWidgetAnalytics->setData($aggregatedData['clicks']);
        $clicksWidgetAnalytics->setTotal((float) $postData['clicks']);
        $clicksWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['clicks']));
        $clicksWidgetAnalytics->setComparison($clicksComparisonTotal ?? null);
        $clicksWidgetAnalytics->setComparisonFormatted($clicksComparisonTotal ? $this->formatValue($clicksComparisonTotal) : null);
        $clicksWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Reach
        $reachComparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;
        $reachWidgetAnalytics = new WidgetAnalytics;
        $reachWidgetAnalytics->setData($aggregatedData['reach']);
        $reachWidgetAnalytics->setTotal((float) $postData['reach']);
        $reachWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['reach']));
        $reachWidgetAnalytics->setComparison($reachComparisonTotal ?? null);
        $reachWidgetAnalytics->setComparisonFormatted($reachComparisonTotal ? $this->formatValue($reachComparisonTotal) : null);
        $reachWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $impressionsComparisonTotal = $comparisonData ? (float) $comparisonData['impressions'] : null;
        $impressionsWidgetAnalytics = new WidgetAnalytics;
        $impressionsWidgetAnalytics->setData($aggregatedData['impressions']);
        $impressionsWidgetAnalytics->setTotal((float) $postData['impressions']);
        $impressionsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['impressions']));
        $impressionsWidgetAnalytics->setComparison($impressionsComparisonTotal ?? null);
        $impressionsWidgetAnalytics->setComparisonFormatted($impressionsComparisonTotal ? $this->formatValue($impressionsComparisonTotal) : null);
        $impressionsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $impressionsWidgetAnalytics,
            'Reach' => $reachWidgetAnalytics,
            'Spend' => $spendWidgetAnalytics,
            'Clicks' => $clicksWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getCampaignData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $campaign = FacebookCampaign::query()->findOrFail($post['facebook_campaign_id']);
        $comparisonData = $this->getCampaignComparisonData($campaign->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;

        $widgetPostAnalytics->setText(Str::limit($post['name']));
        $widgetPostAnalytics->setTotal((float) $post['reach']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['reach']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getCampaignData(): ?array
    {
        $baseQuery = FacebookCampaignInsight::query()
            ->whereIn('publisher_platform', $this->publisherPlatforms)
            ->selectRaw('facebook_campaign_insights.facebook_campaign_id')
            ->selectRaw('facebook_campaigns.name as name')
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('facebook_campaigns', 'facebook_campaigns.id', '=', 'facebook_campaign_insights.facebook_campaign_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('facebook_campaign_insights.facebook_campaign_id')
            ->groupByRaw('facebook_campaigns.name');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_campaign_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_campaign_insights.date', '<=', $this->filter->getEndDate());
        }

        match ($this->widget->type) {
            WidgetType::META_ADS_FACEBOOK_TOP_CAMPAIGN, WidgetType::META_ADS_INSTAGRAM_TOP_CAMPAIGN => $baseQuery->orderBy('clicks', 'DESC'),
            WidgetType::META_ADS_FACEBOOK_BOTTOM_CAMPAIGN, WidgetType::META_ADS_INSTAGRAM_BOTTOM_CAMPAIGN => $baseQuery->orderBy('clicks', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getCampaignDetailData(int $campaignId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(facebook_campaign_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(facebook_campaign_insights.date, "%Y-%m")',
        };

        $baseQuery = FacebookCampaignInsight::query()
            ->whereIn('publisher_platform', $this->publisherPlatforms)
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('facebook_campaigns', 'facebook_campaigns.id', '=', 'facebook_campaign_insights.facebook_campaign_id')
            ->where('facebook_campaign_insights.facebook_campaign_id', $campaignId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_campaign_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_campaign_insights.date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getCampaignComparisonData(int $campaignId): ?array
    {
        $baseQuery = FacebookCampaignInsight::query()
            ->whereIn('publisher_platform', $this->publisherPlatforms)
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->join('facebook_campaigns', 'facebook_campaigns.id', '=', 'facebook_campaign_insights.facebook_campaign_id')
            ->where('facebook_campaign_insights.facebook_campaign_id', $campaignId)
            ->groupByRaw('facebook_campaign_insights.facebook_campaign_id');

        $baseQuery->whereBetween('facebook_campaign_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
