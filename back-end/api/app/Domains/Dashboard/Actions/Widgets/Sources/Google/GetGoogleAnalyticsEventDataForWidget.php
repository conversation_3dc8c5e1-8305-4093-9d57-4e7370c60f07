<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Google;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class GetGoogleAnalyticsEventDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPieAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getGoogleAnalyticsReportData());
    }

    /**
     * @return array<string, WidgetPieAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getGoogleAnalyticsReportData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getGoogleAnalyticsReportData(?string $detailKey = null): Collection
    {
        $baseQuery = $this->getBaseQuery($detailKey);

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetPieAnalytics
    {
        $formattedData = [];
        $subTotal = 0;
        $comparisonTotal = null;

        foreach ($dataSet as $dataPoint) {
            $value = (float) $dataPoint->value;
            $subTotal += $value;

            $widgetPieAnalyticsDataPoint = new WidgetPieAnalyticsDataPoint;
            $widgetPieAnalyticsDataPoint->setValue($value);
            $widgetPieAnalyticsDataPoint->setValueFormatted($this->formatValue($value));
            $widgetPieAnalyticsDataPoint->setName($dataPoint->name);

            $formattedData[] = $widgetPieAnalyticsDataPoint;
        }

        $comparisons = $this->getComparisonData($detailKey, $detailValue);

        foreach ($comparisons ?? [] as $comparisonPoint) {
            $value = (float) $comparisonPoint->value;
            $comparisonTotal += $value;

            /** @var WidgetPieAnalyticsDataPoint $dataPoint */
            $dataPoint = Arr::first(array_filter($formattedData, fn (WidgetPieAnalyticsDataPoint $dataPoint) => $dataPoint->getName() === $comparisonPoint->name));

            if (! $dataPoint) {
                continue;
            }

            $dataPoint->setComparison($value);
            $dataPoint->setComparisonFormatted($this->formatValue($value));
        }

        $widgetAnalytics = new WidgetPieAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($subTotal);
        $widgetAnalytics->setTotalFormatted($this->formatValue($subTotal));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getComparisonData(?string $detailKey = null, ?string $detailValue = null): ?Collection
    {
        $baseQuery = $this->getBaseQuery($detailKey, $detailValue);

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        return $baseQuery->get();
    }

    protected function formatValue(float $value): string
    {
        return number_format(
            num: $value,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getReportType(): ReportType
    {
        return match ($this->widget->type) {
            WidgetType::GOOGLE_ANALYTICS_CHANNEL_GROUPS => ReportType::CHANNEL_GROUP,
        };
    }

    protected function getBaseQuery(?string $detailKey = null, ?string $detailValue = null): Builder
    {
        $query = GoogleAnalyticsReport::query()
            ->selectRaw('SUM(value) as value')
            ->selectRaw('name')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->where('type', $this->getReportType()->value)
            ->groupBy('name');

        if ($detailKey) {
            $query
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'google_analytics_reports.google_analytics_property_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: GoogleAnalyticsProperty::class
                        );
                })
                ->when((bool) $detailValue, fn (Builder $query) => $query->where(sprintf('data_sources.%s', $detailKey), $detailValue))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        return $query;
    }
}
