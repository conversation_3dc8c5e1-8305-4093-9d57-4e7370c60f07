<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Mailchimp;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetMailchimpDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getMailchimpReportData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getMailchimpReportData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $decimals = 0;
        $multiplier = 1;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::MAILCHIMP_SENDS:
                    $value = (float) $dataPoint->emails_sent;
                    $numerator += (float) $dataPoint->emails_sent;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::MAILCHIMP_OPEN_RATE:
                    $value = $dataPoint->emails_sent ? (($dataPoint->unique_opens / $dataPoint->emails_sent)) * 100 : 0;
                    $numerator += (float) $dataPoint->unique_opens;
                    $denominator += (float) $dataPoint->emails_sent;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
                case WidgetType::MAILCHIMP_CLICK_THROUGH_RATE:
                    $value = $dataPoint->emails_sent ? (($dataPoint->unique_clicks / $dataPoint->emails_sent)) * 100 : 0;
                    $numerator += (float) $dataPoint->unique_clicks;
                    $denominator += (float) $dataPoint->emails_sent;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue($value);
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getMailchimpReportData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = MailchimpReport::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(emails_sent) as emails_sent')
            ->selectRaw('SUM(unique_opens) as unique_opens')
            ->selectRaw('SUM(unique_clicks) as unique_clicks')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('mailchimp_campaigns', 'mailchimp_reports.mailchimp_campaign_id', '=', 'mailchimp_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'mailchimp_campaigns.mailchimp_audience_id')
                        ->where('data_sources.sourceable_type', '=', MailchimpAudience::class);
                })
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = MailchimpReport::query()
            ->selectRaw('SUM(emails_sent) as emails_sent')
            ->selectRaw('SUM(unique_opens) as unique_opens')
            ->selectRaw('SUM(unique_clicks) as unique_clicks')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('mailchimp_campaigns', 'mailchimp_reports.mailchimp_campaign_id', '=', 'mailchimp_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'mailchimp_campaigns.mailchimp_audience_id')
                        ->where('data_sources.sourceable_type', '=', MailchimpAudience::class);
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $dataPoint = $baseQuery->first();
        $total = 0;

        if (! $dataPoint) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::MAILCHIMP_SENDS:
                $total = $dataPoint->emails_sent;
                break;
            case WidgetType::MAILCHIMP_OPEN_RATE:
                $total = $dataPoint->emails_sent ? ($dataPoint->unique_opens / $dataPoint->emails_sent) * 100 : 0;
                break;
            case WidgetType::MAILCHIMP_CLICK_THROUGH_RATE:
                $total = $dataPoint->emails_sent ? ($dataPoint->unique_clicks / $dataPoint->emails_sent) * 100 : 0;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
