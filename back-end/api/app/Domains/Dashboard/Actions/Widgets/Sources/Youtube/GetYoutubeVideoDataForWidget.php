<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Youtube;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use Illuminate\Support\Collection;

class GetYoutubeVideoDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getYoutubeVideoData();
        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getYoutubeDetailVideoData($postData['youtube_video_id']);
        $comparisonData = $this->getYoutubeVideoComparisonData($postData['youtube_video_id']);

        $aggregatedData = [
            'engagement' => [],
            'views' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Engagement
            $engagementDataPoint = new WidgetAnalyticsDataPoint;
            $engagementDataPoint->setDate($detailData['date']);
            $engagementDataPoint->setValue((float) $detailData['engagement']);
            $engagementDataPoint->setValueFormatted($this->formatValue((float) $detailData['engagement']));
            $aggregatedData['engagement'][] = $engagementDataPoint;

            // Impressions
            $viewsDataPoint = new WidgetAnalyticsDataPoint;
            $viewsDataPoint->setDate($detailData['date']);
            $viewsDataPoint->setValue((float) $detailData['views']);
            $viewsDataPoint->setValueFormatted($this->formatValue((float) $detailData['views']));
            $aggregatedData['views'][] = $viewsDataPoint;
        }

        // Engagement
        $engagementComparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;
        $engagementWidgetAnalytics = new WidgetAnalytics;
        $engagementWidgetAnalytics->setData($aggregatedData['engagement']);
        $engagementWidgetAnalytics->setTotal((float) $postData['engagement']);
        $engagementWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['engagement']));
        $engagementWidgetAnalytics->setComparison($engagementComparisonTotal ?? null);
        $engagementWidgetAnalytics->setComparisonFormatted($engagementComparisonTotal ? $this->formatValue($engagementComparisonTotal) : null);
        $engagementWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $viewsComparisonTotal = $comparisonData ? (float) $comparisonData['views'] : null;
        $viewsWidgetAnalytics = new WidgetAnalytics;
        $viewsWidgetAnalytics->setData($aggregatedData['views']);
        $viewsWidgetAnalytics->setTotal((float) $postData['views']);
        $viewsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['views']));
        $viewsWidgetAnalytics->setComparison($viewsComparisonTotal ?? null);
        $viewsWidgetAnalytics->setComparisonFormatted($viewsComparisonTotal ? $this->formatValue($viewsComparisonTotal) : null);
        $viewsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        $postsWidgetAnalytics = new WidgetAnalytics;
        $postsWidgetAnalytics->setData($this->getPostIds());
        $postsWidgetAnalytics->setTotal(0);
        $postsWidgetAnalytics->setTotalFormatted('0');
        $postsWidgetAnalytics->setComparison(null);
        $postsWidgetAnalytics->setComparisonFormatted(null);
        $postsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $viewsWidgetAnalytics,
            'Engagement' => $engagementWidgetAnalytics,
            'post_ids' => $postsWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getYoutubeVideoData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $youtubeVideo = YoutubeVideo::query()->findOrFail($post['youtube_video_id']);
        $comparisonData = $this->getYoutubeVideoComparisonData($youtubeVideo->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;

        $widgetPostAnalytics->setUrl($youtubeVideo->thumbnail_url);
        $widgetPostAnalytics->setTotal((float) $post['engagement']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['engagement']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getYoutubeVideoData(): ?array
    {
        $baseQuery = YoutubeVideoInsight::query()
            ->selectRaw('youtube_video_insights.youtube_video_id')
            ->selectRaw('SUM(engagement) as engagement')
            ->selectRaw('SUM(views) as views')
            ->join('youtube_videos', 'youtube_videos.id', '=', 'youtube_video_insights.youtube_video_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('youtube_video_insights.youtube_video_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('youtube_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('youtube_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::YOUTUBE_TOP_VIDEO => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::YOUTUBE_BOTTOM_VIDEO => $baseQuery->orderBy('engagement', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getYoutubeDetailVideoData(int $mediaItemId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(youtube_video_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(youtube_video_insights.date, "%Y-%m")',
        };

        $baseQuery = YoutubeVideoInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(engagement) as engagement')
            ->selectRaw('SUM(views) as views')
            ->join('youtube_videos', 'youtube_videos.id', '=', 'youtube_video_insights.youtube_video_id')
            ->where('youtube_video_insights.youtube_video_id', $mediaItemId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('youtube_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('youtube_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->get();
    }

    protected function getYoutubeVideoComparisonData(int $mediaItemId): ?array
    {
        $baseQuery = YoutubeVideoInsight::query()
            ->selectRaw('youtube_video_insights.youtube_video_id')
            ->selectRaw('SUM(engagement) as engagement')
            ->selectRaw('SUM(views) as views')
            ->join('youtube_videos', 'youtube_videos.id', '=', 'youtube_video_insights.youtube_video_id')
            ->where('youtube_video_insights.youtube_video_id', $mediaItemId)
            ->groupByRaw('youtube_video_insights.youtube_video_id');

        $baseQuery->whereBetween('youtube_video_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
            $baseQuery->whereBetween('youtube_videos.published_at', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ]);
        }

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getPostIds(): array
    {
        $baseQuery = YoutubeVideoInsight::query()
            ->selectRaw('youtube_video_insights.youtube_video_id')
            ->selectRaw('SUM(engagement) as engagement')
            ->join('youtube_videos', 'youtube_videos.id', '=', 'youtube_video_insights.youtube_video_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('youtube_video_insights.youtube_video_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('youtube_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('youtube_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::YOUTUBE_BOTTOM_VIDEO) {
                $baseQuery->where('youtube_videos.published_at', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::YOUTUBE_TOP_VIDEO => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::YOUTUBE_BOTTOM_VIDEO => $baseQuery->orderBy('engagement', 'ASC'),
        };

        return $baseQuery->chunkMap(function (YoutubeVideoInsight $post) {
            $dto = new WidgetAnalyticsDataPoint;
            $dto->setValue($post->youtube_video_id);

            return $dto;
        })->all();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
