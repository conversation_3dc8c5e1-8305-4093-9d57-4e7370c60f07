<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Facebook;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Meta\Models\FacebookPagePost;
use App\Domains\Sources\Meta\Models\FacebookPagePostInsight;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GetFacebookPostDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getPostData();

        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getDetailPostData($postData['facebook_page_post_id']);
        $comparisonData = $this->getPostComparisonData($postData['facebook_page_post_id']);

        $aggregatedData = [
            'reach' => [],
            'clicks' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Clicks
            $clicksDataPoint = new WidgetAnalyticsDataPoint;
            $clicksDataPoint->setDate($detailData['date']);
            $clicksDataPoint->setValue((float) $detailData['clicks']);
            $clicksDataPoint->setValueFormatted($this->formatValue((float) $detailData['clicks']));
            $aggregatedData['clicks'][] = $clicksDataPoint;

            // Reach
            $reachDataPoint = new WidgetAnalyticsDataPoint;
            $reachDataPoint->setDate($detailData['date']);
            $reachDataPoint->setValue((float) $detailData['reach']);
            $reachDataPoint->setValueFormatted($this->formatValue((float) $detailData['reach']));
            $aggregatedData['reach'][] = $reachDataPoint;
        }

        // Clicks
        $clicksComparisonTotal = $comparisonData ? (float) $comparisonData['clicks'] : null;
        $clicksWidgetAnalytics = new WidgetAnalytics;
        $clicksWidgetAnalytics->setData($aggregatedData['clicks']);
        $clicksWidgetAnalytics->setTotal((float) $postData['clicks']);
        $clicksWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['clicks']));
        $clicksWidgetAnalytics->setComparison($clicksComparisonTotal ?? null);
        $clicksWidgetAnalytics->setComparisonFormatted($clicksComparisonTotal ? $this->formatValue($clicksComparisonTotal) : null);
        $clicksWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Reach
        $reachComparisonTotal = $comparisonData ? (float) $comparisonData['reach'] : null;
        $reachWidgetAnalytics = new WidgetAnalytics;
        $reachWidgetAnalytics->setData($aggregatedData['reach']);
        $reachWidgetAnalytics->setTotal((float) $postData['reach']);
        $reachWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['reach']));
        $reachWidgetAnalytics->setComparison($reachComparisonTotal ?? null);
        $reachWidgetAnalytics->setComparisonFormatted($reachComparisonTotal ? $this->formatValue($reachComparisonTotal) : null);
        $reachWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        $postsWidgetAnalytics = new WidgetAnalytics;
        $postsWidgetAnalytics->setData($this->getPostsIds());
        $postsWidgetAnalytics->setTotal(0);
        $postsWidgetAnalytics->setTotalFormatted('0');
        $postsWidgetAnalytics->setComparison(null);
        $postsWidgetAnalytics->setComparisonFormatted(null);
        $postsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Reach' => $reachWidgetAnalytics,
            'Clicks' => $clicksWidgetAnalytics,
            'post_ids' => $postsWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getPostData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $postModel = FacebookPagePost::query()->findOrFail($post['facebook_page_post_id']);
        $comparisonData = $this->getPostComparisonData($postModel->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['clicks'] : null;

        $widgetPostAnalytics->setUrl($postModel->media_url);
        $widgetPostAnalytics->setText(Str::limit($postModel->message));
        $widgetPostAnalytics->setTotal((float) $post['clicks']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['clicks']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getPostData(): ?array
    {
        $baseQuery = FacebookPagePostInsight::query()
            ->selectRaw('facebook_page_post_insights.facebook_page_post_id')
            ->selectRaw('SUM(post_clicks) as clicks')
            ->selectRaw('SUM(post_impressions_unique) as reach')
            ->join('facebook_page_posts', 'facebook_page_posts.id', '=', 'facebook_page_post_insights.facebook_page_post_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('facebook_page_post_insights.facebook_page_post_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::FACEBOOK_TOP_POST => $baseQuery->orderBy('clicks', 'DESC'),
            WidgetType::FACEBOOK_BOTTOM_POST => $baseQuery->orderBy('clicks', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getDetailPostData(int $mediaItemId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(facebook_page_post_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(facebook_page_post_insights.date, "%Y-%m")',
        };

        $baseQuery = FacebookPagePostInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(post_clicks) as clicks')
            ->selectRaw('SUM(post_impressions_unique) as reach')
            ->join('facebook_page_posts', 'facebook_page_posts.id', '=', 'facebook_page_post_insights.facebook_page_post_id')
            ->where('facebook_page_post_insights.facebook_page_post_id', $mediaItemId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->get();
    }

    protected function getPostComparisonData(int $mediaItemId): ?array
    {
        $baseQuery = FacebookPagePostInsight::query()
            ->selectRaw('facebook_page_post_insights.facebook_page_post_id')
            ->selectRaw('SUM(post_clicks) as clicks')
            ->selectRaw('SUM(post_impressions_unique) as reach')
            ->join('facebook_page_posts', 'facebook_page_posts.id', '=', 'facebook_page_post_insights.facebook_page_post_id')
            ->where('facebook_page_post_insights.facebook_page_post_id', $mediaItemId)
            ->groupByRaw('facebook_page_post_insights.facebook_page_post_id');

        $baseQuery->whereBetween('facebook_page_post_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
            $baseQuery->whereBetween('facebook_page_posts.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ]);
        }

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getPostsIds(): array
    {
        $baseQuery = FacebookPagePostInsight::query()
            ->selectRaw('facebook_page_post_insights.facebook_page_post_id')
            ->selectRaw('SUM(post_clicks) as clicks')
            ->join('facebook_page_posts', 'facebook_page_posts.id', '=', 'facebook_page_post_insights.facebook_page_post_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupBy('facebook_page_post_insights.facebook_page_post_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('facebook_page_post_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::FACEBOOK_BOTTOM_POST) {
                $baseQuery->where('facebook_page_posts.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::FACEBOOK_TOP_POST => $baseQuery->orderBy('clicks', 'DESC'),
            WidgetType::FACEBOOK_BOTTOM_POST => $baseQuery->orderBy('clicks', 'ASC'),
        };

        return $baseQuery->chunkMap(function (FacebookPagePostInsight $post) {
            $dto = new WidgetAnalyticsDataPoint;
            $dto->setValue($post->facebook_page_post_id);

            return $dto;
        })->all();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
