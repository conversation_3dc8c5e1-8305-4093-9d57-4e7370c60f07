<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\TikTok;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\TikTok\Models\TikTokVideo;
use App\Domains\Sources\TikTok\Models\TikTokVideoInsight;
use Illuminate\Support\Collection;

class GetTikTokVideoDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetPostAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetPostAnalytics();
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $postData = $this->getVideoData();
        if (! $postData) {
            return [];
        }

        $detailPostData = $this->getDetailVideoData($postData['tik_tok_video_id']);
        $comparisonData = $this->getVideoComparisonData($postData['tik_tok_video_id']);

        $aggregatedData = [
            'engagement' => [],
            'views' => [],
        ];

        foreach ($detailPostData as $detailData) {
            // Engagement
            $engagementDataPoint = new WidgetAnalyticsDataPoint;
            $engagementDataPoint->setDate($detailData['date']);
            $engagementDataPoint->setValue((float) $detailData['engagement']);
            $engagementDataPoint->setValueFormatted($this->formatValue((float) $detailData['engagement']));
            $aggregatedData['engagement'][] = $engagementDataPoint;

            // Impressions
            $viewsDataPoint = new WidgetAnalyticsDataPoint;
            $viewsDataPoint->setDate($detailData['date']);
            $viewsDataPoint->setValue((float) $detailData['views']);
            $viewsDataPoint->setValueFormatted($this->formatValue((float) $detailData['views']));
            $aggregatedData['views'][] = $viewsDataPoint;
        }

        // Engagement
        $engagementComparisonTotal = $comparisonData ? (float) $comparisonData['engagement'] : null;
        $engagementWidgetAnalytics = new WidgetAnalytics;
        $engagementWidgetAnalytics->setData($aggregatedData['engagement']);
        $engagementWidgetAnalytics->setTotal((float) $postData['engagement']);
        $engagementWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['engagement']));
        $engagementWidgetAnalytics->setComparison($engagementComparisonTotal ?? null);
        $engagementWidgetAnalytics->setComparisonFormatted($engagementComparisonTotal ? $this->formatValue($engagementComparisonTotal) : null);
        $engagementWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        // Impressions
        $viewsComparisonTotal = $comparisonData ? (float) $comparisonData['views'] : null;
        $viewsWidgetAnalytics = new WidgetAnalytics;
        $viewsWidgetAnalytics->setData($aggregatedData['views']);
        $viewsWidgetAnalytics->setTotal((float) $postData['views']);
        $viewsWidgetAnalytics->setTotalFormatted($this->formatValue((float) $postData['views']));
        $viewsWidgetAnalytics->setComparison($viewsComparisonTotal ?? null);
        $viewsWidgetAnalytics->setComparisonFormatted($viewsComparisonTotal ? $this->formatValue($viewsComparisonTotal) : null);
        $viewsWidgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return [
            'Impressions' => $viewsWidgetAnalytics,
            'Engagement' => $engagementWidgetAnalytics,
        ];
    }

    protected function getWidgetPostAnalytics(): WidgetPostAnalytics
    {
        $post = $this->getVideoData();
        $widgetPostAnalytics = new WidgetPostAnalytics;

        if (! $post) {
            return $widgetPostAnalytics;
        }

        $video = TikTokVideo::query()->findOrFail($post['tik_tok_video_id']);
        $comparisonData = $this->getVideoComparisonData($video->id);

        $comparisonTotal = $comparisonData ? (float) $comparisonData['views'] : null;

        $widgetPostAnalytics->setUrl(route('dashboard.file.show', ['path' => $video->image_url]));
        $widgetPostAnalytics->setTotal((float) $post['views']);
        $widgetPostAnalytics->setTotalFormatted($this->formatValue((float) $post['views']));
        $widgetPostAnalytics->setComparison($comparisonTotal ?? null);
        $widgetPostAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);

        return $widgetPostAnalytics;
    }

    protected function getVideoData(): ?array
    {
        $baseQuery = TiktokVideoInsight::query()
            ->selectRaw('tik_tok_video_insights.tik_tok_video_id')
            ->selectRaw('(SUM(comment_count) + SUM(share_count) + SUM(like_count)) as engagement')
            ->selectRaw('SUM(view_count) as views')
            ->join('tik_tok_videos', 'tik_tok_videos.id', '=', 'tik_tok_video_insights.tik_tok_video_id')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw('tik_tok_video_insights.tik_tok_video_id');

        if ($this->filter->getStartDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::TIKTOK_BOTTOM_VIDEO) {
                $baseQuery->where('tik_tok_videos.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::TIKTOK_BOTTOM_VIDEO) {
                $baseQuery->where('tik_tok_videos.date', '<=', $this->filter->getEndDate());
            }
        }

        match ($this->widget->type) {
            WidgetType::TIKTOK_TOP_VIDEO => $baseQuery->orderBy('engagement', 'DESC'),
            WidgetType::TIKTOK_BOTTOM_VIDEO => $baseQuery->orderBy('engagement', 'ASC'),
        };

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function getDetailVideoData(int $mediaItemId): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(tik_tok_video_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(tik_tok_video_insights.date, "%Y-%m")',
        };

        $baseQuery = TikTokVideoInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('(SUM(comment_count) + SUM(share_count) + SUM(like_count)) as engagement')
            ->selectRaw('SUM(view_count) as views')
            ->join('tik_tok_videos', 'tik_tok_videos.id', '=', 'tik_tok_video_insights.tik_tok_video_id')
            ->where('tik_tok_video_insights.tik_tok_video_id', $mediaItemId)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($this->filter->getStartDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '>=', $this->filter->getStartDate());

            if ($this->widget->type === WidgetType::TIKTOK_BOTTOM_VIDEO) {
                $baseQuery->where('tik_tok_videos.date', '>=', $this->filter->getStartDate());
            }
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '<=', $this->filter->getEndDate());

            if ($this->widget->type === WidgetType::TIKTOK_BOTTOM_VIDEO) {
                $baseQuery->where('tik_tok_videos.date', '<=', $this->filter->getEndDate());
            }
        }

        return $baseQuery->get();
    }

    protected function getVideoComparisonData(int $mediaItemId): ?array
    {
        $baseQuery = TikTokVideoInsight::query()
            ->selectRaw('tik_tok_video_insights.tik_tok_video_id')
            ->selectRaw('(SUM(comment_count) + SUM(share_count) + SUM(like_count)) as engagement')
            ->selectRaw('SUM(view_count) as views')
            ->join('tik_tok_videos', 'tik_tok_videos.id', '=', 'tik_tok_video_insights.tik_tok_video_id')
            ->where('tik_tok_video_insights.tik_tok_video_id', $mediaItemId)
            ->groupByRaw('tik_tok_video_insights.tik_tok_video_id');

        $baseQuery->whereBetween('tik_tok_video_insights.date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        if ($this->widget->type === WidgetType::TIKTOK_BOTTOM_VIDEO) {
            $baseQuery->whereBetween('tik_tok_videos.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ]);
        }

        $item = $baseQuery->first();

        if (! $item) {
            return null;
        }

        return $item->toArray();
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
