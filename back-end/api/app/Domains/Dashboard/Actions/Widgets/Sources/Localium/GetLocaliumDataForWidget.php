<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Localium;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfileReport;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetLocaliumDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getGoogleBusinessProfilesData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleBusinessProfilesData = $this->getGoogleBusinessProfilesData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleBusinessProfilesData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::LOCALIUM_PHONE_CALLS:
                    $value = (float) $dataPoint->phone_calls;
                    $numerator += (float) $dataPoint->phone_calls;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LOCALIUM_VIEWS_MAPS:
                    $value = (float) $dataPoint->views_maps;
                    $numerator += (float) $dataPoint->views_maps;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LOCALIUM_VIEWS_GOOGLE:
                    $value = (float) $dataPoint->views_google;
                    $numerator += (float) $dataPoint->views_google;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LOCALIUM_VIEWS_DIRECTIONS:
                    $value = (float) $dataPoint->views_directions;
                    $numerator += (float) $dataPoint->views_directions;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getGoogleBusinessProfilesData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = GoogleBusinessProfileReport::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(views_google) as views_google')
            ->selectRaw('SUM(views_maps) as views_maps')
            ->selectRaw('SUM(views_directions) as views_directions')
            ->selectRaw('SUM(phone_calls) as phone_calls')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'google_business_profile_reports.google_business_profile_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: GoogleBusinessProfile::class
                        );
                })
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = GoogleBusinessProfileReport::query()
            ->selectRaw('SUM(views_google) as views_google')
            ->selectRaw('SUM(views_maps) as views_maps')
            ->selectRaw('SUM(views_directions) as views_directions')
            ->selectRaw('SUM(phone_calls) as phone_calls')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'google_business_profile_reports.google_business_profile_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: GoogleBusinessProfile::class
                        );
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $dataPoint = $baseQuery->first();
        $total = 0;

        if (! $dataPoint) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::LOCALIUM_PHONE_CALLS:
                $total = $dataPoint->phone_calls;
                break;
            case WidgetType::LOCALIUM_VIEWS_MAPS:
                $total = $dataPoint->views_maps;
                break;
            case WidgetType::LOCALIUM_VIEWS_GOOGLE:
                $total = $dataPoint->views_google;
                break;
            case WidgetType::LOCALIUM_VIEWS_DIRECTIONS:
                $total = $dataPoint->views_directions;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
