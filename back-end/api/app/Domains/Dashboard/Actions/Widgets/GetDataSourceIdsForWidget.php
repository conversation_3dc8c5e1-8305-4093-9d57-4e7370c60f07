<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets;

use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;

class GetDataSourceIdsForWidget
{
    protected Widget $widget;

    public function execute(Widget $widget, WidgetAnalyticsFilter $widgetAnalyticsFilter): array
    {
        $baseQuery = $widget
            ->section
            ->dashboard
            ->dataSources();

        if ($widgetAnalyticsFilter->getRegions()) {
            $baseQuery->whereIn('region', $widgetAnalyticsFilter->getRegions());
        }

        if ($widgetAnalyticsFilter->getChannels()) {
            $baseQuery->whereIn('channel', $widgetAnalyticsFilter->getChannels());
        }

        if ($widgetAnalyticsFilter->getBusinessUnits()) {
            $baseQuery->whereIn('business_unit', $widgetAnalyticsFilter->getBusinessUnits());
        }

        return $baseQuery
            ->pluck('id')
            ->all();
    }
}
