<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Dashboard;

use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;

class StoreDashboardViewForDashboard
{
    public function execute(Dashboard $dashboard, string $name, WidgetAnalyticsFilter $widgetAnalyticsFilter)
    {
        return DashboardView::query()->create([
            'dashboard_id' => $dashboard->id,
            'name' => $name,
            'start_date' => $widgetAnalyticsFilter->getStartDate(),
            'end_date' => $widgetAnalyticsFilter->getEndDate(),
            'channels' => $widgetAnalyticsFilter->getChannels(),
            'regions' => $widgetAnalyticsFilter->getRegions(),
            'business_units' => $widgetAnalyticsFilter->getBusinessUnits(),
        ]);
    }
}
