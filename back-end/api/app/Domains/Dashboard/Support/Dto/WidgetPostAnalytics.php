<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

class WidgetPostAnalytics
{
    private float $total;

    private string $totalFormatted;

    private ?float $comparison;

    private ?string $comparisonFormatted;

    private ?string $url = null;

    private ?string $text = null;

    public function getTotal(): ?float
    {
        return $this->total ?? null;
    }

    public function setTotal(float $total): void
    {
        $this->total = $total;
    }

    public function getTotalFormatted(): ?string
    {
        return $this->totalFormatted ?? null;
    }

    public function setTotalFormatted(string $totalFormatted): void
    {
        $this->totalFormatted = $totalFormatted;
    }

    public function getComparison(): ?float
    {
        return $this->comparison ?? null;
    }

    public function setComparison(?float $comparison): void
    {
        $this->comparison = $comparison;
    }

    public function getComparisonFormatted(): ?string
    {
        return $this->comparisonFormatted ?? null;
    }

    public function setComparisonFormatted(?string $comparisonFormatted): void
    {
        $this->comparisonFormatted = $comparisonFormatted;
    }

    public function getUrl(): ?string
    {
        return $this->url ?? null;
    }

    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }

    public function getText(): ?string
    {
        return $this->text ?? null;
    }

    public function setText(?string $text): void
    {
        $this->text = $text;
    }
}
