<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;

class WidgetPieAnalytics
{
    private array $data;

    private float $total;

    private string $totalFormatted;

    private ?float $comparison;

    private ?string $comparisonFormatted;

    private WidgetAccuracy $widgetAccuracy;

    public function getData(): array
    {
        return $this->data;
    }

    public function setData(array $data): void
    {
        $this->data = $data;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(float $total): void
    {
        $this->total = $total;
    }

    public function getTotalFormatted(): string
    {
        return $this->totalFormatted;
    }

    public function setTotalFormatted(string $totalFormatted): void
    {
        $this->totalFormatted = $totalFormatted;
    }

    public function getComparison(): ?float
    {
        return $this->comparison;
    }

    public function setComparison(?float $comparison): void
    {
        $this->comparison = $comparison;
    }

    public function getComparisonFormatted(): ?string
    {
        return $this->comparisonFormatted;
    }

    public function setComparisonFormatted(?string $comparisonFormatted): void
    {
        $this->comparisonFormatted = $comparisonFormatted;
    }

    public function getWidgetAccuracy(): WidgetAccuracy
    {
        return $this->widgetAccuracy;
    }

    public function setWidgetAccuracy(WidgetAccuracy $widgetAccuracy): void
    {
        $this->widgetAccuracy = $widgetAccuracy;
    }
}
