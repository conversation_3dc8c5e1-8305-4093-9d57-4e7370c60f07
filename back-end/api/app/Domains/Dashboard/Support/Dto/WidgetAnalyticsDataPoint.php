<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

use Carbon\CarbonInterface;

class WidgetAnalyticsDataPoint
{
    private ?CarbonInterface $date = null;

    private float $value;

    private ?string $valueFormatted = null;

    public function getDate(): ?CarbonInterface
    {
        return $this->date;
    }

    public function setDate(?CarbonInterface $date): void
    {
        $this->date = $date;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): void
    {
        $this->value = $value;
    }

    public function getValueFormatted(): ?string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(?string $valueFormatted): void
    {
        $this->valueFormatted = $valueFormatted;
    }
}
