<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

class WidgetPieAnalyticsDataPoint
{
    private float $value;

    private string $valueFormatted;

    private ?float $comparison = null;

    private ?string $comparisonFormatted = null;

    private string $name;

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): void
    {
        $this->value = $value;
    }

    public function getValueFormatted(): string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(string $valueFormatted): void
    {
        $this->valueFormatted = $valueFormatted;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getComparison(): ?float
    {
        return $this->comparison;
    }

    public function setComparison(?float $comparison): void
    {
        $this->comparison = $comparison;
    }

    public function getComparisonFormatted(): ?string
    {
        return $this->comparisonFormatted;
    }

    public function setComparisonFormatted(?string $comparisonFormatted): void
    {
        $this->comparisonFormatted = $comparisonFormatted;
    }
}
