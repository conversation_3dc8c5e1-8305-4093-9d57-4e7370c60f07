<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use Carbon\CarbonInterface;

class WidgetAnalyticsFilter
{
    private ?CarbonInterface $startDate = null;

    private ?CarbonInterface $endDate = null;

    private ?WidgetAccuracy $accuracy = null;

    private ?array $channels = null;

    private ?array $regions = null;

    private ?array $businessUnits = null;

    public function getStartDate(): ?CarbonInterface
    {
        return $this->startDate?->startOfDay();
    }

    public function setStartDate(?CarbonInterface $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getComparableStartDate(): ?CarbonInterface
    {
        if (! $this->getDaysInFilteredDateRange()) {
            return null;
        }

        if ($this->isMonthlyPeriod()) {
            return $this->getStartDate()
                ->copy()
                ->subMonthNoOverflow()
                ->startOfMonth();
        }

        if ($this->isYearlyPeriod()) {
            return $this->getStartDate()
                ->copy()
                ->subYearNoOverflow()
                ->startOfYear();
        }

        return $this->getStartDate()
            ->copy()
            ->startOfDay()
            ->subDays($this->getDaysInFilteredDateRange());
    }

    public function getEndDate(): ?CarbonInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?CarbonInterface $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getAccuracy(): WidgetAccuracy
    {
        if ($this->accuracy) {
            return $this->accuracy;
        }

        if (! $this->getDaysInFilteredDateRange()) {
            return WidgetAccuracy::MONTH;
        }

        if ($this->getDaysInFilteredDateRange() > 31) {
            return WidgetAccuracy::MONTH;
        }

        return WidgetAccuracy::DAY;
    }

    public function setAccuracy(?WidgetAccuracy $widgetAccuracy): void
    {
        $this->accuracy = $widgetAccuracy;
    }

    public function getChannels(): ?array
    {
        return $this->channels;
    }

    public function setChannels(?array $channels): void
    {
        $this->channels = $channels;
    }

    public function getRegions(): ?array
    {
        return $this->regions;
    }

    public function setRegions(?array $regions): void
    {
        $this->regions = $regions;
    }

    public function getBusinessUnits(): ?array
    {
        return $this->businessUnits;
    }

    public function setBusinessUnits(?array $businessUnits): void
    {
        $this->businessUnits = $businessUnits;
    }

    public function getComparableEndDate(): ?CarbonInterface
    {
        if (! $this->getDaysInFilteredDateRange()) {
            return null;
        }

        if ($this->isMonthlyPeriod()) {
            return $this->getEndDate()
                ->copy()
                ->subMonthNoOverflow()
                ->endOfMonth();
        }

        if ($this->isYearlyPeriod()) {
            return $this->getEndDate()
                ->copy()
                ->subYearNoOverflow()
                ->endOfYear();
        }

        return $this->getEndDate()
            ->copy()
            ->startOfDay()
            ->subDays($this->getDaysInFilteredDateRange())
            ->subSecond();
    }

    public function isMonthlyPeriod(): bool
    {
        if (! $this->getDaysInFilteredDateRange()) {
            return false;
        }

        if (! $this->getStartDate()->isSameMonth($this->getEndDate())) {
            return false;
        }

        $firstOfMonth = $this->getStartDate()->copy()->startOfMonth();
        $endOfMonth = $this->getEndDate()->copy()->endOfMonth();

        return $this->getStartDate()->isSameDay($firstOfMonth)
            && $this->getEndDate()->isSameDay($endOfMonth);
    }

    public function isYearlyPeriod(): bool
    {
        if (! $this->getDaysInFilteredDateRange()) {
            return false;
        }

        if (! $this->getStartDate()->isSameYear($this->getEndDate())) {
            return false;
        }

        $firstOfYear = $this->getStartDate()->copy()->startOfYear();
        $endOfYear = $this->getEndDate()->copy()->endOfYear();

        return $this->getStartDate()->isSameDay($firstOfYear)
            && $this->getEndDate()->isSameDay($endOfYear);
    }

    public function getDaysInFilteredDateRange(): ?int
    {
        if (! $this->getStartDate() || ! $this->getEndDate()) {
            return null;
        }

        return (int) $this->getStartDate()->diffInDays($this->getEndDate());
    }
}
