<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Support\Dto;

class PostAnalytics
{
    private ?string $url = null;

    private ?string $text = null;

    private ?string $channel = null;

    private array $entries;

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(?string $text): void
    {
        $this->text = $text;
    }

    /**
     * @return PostAnalyticsEntry[]
     */
    public function getEntries(): array
    {
        return $this->entries;
    }

    /**
     * @param  PostAnalyticsEntry[]  $entries
     */
    public function setEntries(array $entries): void
    {
        $this->entries = $entries;
    }

    public function getChannel(): ?string
    {
        return $this->channel;
    }

    public function setChannel(?string $channel): void
    {
        $this->channel = $channel;
    }
}
