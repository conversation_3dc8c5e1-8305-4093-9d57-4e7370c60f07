<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class DataSource extends Model
{
    use HasFactory;

    public const TABLE_NAME = 'data_sources';

    protected $table = self::TABLE_NAME;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'options' => 'array',
        ];
    }

    public function dashboards(): BelongsToMany
    {
        return $this->belongsToMany(Dashboard::class, 'dashboard_data_source');
    }

    public function sourceable(): MorphTo
    {
        return $this->morphTo();
    }
}
