<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DashboardView extends Model
{
    use HasFactory;

    public const TABLE_NAME = 'dashboard_views';

    protected $table = self::TABLE_NAME;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'channels' => 'array',
            'regions' => 'array',
            'business_units' => 'array',
        ];
    }

    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class);
    }
}
