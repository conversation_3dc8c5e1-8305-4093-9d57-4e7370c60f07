<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Section extends Model
{
    use HasFactory;

    public const TABLE_NAME = 'sections';

    protected $table = self::TABLE_NAME;

    protected $guarded = [];

    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class, 'dashboard_id');
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class);
    }
}
