<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Models;

use App\Domains\Dashboard\Support\Enums\Widget\WidgetDataType;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Widget extends Model
{
    use HasFactory;

    public const TABLE_NAME = 'widgets';

    protected $table = self::TABLE_NAME;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'data_type' => WidgetDataType::class,
            'type' => WidgetType::class,
        ];
    }

    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class, 'section_id');
    }
}
