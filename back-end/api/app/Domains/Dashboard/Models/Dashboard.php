<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Models;

use App\Domains\Authentication\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Dashboard extends Model
{
    use HasFactory;

    public const TABLE_NAME = 'dashboards';

    protected $table = self::TABLE_NAME;

    protected $guarded = [];

    protected static function booted(): void
    {
        static::addGlobalScope('user_dashboards', function (Builder $builder) {
            /** @var User|null $user */
            $user = auth()->user();
            if (! $user) {
                return;
            }

            $builder->whereIn('id', $user->dashboards ?? []);
        });

        static::addGlobalScope('default_sort', function (Builder $builder) {
            $builder
                ->orderBy('sort')
                ->orderBy('title');
        });
    }

    public function dataSources(): BelongsToMany
    {
        return $this->belongsToMany(DataSource::class, 'dashboard_data_source');
    }

    public function sections(): HasMany
    {
        return $this->hasMany(Section::class);
    }

    public function dashboardViews(): HasMany
    {
        return $this->hasMany(DashboardView::class);
    }
}
