<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Jobs;

use App\Domains\Sources\TikTok\Actions\Video\SyncVideoInsights;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncTikTokVideos implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $tikTokAccountId, public ?int $cursor = null) {}

    public function handle(): void
    {
        $tikTokAccount = TikTokAccount::query()
            ->findOrFail($this->tikTokAccountId);

        $cursor = app(SyncVideoInsights::class)->execute(
            $tikTokAccount,
            $this->cursor
        );

        if ($cursor) {
            $job = new SyncTikTokVideos($this->tikTokAccountId, $cursor);
            dispatch($job);
        }
    }
}
