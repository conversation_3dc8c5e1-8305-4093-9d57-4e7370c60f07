<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Jobs;

use App\Domains\Sources\TikTok\Actions\User\SyncUserInfo;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncTikTokAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $tikTokAccountId) {}

    public function handle(): void
    {
        $tikTokAccount = TikTokAccount::query()
            ->findOrFail($this->tikTokAccountId);

        app(SyncUserInfo::class)->execute($tikTokAccount);

        $job = new SyncTikTokVideos($this->tikTokAccountId);
        dispatch($job);
    }
}
