<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Jobs;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncTikTokAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (TikTokAccount $tikTokAccount) {
            $job = new SyncTikTokAccount($tikTokAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return TikTokAccount::query()
            ->chunk(100, function (Collection $tikTokAccounts) use ($closure) {
                foreach ($tikTokAccounts as $tikTokAccount) {
                    $closure($tikTokAccount);
                }
            });
    }
}
