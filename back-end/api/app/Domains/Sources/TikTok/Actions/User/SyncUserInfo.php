<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Actions\User;

use App\Domains\Sources\TikTok\Actions\Authentication\RefreshToken;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Domains\Sources\TikTok\Models\TikTokAccountInsights;
use App\Domains\Sources\TikTok\Support\Exceptions\User\CannotSyncUserInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SyncUserInfo
{
    public function execute(TikTokAccount $tikTokAccount): void
    {
        $report = $this->getResponse($tikTokAccount);
        $currentTotal = $this->getCurrentTotal($tikTokAccount);

        TikTokAccountInsights::query()
            ->updateOrCreate([
                'tik_tok_account_id' => $tikTokAccount->id,
                'date' => now()->format('Y-m-d'),
            ], [
                'video_count' => $report['video_count'] - ($currentTotal['video_count'] ?? 0),
                'follower_count' => $report['follower_count'] - ($currentTotal['follower_count'] ?? 0),
                'likes_count' => $report['likes_count'] - ($currentTotal['likes_count'] ?? 0),
            ]);
    }

    protected function getCurrentTotal(TikTokAccount $tikTokAccount): ?array
    {
        $totals = TikTokAccountInsights::query()
            ->where('tik_tok_account_id', $tikTokAccount->id)
            ->where('date', '<', now()->format('Y-m-d'))
            ->addSelect(DB::raw('SUM(video_count) as video_count'))
            ->addSelect(DB::raw('SUM(follower_count) as follower_count'))
            ->addSelect(DB::raw('SUM(likes_count) as likes_count'))
            ->groupBy(['tik_tok_account_id'])
            ->first();

        if (! $totals) {
            return null;
        }

        return [
            'video_count' => (int) ($totals['video_count'] ?? 0),
            'follower_count' => (int) ($totals['follower_count'] ?? 0),
            'likes_count' => (int) ($totals['likes_count'] ?? 0),
        ];
    }

    protected function getResponse(TikTokAccount $tikTokAccount): array
    {
        $token = app(RefreshToken::class)->execute($tikTokAccount);

        if (! $token) {
            throw CannotSyncUserInfo::becauseOfMissingToken($tikTokAccount);
        }

        $response = Http::withToken($token)
            ->get('https://open.tiktokapis.com/v2/user/info/', [
                'fields' => 'open_id,is_verified,username,follower_count,following_count,likes_count,video_count',
            ]);

        if (! $response->successful()) {
            throw CannotSyncUserInfo::becauseOfHttpErrorWithStatusCode(
                tikTokAccount: $tikTokAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response->json('data.user');
    }
}
