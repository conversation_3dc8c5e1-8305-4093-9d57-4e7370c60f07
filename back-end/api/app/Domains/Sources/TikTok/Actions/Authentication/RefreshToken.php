<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Actions\Authentication;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class RefreshToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(TikTokAccount $tikTokAccount): ?string
    {
        if (! $this->shouldRefreshToken($tikTokAccount)) {
            return $tikTokAccount->token;
        }

        $token = $this->refreshToken($tikTokAccount);

        $tikTokAccount->token = $token['access_token'] ?? null;
        $tikTokAccount->refresh_token = $token['refresh_token'] ?? null;
        $tikTokAccount->token_created_at = $token['access_token'] ? now() : null;

        $tikTokAccount->save();

        return $token['access_token'] ?? null;
    }

    protected function shouldRefreshToken(TikTokAccount $tikTokAccount): bool
    {
        if (! $tikTokAccount->token_created_at || ! $tikTokAccount->token) {
            return true;
        }

        $expiryThreshold = now()->subHours(55);

        return $tikTokAccount->token_created_at->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function refreshToken(TikTokAccount $tikTokAccount): ?array
    {
        $response = Http::asForm()
            ->withHeaders(['Cache-Control' => 'no-cache'])
            ->connectTimeout(10)
            ->post(
                url: 'https://open.tiktokapis.com/v2/oauth/token/',
                data: [
                    'client_key' => config('services.tiktok.client_id'),
                    'client_secret' => config('services.tiktok.client_secret'),
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $tikTokAccount->refresh_token,
                ]
            );

        if (! $response->successful()) {
            return null;
        }

        return $response->json();
    }
}
