<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Actions\Authentication;

use App\Domains\Slack\Messages\NewTikTokOrganicAccountConnectedMessage;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Domains\Support\SlackNotification;
use App\Support\Scopes\SyncDeactivatedScope;
use SocialiteProviders\Manager\OAuth2\User;

class UpdateTikTokAccountBySocialiteUser
{
    public function execute(User $user, ?array $scopes = null): TikTokAccount
    {
        $tikTokAccount = TikTokAccount::withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('external_id', $user->getId())
            ->first();

        if (! $tikTokAccount) {
            $tikTokAccount = new TikTokAccount;
        }

        $tikTokAccount->external_id = $user->getId();
        $tikTokAccount->name = $user->getName();
        $tikTokAccount->image_url = $user->getAvatar();
        $tikTokAccount->refresh_token = $user->refreshToken;
        $tikTokAccount->token = $user->token;
        $tikTokAccount->token_created_at = now();
        $tikTokAccount->scopes = $scopes;

        $tikTokAccount->save();

        if ($tikTokAccount->wasRecentlyCreated) {
            SlackNotification::send(new NewTikTokOrganicAccountConnectedMessage($tikTokAccount));
        }

        return $tikTokAccount;
    }
}
