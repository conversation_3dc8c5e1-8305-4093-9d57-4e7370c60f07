<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Actions\Authentication;

use Lara<PERSON>\Socialite\Facades\Socialite;
use SocialiteProviders\TikTok\Provider as TikTokProvider;

class GenerateOAuthRedirectLink
{
    private string $redirectUrl;

    public function execute(string $redirectUrl): string
    {
        $this->redirectUrl = $redirectUrl;

        return $this
            ->getTikTokProvider()
            ->redirect()
            ->getTargetUrl();
    }

    protected function getTikTokProvider(): TikTokProvider
    {
        /** @var TikTokProvider $provider */
        $provider = Socialite::driver('tiktok');

        $provider->scopes([
            'user.info.profile',
            'user.info.stats',
            'video.list',
        ]);
        $provider->with([
            'disable_auto_auth' => 1,
            'response_type' => 'code',
            'state' => [],
        ]);
        $provider->redirectUrl($this->redirectUrl);
        $provider->stateless();

        return $provider;
    }
}
