<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Actions\Video;

use App\Domains\Sources\TikTok\Actions\Authentication\RefreshToken;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Domains\Sources\TikTok\Models\TikTokVideo;
use App\Domains\Sources\TikTok\Models\TikTokVideoInsight;
use App\Domains\Sources\TikTok\Support\Exceptions\Video\CannotSyncVideoInsights;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Log;

class SyncVideoInsights
{
    /**
     * @throws CannotSyncVideoInsights
     * @throws ConnectionException
     */
    public function execute(TikTokAccount $tikTokAccount, ?int $cursor = null): ?int
    {
        $response = $this->getResponse($tikTokAccount, $cursor);
        $videos = $response->json('data.videos', []);

        $this->processVideos(
            tikTokAccount: $tikTokAccount,
            videos: $videos
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?int
    {
        $hasMore = $response->json('data.has_more');
        $nextPageCursor = $response->json('data.cursor');

        return $hasMore ? $nextPageCursor : null;
    }

    protected function processVideos(TikTokAccount $tikTokAccount, array $videos): void
    {
        foreach ($videos as $video) {
            $this->processVideo($tikTokAccount, $video);
        }
    }

    protected function processVideo(TikTokAccount $tikTokAccount, array $video): void
    {
        $tikTokVideo = TikTokVideo::query()
            ->where('external_id', $video['id'])
            ->first();

        if (! $tikTokVideo) {
            $tikTokVideo = new TikTokVideo;

            $tikTokVideo->external_id = $video['id'];
            $tikTokVideo->tik_tok_account_id = $tikTokAccount->id;
            $tikTokVideo->image_url = $this->storeExternalFile($video['id'], $video['cover_image_url']);
        }

        $tikTokVideo->date = $video['create_time'];
        $tikTokVideo->title = $video['title'];
        $tikTokVideo->description = $video['video_description'];
        $tikTokVideo->duration = $video['duration'];

        $tikTokVideo->save();

        $this->processVideoInsight($tikTokVideo, $video);
    }

    protected function processVideoInsight(TikTokVideo $tikTokVideo, array $video): void
    {
        $currentTotal = $this->getCurrentVideoInsightsTotal($tikTokVideo);

        TikTokVideoInsight::query()
            ->updateOrCreate([
                'tik_tok_video_id' => $tikTokVideo->id,
                'date' => now()->format('Y-m-d'),
            ], [
                'view_count' => $video['view_count'] - ($currentTotal['view_count'] ?? 0),
                'comment_count' => $video['comment_count'] - ($currentTotal['comment_count'] ?? 0),
                'share_count' => $video['share_count'] - ($currentTotal['share_count'] ?? 0),
                'like_count' => $video['like_count'] - ($currentTotal['like_count'] ?? 0),
            ]);
    }

    protected function getCurrentVideoInsightsTotal(TikTokVideo $tikTokVideo): ?array
    {
        $totals = TikTokVideoInsight::query()
            ->where('tik_tok_video_id', $tikTokVideo->id)
            ->where('date', '<', now()->format('Y-m-d'))
            ->addSelect(DB::raw('SUM(view_count) as view_count'))
            ->addSelect(DB::raw('SUM(comment_count) as comment_count'))
            ->addSelect(DB::raw('SUM(share_count) as share_count'))
            ->addSelect(DB::raw('SUM(like_count) as like_count'))
            ->groupBy(['tik_tok_video_id'])
            ->first();

        if (! $totals) {
            return null;
        }

        return [
            'view_count' => (int) ($totals['view_count'] ?? 0),
            'comment_count' => (int) ($totals['comment_count'] ?? 0),
            'share_count' => (int) ($totals['share_count'] ?? 0),
            'like_count' => (int) ($totals['like_count'] ?? 0),
        ];
    }

    protected function storeExternalFile(string $externalId, string $path): ?string
    {
        try {
            $extension = pathinfo(parse_url($path, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
            $filename = "tiktok/videos/{$externalId}/cover.{$extension}";

            $imageContent = Http::get($path)->body();

            Storage::put($filename, $imageContent);

            return $filename;
        } catch (Exception $e) {
            Log::error("Failed to download TikTok cover image: {$e->getMessage()}", [
                'video_id' => $externalId,
                'image_url' => $path,
            ]);

            return null;
        }
    }

    /**
     * @throws CannotSyncVideoInsights
     * @throws ConnectionException
     */
    protected function getResponse(TikTokAccount $tikTokAccount, ?int $cursor): Response
    {
        $token = app(RefreshToken::class)->execute($tikTokAccount);

        if (! $token) {
            throw CannotSyncVideoInsights::becauseOfMissingToken($tikTokAccount);
        }

        $data = [
            'max_count' => 20,
        ];

        if ($cursor) {
            $data['cursor'] = $cursor;
        }

        $response = Http::withQueryParameters([
            'fields' => 'id,create_time,cover_image_url,video_description,duration,title,like_count,comment_count,share_count,view_count',
        ])
            ->withToken($token)
            ->post(
                url: 'https://open.tiktokapis.com/v2/video/list/',
                data: $data
            );

        if (! $response->successful()) {
            throw CannotSyncVideoInsights::becauseOfHttpErrorWithStatusCode(
                tikTokAccount: $tikTokAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
