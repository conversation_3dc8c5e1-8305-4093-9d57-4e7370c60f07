<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Support\Exceptions\User;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Exception;

class CannotSyncUserInfo extends Exception
{
    public static function becauseOfMissingToken(TikTokAccount $tikTokAccount): self
    {
        return new self(
            "Cannot sync user info for TikTok account {$tikTokAccount->id} because of missing token"
        );
    }

    public static function becauseOfHttpErrorWithStatusCode(
        TikTokAccount $tikTokAccount,
        int $statusCode,
        string $body
    ): self {
        return new self(
            "Cannot sync user info for TikTok account {$tikTokAccount->id}. ".
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
