<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class TikTokAccount extends Model
{
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token_created_at' => 'datetime',
            'scopes' => 'array',
        ];
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }
}
