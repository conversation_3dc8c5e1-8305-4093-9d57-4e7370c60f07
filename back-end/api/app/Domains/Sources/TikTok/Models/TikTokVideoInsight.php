<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class TikTokVideoInsight extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function tikTokVideos(): BelongsTo
    {
        return $this->belongsTo(TikTokVideo::class, 'tik_tok_video_id');
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: TikTokVideo::class,
                firstKey: 'id',                         // Foreign key on TikTokVideo table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'tik_tok_video_id',
                secondLocalKey: 'tik_tok_account_id'    // Local key on TikTokVideo table
            )
            ->where('sourceable_type', TikTokAccount::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
