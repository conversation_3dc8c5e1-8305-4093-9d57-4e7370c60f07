<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTok\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TikTokVideo extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function tikTokAccount(): BelongsTo
    {
        return $this->belongsTo(TikTokAccount::class, 'tik_tok_account_id');
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'tik_tok_account_id', 'sourceable_id')
            ->where('sourceable_type', TikTokAccount::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
