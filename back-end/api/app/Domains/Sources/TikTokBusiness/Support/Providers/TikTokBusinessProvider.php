<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTokBusiness\Support\Providers;

use GuzzleHttp\RequestOptions;
use Illuminate\Support\Arr;
use Laravel\Socialite\Two\InvalidStateException;
use SocialiteProviders\Manager\OAuth2\AbstractProvider;
use SocialiteProviders\Manager\OAuth2\User;

class TikTokBusinessProvider extends AbstractProvider
{
    /**
     * {@inheritdoc}
     */
    protected $scopes = [];

    /**
     * @var User
     */
    protected $user;

    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        $fields = [
            'app_id' => $this->clientId,
            'state' => $state,
            'response_type' => 'code',
            'scope' => $this->formatScopes($this->getScopes(), $this->scopeSeparator),
            'redirect_uri' => $this->redirectUrl,
        ];

        $fields = array_merge($fields, $this->parameters);

        return 'https://business-api.tiktok.com/portal/auth?'.http_build_query($fields);
    }

    /**
     * {@inheritdoc}
     */
    public function user()
    {
        if ($this->user) {
            return $this->user;
        }

        if ($this->hasInvalidState()) {
            throw new InvalidStateException;
        }

        $response = $this->getAccessTokenResponse($this->getCode());

        $token = Arr::get($response, 'data.access_token');

        $this->user = $this->mapUserToObject($this->getUserByToken($token));

        return $this->user
            ->setToken($token)
            ->setExpiresIn(Arr::get($response, 'expires_in'))
            ->setRefreshToken(Arr::get($response, 'refresh_token'))
            ->setApprovedScopes(explode($this->scopeSeparator, Arr::get($response, 'scope', '')));
    }

    /**
     * {@inheritdoc}
     */
    public function getTokenUrl()
    {
        return 'https://business-api.tiktok.com/open_api/v1.3/oauth2/access_token/';
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return [
            'auth_code' => $code,
            'app_id' => $this->clientId,
            'secret' => $this->clientSecret,
        ];
    }

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get('https://business-api.tiktok.com/open_api/v1.3/user/info/', [
            RequestOptions::HEADERS => [
                'Access-Token' => $token,
            ],
        ]);

        return json_decode((string) $response->getBody(), true);
    }

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject($user)
    {
        $user = $user['data'];

        return (new User)
            ->setRaw($user)
            ->map([
                'id' => $user['core_user_id'],
                'nickname' => $user['username'] ?? null,
                'union_id' => $user['union_id'] ?? null,
                'name' => $user['display_name'],
                'avatar' => $user['avatar_url'],
            ]);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenHeaders($code)
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    public function getAccessTokenResponse($code)
    {
        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            RequestOptions::HEADERS => $this->getTokenHeaders($code),
            RequestOptions::JSON => $this->getTokenFields($code),
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }
}
