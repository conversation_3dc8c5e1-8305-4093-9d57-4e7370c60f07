<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTokBusiness\Actions\Authentication;

use App\Domains\Slack\Messages\NewTikTokBusinessAccountConnectedMessage;
use App\Domains\Sources\TikTokBusiness\Models\TikTokBusinessAccount;
use App\Domains\Support\SlackNotification;
use App\Support\Scopes\SyncDeactivatedScope;
use SocialiteProviders\Manager\OAuth2\User;

class UpdateTikTokBusinessAccountBySocialiteUser
{
    public function execute(User $user): TikTokBusinessAccount
    {
        $tikTokAccount = TikTokBusinessAccount::withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('external_id', $user->getId())
            ->first();

        if (! $tikTokAccount) {
            $tikTokAccount = new TikTokBusinessAccount;
        }

        $tikTokAccount->external_id = $user->getId();
        $tikTokAccount->name = $user->getRaw()['display_name'] ?? null;
        $tikTokAccount->email = $user->getRaw()['email'] ?? null;
        $tikTokAccount->image_url = null;
        $tikTokAccount->token = $user->token;
        $tikTokAccount->token_created_at = now();

        $tikTokAccount->save();

        if (! $tikTokAccount->wasRecentlyCreated) {
            SlackNotification::send(new NewTikTokBusinessAccountConnectedMessage($googleAccount));
        }

        return $tikTokAccount;
    }
}
