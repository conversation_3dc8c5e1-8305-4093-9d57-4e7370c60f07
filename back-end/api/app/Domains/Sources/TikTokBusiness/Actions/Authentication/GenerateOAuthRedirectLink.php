<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTokBusiness\Actions\Authentication;

use App\Domains\Sources\TikTokBusiness\Support\Providers\TikTokBusinessProvider;
use Laravel\Socialite\Facades\Socialite;

class GenerateOAuthRedirectLink
{
    private string $redirectUrl;

    public function execute(string $redirectUrl): string
    {
        $this->redirectUrl = $redirectUrl;

        return $this
            ->getTikTokProvider()
            ->redirect()
            ->getTargetUrl();
    }

    protected function getTikTokProvider(): TikTokBusinessProvider
    {
        /** @var TikTokBusinessProvider $provider */
        $provider = Socialite::driver('tiktok-business');

        $provider->scopes([]);
        $provider->with([
            'response_type' => 'code',
            'state' => [],
        ]);
        $provider->redirectUrl($this->redirectUrl);
        $provider->stateless();

        return $provider;
    }
}
