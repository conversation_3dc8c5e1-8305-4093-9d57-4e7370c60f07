<?php

declare(strict_types=1);

namespace App\Domains\Sources\TikTokBusiness\Models;

use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TikTokBusinessAccount extends Model
{
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token_created_at' => 'datetime',
        ];
    }
}
