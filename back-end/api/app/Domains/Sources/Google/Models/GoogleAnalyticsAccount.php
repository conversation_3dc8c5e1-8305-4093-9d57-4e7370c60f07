<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleAnalyticsAccount extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'external_created_at' => 'datetime',
            'external_updated_at' => 'datetime',
            'last_synced_at' => 'datetime',
        ];
    }

    public function googleAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAccount::class, 'google_account_id');
    }

    public function googleAnalyticsProperties(): HasMany
    {
        return $this->hasMany(GoogleAnalyticsProperty::class);
    }
}
