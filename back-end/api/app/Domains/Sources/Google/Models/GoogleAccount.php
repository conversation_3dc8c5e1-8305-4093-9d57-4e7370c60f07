<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class GoogleAccount extends BaseGoogleAccount
{
    public function googleAnalyticsAccounts(): HasMany
    {
        return $this->hasMany(GoogleAnalyticsAccount::class);
    }

    public function googleAdAccounts(): HasMany
    {
        return $this->hasMany(GoogleAdAccount::class);
    }
}
