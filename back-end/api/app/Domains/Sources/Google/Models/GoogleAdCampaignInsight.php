<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleAdCampaignInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function googleAdCampaign(): BelongsTo
    {
        return $this->belongsTo(GoogleAdCampaign::class);
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: GoogleAdCampaign::class,
                firstKey: 'id',                         // Foreign key on GoogleAdCampaign table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'google_ad_campaign_id',
                secondLocalKey: 'google_ad_account_id'  // Local key on GoogleAdCampaign table
            )
            ->where('sourceable_type', GoogleAdAccount::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
