<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Sources\Google\Support\Enums\Ads\AdvertisingChannelType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleAdCampaign extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'advertising_channel_type' => AdvertisingChannelType::class,
        ];
    }

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class);
    }

    public function googleAdCampaignInsights(): HasMany
    {
        return $this->hasMany(GoogleAdCampaignInsight::class);
    }
}
