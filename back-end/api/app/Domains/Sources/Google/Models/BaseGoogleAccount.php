<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

abstract class BaseGoogleAccount extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token_created_at' => 'datetime',
            'deactivated_at' => 'datetime',
            'scopes' => 'array',
        ];
    }
}
