<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleAdAccount extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_hidden' => 'boolean',
            'is_test_account' => 'boolean',
            'is_manager' => 'boolean',
            'last_synced_at' => 'datetime',
        ];
    }

    public function googleAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAccount::class);
    }

    public function googleAdCampaigns(): HasMany
    {
        return $this->hasMany(GoogleAdCampaign::class);
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }
}
