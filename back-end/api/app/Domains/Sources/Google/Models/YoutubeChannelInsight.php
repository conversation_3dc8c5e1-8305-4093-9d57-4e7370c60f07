<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class YoutubeChannelInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function youtubeChannel(): BelongsTo
    {
        return $this->belongsTo(YoutubeChannel::class);
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'youtube_channel_id', 'sourceable_id')
            ->where('sourceable_type', YoutubeChannel::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
