<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class YoutubeVideoInsight extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function youtubeVideo(): BelongsTo
    {
        return $this->belongsTo(YoutubeVideo::class);
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: YoutubeVideo::class,
                firstKey: 'id',                         // Foreign key on YoutubeVideo table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'youtube_video_id',
                secondLocalKey: 'youtube_channel_id'  // Local key on YoutubeVideo table
            )
            ->where('sourceable_type', YoutubeChannel::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
