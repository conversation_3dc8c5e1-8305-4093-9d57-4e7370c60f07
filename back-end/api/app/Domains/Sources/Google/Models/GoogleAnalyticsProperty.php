<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Support\Enums\Analytics\IndustryCategory;
use App\Domains\Sources\Google\Support\Enums\Analytics\PropertyType;
use App\Domains\Sources\Google\Support\Enums\Analytics\ServiceLevel;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleAnalyticsProperty extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'industry_category' => IndustryCategory::class,
            'service_level' => ServiceLevel::class,
            'property_type' => PropertyType::class,
            'external_created_at' => 'datetime',
            'external_updated_at' => 'datetime',
            'last_synced_at' => 'datetime',
        ];
    }

    public function googleAnalyticsAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAnalyticsAccount::class, 'google_analytics_account_id');
    }

    public function googleAnalyticsReports(): HasMany
    {
        return $this->hasMany(GoogleAnalyticsReport::class);
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
