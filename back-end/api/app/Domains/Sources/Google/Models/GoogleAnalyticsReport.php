<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GoogleAnalyticsReport extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function googleAnalyticsProperty(): BelongsTo
    {
        return $this->belongsTo(GoogleAnalyticsProperty::class, 'google_analytics_property_id');
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'google_analytics_property_id', 'sourceable_id')
            ->where('sourceable_type', GoogleAnalyticsProperty::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
