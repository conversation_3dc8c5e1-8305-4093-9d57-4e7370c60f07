<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Exceptions\Ads;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Exception;

class CannotGetCustomerClientsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(GoogleAccount $googleAccount, int $customerId, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get customer clients. Received HTTP error with status code %s for Google Account #%s, customer id %s. Response body:\n\n%s',
            $statusCode,
            $googleAccount->id,
            $customerId,
            $body
        ));
    }

    public static function becauseOfMissingToken(GoogleAccount $googleAccount, int $customerId): self
    {
        return new self(sprintf(
            'Cannot get customer clients. Could not retrieve token for Google Account #%s, customer id %s.',
            $googleAccount->id,
            $customerId
        ));
    }
}
