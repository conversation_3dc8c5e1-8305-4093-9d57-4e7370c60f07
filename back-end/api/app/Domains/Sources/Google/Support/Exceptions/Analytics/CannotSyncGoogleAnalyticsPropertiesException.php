<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Exceptions\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Exception;

class CannotSyncGoogleAnalyticsPropertiesException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(GoogleAnalyticsAccount $googleAnalyticsAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot sync Google Analytics properties. Received HTTP error with status code %s for Google Analytics Account #%s. Response body:\n\n%s',
            $statusCode,
            $googleAnalyticsAccount->id,
            $body
        ));
    }

    public static function becauseOfMissingToken(GoogleAnalyticsAccount $googleAnalyticsAccount): self
    {
        return new self(sprintf(
            'Cannot sync Google Analytics properties. Could not retrieve token for Google Analytics Account #%s.',
            $googleAnalyticsAccount->id
        ));
    }
}
