<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Exceptions\Ads;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Exception;

class CannotGetCampaignsWithMetricsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(GoogleAdAccount $googleAdAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get campaign metrics. Received HTTP error with status code %s for Google Ad Account #%s. Response body:\n\n%s',
            $statusCode,
            $googleAdAccount->id,
            $body
        ));
    }

    public static function becauseOfMissingToken(GoogleAdAccount $googleAdAccount): self
    {
        return new self(sprintf(
            'Cannot get campaign metrics. Could not retrieve token for Google Ad Account #%s.',
            $googleAdAccount->id
        ));
    }
}
