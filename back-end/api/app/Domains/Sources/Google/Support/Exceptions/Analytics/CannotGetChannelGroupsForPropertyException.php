<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Exceptions\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Exception;

class CannotGetChannelGroupsForPropertyException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(GoogleAnalyticsProperty $googleAnalyticsProperty, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $googleAnalyticsProperty->id,
            $statusCode,
            $body
        ));
    }

    public static function becauseOfMissingToken(GoogleAnalyticsProperty $googleAnalyticsProperty): self
    {
        return new self(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Could not retrieve token.',
            $googleAnalyticsProperty->id
        ));
    }

    public static function becauseOfDateHourConversionError(
        GoogleAnalyticsProperty $googleAnalyticsProperty,
        ?string $dateHour,
        ?string $timeZone,
        ?string $originalMessage,
    ): self {
        return new self(sprintf(
            'Cannot get channel groups for Google Analytics property #%s. Error in converting date hour (%s) and time zone (%s) into UTC. Original message:\n\n%s',
            $googleAnalyticsProperty->id,
            $dateHour,
            $timeZone,
            $originalMessage
        ));
    }
}
