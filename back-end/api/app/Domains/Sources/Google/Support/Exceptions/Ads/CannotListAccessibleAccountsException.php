<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Exceptions\Ads;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Exception;

class CannotListAccessibleAccountsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(GoogleAccount $googleAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot list accessible ads accounts. Received HTTP error with status code %s for Google Account #%s. Response body:\n\n%s',
            $statusCode,
            $googleAccount->id,
            $body
        ));
    }

    public static function becauseOfMissingToken(GoogleAccount $googleAccount): self
    {
        return new self(sprintf(
            'Cannot list accessible ads accounts. Could not retrieve token for Google Account #%s.',
            $googleAccount->id
        ));
    }
}
