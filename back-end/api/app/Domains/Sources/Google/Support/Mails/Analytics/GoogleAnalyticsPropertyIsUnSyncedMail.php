<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Mails\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class GoogleAnalyticsPropertyIsUnSyncedMail extends Mailable
{
    public function __construct(public int $googleAnalyticsPropertyId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Google Analytics Property #%s is not being synchronised', $this->googleAnalyticsPropertyId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.google.analytics.google-analytics-property-is-un-synced-mail',
            with: [
                'googleAnalyticsProperty' => GoogleAnalyticsProperty::query()->findOrFail($this->googleAnalyticsPropertyId),
            ],
        );
    }
}
