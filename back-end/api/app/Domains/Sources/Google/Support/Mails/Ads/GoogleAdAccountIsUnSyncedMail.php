<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Mails\Ads;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class GoogleAdAccountIsUnSyncedMail extends Mailable
{
    public function __construct(public int $googleAdAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Google Ad Account #%s is not being synchronised', $this->googleAdAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.google.ads.google-ad-account-is-un-synced-mail',
            with: [
                'googleAdAccount' => GoogleAdAccount::query()->findOrFail($this->googleAdAccountId),
            ],
        );
    }
}
