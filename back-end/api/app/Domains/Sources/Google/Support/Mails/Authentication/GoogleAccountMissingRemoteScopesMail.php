<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Mails\Authentication;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class GoogleAccountMissingRemoteScopesMail extends Mailable
{
    public function __construct(protected int $googleAccountId, protected array $missingRemoteScopes = []) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Google Account #%s deactivated because of missing remote scopes', $this->googleAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.google.authentication.google-account-missing-remote-scopes',
            with: [
                'googleAccount' => GoogleAccount::query()->findOrFail($this->googleAccountId),
                'missingRemoteScopes' => $this->missingRemoteScopes,
            ],
        );
    }
}
