<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Support\Mails\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class GoogleAnalyticsAccountIsUnSyncedMail extends Mailable
{
    public function __construct(public int $googleAnalyticsAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Google Analytics Account #%s is not being synchronised', $this->googleAnalyticsAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.google.analytics.google-analytics-account-is-un-synced-mail',
            with: [
                'googleAnalyticsAccount' => GoogleAnalyticsAccount::query()->findOrFail($this->googleAnalyticsAccountId),
            ],
        );
    }
}
