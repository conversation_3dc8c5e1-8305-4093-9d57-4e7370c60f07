<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Enums\Analytics\IndustryCategory;
use App\Domains\Sources\Google\Support\Enums\Analytics\PropertyType;
use App\Domains\Sources\Google\Support\Enums\Analytics\ServiceLevel;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsPropertiesException;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class SyncProperties
{
    /**
     * @throws CannotSyncGoogleAnalyticsPropertiesException
     * @throws ConnectionException
     */
    public function execute(GoogleAnalyticsAccount $googleAnalyticsAccount, ?string $nextPageToken = null): void
    {
        $response = $this->getResponse($googleAnalyticsAccount, $nextPageToken);
        $nextPageToken = $response->json('nextPageToken');
        $properties = $response->json('properties', []);

        $this->processProperties(
            googleAnalyticsAccount: $googleAnalyticsAccount,
            properties: $properties
        );

        if ($nextPageToken) {
            app(self::class)->execute(
                googleAnalyticsAccount: $googleAnalyticsAccount,
                nextPageToken: $nextPageToken
            );
        }
    }

    protected function processProperties(GoogleAnalyticsAccount $googleAnalyticsAccount, array $properties): void
    {
        foreach ($properties as $account) {
            $this->processProperty(
                googleAnalyticsAccount: $googleAnalyticsAccount,
                property: $account
            );
        }
    }

    protected function processProperty(GoogleAnalyticsAccount $googleAnalyticsAccount, array $property): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::withoutGlobalScope(SyncDeactivatedScope::class)->where('external_id', $property['name'])->first();

        if (! $googleAnalyticsProperty) {
            $googleAnalyticsProperty = new GoogleAnalyticsProperty;
        }

        $googleAnalyticsProperty->google_analytics_account_id = $googleAnalyticsAccount->id;
        $googleAnalyticsProperty->external_id = $property['name'];
        $googleAnalyticsProperty->name = $property['displayName'];
        $googleAnalyticsProperty->industry_category = IndustryCategory::tryFrom($property['industryCategory'] ?? '');
        $googleAnalyticsProperty->time_zone = $property['timeZone'];
        $googleAnalyticsProperty->currency_code = $property['currencyCode'];
        $googleAnalyticsProperty->service_level = ServiceLevel::from($property['serviceLevel']);
        $googleAnalyticsProperty->property_type = PropertyType::from($property['propertyType']);
        $googleAnalyticsProperty->external_created_at = $property['createTime'];
        $googleAnalyticsProperty->external_updated_at = $property['updateTime'];
        $googleAnalyticsProperty->last_synced_at = now();

        $googleAnalyticsProperty->save();
    }

    /**
     * @throws CannotSyncGoogleAnalyticsPropertiesException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAnalyticsAccount $googleAnalyticsAccount, ?string $nextPageToken): Response
    {
        $token = app(RefreshToken::class)->execute($googleAnalyticsAccount->googleAccount);

        if (! $token) {
            throw CannotSyncGoogleAnalyticsPropertiesException::becauseOfMissingToken($googleAnalyticsAccount);
        }

        $query = [
            'pageSize' => 200,
            'showDeleted' => false,
            'filter' => sprintf('parent:%s', $googleAnalyticsAccount->external_id),
        ];

        if ($nextPageToken) {
            $query['pageToken'] = $nextPageToken;
        }

        $response = Http::withToken($token)
            ->get(
                url: 'https://analyticsadmin.googleapis.com/v1beta/properties',
                query: $query
            );

        if (! $response->successful()) {
            throw CannotSyncGoogleAnalyticsPropertiesException::becauseOfHttpErrorWithStatusCode(
                googleAnalyticsAccount: $googleAnalyticsAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
