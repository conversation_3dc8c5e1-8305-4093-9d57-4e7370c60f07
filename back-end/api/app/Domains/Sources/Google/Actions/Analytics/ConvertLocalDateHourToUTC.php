<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Analytics;

use Carbon\CarbonInterface;
use Carbon\CarbonTimeZone;
use Illuminate\Support\Carbon;

class ConvertLocalDateHourToUTC
{
    public function execute(string $dateHour, string $timeZone = 'Europe/Amsterdam'): CarbonInterface
    {
        $date = substr($dateHour, 0, 8);
        $hour = substr($dateHour, 8, 2);

        $dateTimeString = $date.' '.$hour.':00:00';

        // Create a Carbon object in the local timezone
        $localCarbonObject = Carbon::createFromFormat(
            format: 'Ymd H:i:s',
            time: $dateTimeString,
            timezone: new CarbonTimeZone($timeZone)
        );

        return $localCarbonObject->utc();
    }
}
