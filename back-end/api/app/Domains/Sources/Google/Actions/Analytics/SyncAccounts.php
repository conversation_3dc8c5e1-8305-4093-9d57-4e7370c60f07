<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsAccountsException;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class SyncAccounts
{
    /**
     * @throws CannotSyncGoogleAnalyticsAccountsException
     * @throws ConnectionException
     */
    public function execute(GoogleAccount $googleAccount, ?string $nextPageToken = null): void
    {
        $response = $this->getResponse($googleAccount, $nextPageToken);
        $nextPageToken = $response->json('nextPageToken');
        $accounts = $response->json('accounts', []);

        $this->processAccounts(
            googleAccount: $googleAccount,
            accounts: $accounts
        );

        if ($nextPageToken) {
            app(self::class)->execute(
                googleAccount: $googleAccount,
                nextPageToken: $nextPageToken
            );
        }
    }

    protected function processAccounts(GoogleAccount $googleAccount, array $accounts): void
    {
        foreach ($accounts as $account) {
            $this->processAccount(
                googleAccount: $googleAccount,
                account: $account
            );
        }
    }

    protected function processAccount(GoogleAccount $googleAccount, array $account): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::withoutGlobalScope(SyncDeactivatedScope::class)->where('external_id', $account['name'])->first();

        if (! $googleAnalyticsAccount) {
            $googleAnalyticsAccount = new GoogleAnalyticsAccount;
        }

        $googleAnalyticsAccount->google_account_id = $googleAccount->id;
        $googleAnalyticsAccount->external_id = $account['name'];
        $googleAnalyticsAccount->name = $account['displayName'];
        $googleAnalyticsAccount->region_code = $account['regionCode'];
        $googleAnalyticsAccount->external_created_at = $account['createTime'];
        $googleAnalyticsAccount->external_updated_at = $account['updateTime'];
        $googleAnalyticsAccount->last_synced_at = now();

        $googleAnalyticsAccount->save();
    }

    /**
     * @throws CannotSyncGoogleAnalyticsAccountsException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAccount $googleAccount, ?string $nextPageToken): Response
    {
        $token = app(RefreshToken::class)->execute($googleAccount);

        if (! $token) {
            throw CannotSyncGoogleAnalyticsAccountsException::becauseOfMissingToken($googleAccount);
        }

        $query = [
            'pageSize' => 200,
            'showDeleted' => false,
        ];

        if ($nextPageToken) {
            $query['pageToken'] = $nextPageToken;
        }

        $response = Http::withToken($token)
            ->get(
                url: 'https://analyticsadmin.googleapis.com/v1beta/accounts',
                query: $query
            );

        if (! $response->successful()) {
            throw CannotSyncGoogleAnalyticsAccountsException::becauseOfHttpErrorWithStatusCode(
                googleAccount: $googleAccount,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
