<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;

class ConvertEventsToDataSourceForProperty
{
    public function execute(GoogleAnalyticsProperty $googleAnalyticsProperty): void
    {
        if (! $googleAnalyticsProperty->dataSource) {
            return;
        }

        $reportTypes = $this->getReportTypes($googleAnalyticsProperty->dataSource);

        foreach ($reportTypes as $reportType) {
            $this->updateReportType($googleAnalyticsProperty, $reportType);
        }
    }

    /**
     * @return ReportType[]
     */
    protected function getReportTypes(DataSource $dataSource): array
    {
        $types = array_keys($dataSource->options['types'] ?? []);

        return array_map(fn (string $type) => ReportType::from($type), $types);
    }

    /**
     * @return string[]
     */
    protected function getEvents(DataSource $dataSource, ReportType $reportType): array
    {
        return $dataSource->options['types'][$reportType->value];
    }

    public function updateReportType(GoogleAnalyticsProperty $googleAnalyticsProperty, ReportType $reportType): void
    {
        $events = $this->getEvents($googleAnalyticsProperty->dataSource, $reportType);

        GoogleAnalyticsReport::query()
            ->where('google_analytics_property_id', $googleAnalyticsProperty->id)
            ->whereIn('name', $events)
            ->update([
                'type' => $reportType->value,
            ]);
    }
}
