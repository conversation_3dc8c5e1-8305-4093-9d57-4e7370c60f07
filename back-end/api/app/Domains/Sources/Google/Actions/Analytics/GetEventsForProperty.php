<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Analytics;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetEventsForPropertyException;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Throwable;

class GetEventsForProperty
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    /**
     * @throws CannotGetEventsForPropertyException
     * @throws ConnectionException
     */
    public function execute(GoogleAnalyticsProperty $googleAnalyticsProperty, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): void
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($googleAnalyticsProperty);
        $timeZone = $response->json('metadata.timeZone', '');
        $rows = $response->json('rows', []);

        $this->processRows(
            googleAnalyticsProperty: $googleAnalyticsProperty,
            timeZone: $timeZone,
            rows: $rows
        );
    }

    /**
     * @throws CannotGetEventsForPropertyException
     */
    protected function processRows(GoogleAnalyticsProperty $googleAnalyticsProperty, string $timeZone, array $rows): void
    {
        foreach ($rows as $row) {
            $this->processRow($googleAnalyticsProperty, $timeZone, $row);
        }
    }

    /**
     * @throws CannotGetEventsForPropertyException
     */
    protected function processRow(GoogleAnalyticsProperty $googleAnalyticsProperty, string $timeZone, array $row): void
    {
        try {
            $date = app(ConvertLocalDateHourToUTC::class)->execute(
                dateHour: $row['dimensionValues'][0]['value'],
                timeZone: $timeZone
            );
        } catch (Throwable $exception) {
            throw CannotGetEventsForPropertyException::becauseOfDateHourConversionError(
                googleAnalyticsProperty: $googleAnalyticsProperty,
                dateHour: $row['dimensionValues'][0]['value'] ?? null,
                timeZone: $timeZone,
                originalMessage: $exception->getMessage(),
            );
        }

        GoogleAnalyticsReport::updateOrCreate(
            [
                'google_analytics_property_id' => $googleAnalyticsProperty->id,
                'date' => $date,
                'type' => ReportType::EVENT->value,
                'name' => $row['dimensionValues'][1]['value'],
            ],
            [
                'value' => (int) $row['metricValues'][0]['value'],
            ]
        );
    }

    /**
     * @throws CannotGetEventsForPropertyException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAnalyticsProperty $googleAnalyticsProperty): Response
    {
        $token = app(RefreshToken::class)->execute(
            $googleAnalyticsProperty->googleAnalyticsAccount->googleAccount
        );

        if (! $token) {
            throw CannotGetEventsForPropertyException::becauseOfMissingToken($googleAnalyticsProperty);
        }

        $body = [
            'dimensions' => [
                ['name' => 'dateHour'],
                ['name' => 'eventName'],
            ],
            'metrics' => [
                ['name' => 'eventCount'],
            ],
            'dateRanges' => [
                [
                    'startDate' => $this->startDate?->format('Y-m-d') ?? '7daysAgo',
                    'endDate' => $this->endDate?->format('Y-m-d') ?? 'yesterday',
                ],
            ],
        ];

        $url = sprintf('https://analyticsdata.googleapis.com/v1beta/%s:runReport', $googleAnalyticsProperty->external_id);
        $response = Http::withToken($token)
            ->post(
                url: $url,
                data: $body
            );

        if (! $response->successful()) {
            throw CannotGetEventsForPropertyException::becauseOfHttpErrorWithStatusCode(
                googleAnalyticsProperty: $googleAnalyticsProperty,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
