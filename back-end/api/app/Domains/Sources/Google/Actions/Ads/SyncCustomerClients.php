<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Support\Exceptions\Ads\CannotGetCustomerClientsException;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class SyncCustomerClients
{
    /**
     * @return GoogleAdAccount[]
     *
     * @throws CannotGetCustomerClientsException
     * @throws ConnectionException
     */
    public function execute(GoogleAccount $googleAccount, int $customerId, ?int $loginCustomerId = null): array
    {
        $response = $this->getResponse(
            googleAccount: $googleAccount,
            customerId: $customerId,
            loginCustomerId: $loginCustomerId
        );
        $customerClients = $response->json('0.results', []);

        return $this->processCustomerClients(
            googleAccount: $googleAccount,
            customerClients: $customerClients,
            customerId: $customerId,
            loginCustomerId: $loginCustomerId
        );
    }

    /**
     * @return GoogleAdAccount[]
     */
    protected function processCustomerClients(GoogleAccount $googleAccount, array $customerClients, int $customerId, ?int $loginCustomerId = null): array
    {
        $results = [];

        foreach ($customerClients as $account) {
            $customerClient = Arr::get($account, 'customerClient');
            if (! $customerClient) {
                continue;
            }

            $results[] = $this->processCustomerClient(
                googleAccount: $googleAccount,
                customerClient: $customerClient,
                customerId: $customerId,
                loginCustomerId: $loginCustomerId
            );
        }

        return $results;
    }

    protected function processCustomerClient(GoogleAccount $googleAccount, array $customerClient, int $customerId, ?int $loginCustomerId = null): GoogleAdAccount
    {
        return GoogleAdAccount::query()->withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate([
            'google_account_id' => $googleAccount->id,
            'customer_id' => $loginCustomerId ?? $customerId,
            'external_id' => (int) basename($customerClient['resourceName']),
        ], [
            'name' => $customerClient['descriptiveName'],
            'status' => $customerClient['status'],
            'level' => $customerClient['level'],
            'is_manager' => $customerClient['manager'],
            'is_hidden' => $customerClient['hidden'],
            'is_test_account' => $customerClient['testAccount'],
            'time_zone' => $customerClient['timeZone'],
            'currency' => $customerClient['currencyCode'],
            'last_synced_at' => now(),
        ]);
    }

    /**
     * @throws CannotGetCustomerClientsException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAccount $googleAccount, int $customerId, ?int $loginCustomerId = null): Response
    {
        $token = app(RefreshToken::class)->execute($googleAccount);

        if (! $token) {
            throw CannotGetCustomerClientsException::becauseOfMissingToken($googleAccount, $customerId);
        }

        $headers = [
            'Developer-token' => config('services.google.ads_developer_token'),
        ];

        if ($loginCustomerId) {
            $headers['login-customer-id'] = $loginCustomerId;
        }

        $response = Http::withToken($token)
            ->withHeaders($headers)
            ->post(
                url: sprintf('https://googleads.googleapis.com/v18/customers/%s/googleAds:searchStream', $customerId),
                data: [
                    'query' => <<<'SQL'
                        SELECT
                            customer_client.descriptive_name,
                            customer_client.status,
                            customer_client.hidden,
                            customer_client.level,
                            customer_client.time_zone,
                            customer_client.test_account,
                            customer_client.manager,
                            customer_client.currency_code,
                            customer_client.resource_name,
                            customer_client.level
                        FROM
                            customer_client
                    SQL,
                ]
            );

        if (! $response->successful()) {
            if ($response->status() === 403 && Str::contains($response->body(), 'CUSTOMER_NOT_ENABLED')) {
                return $response;
            }

            throw CannotGetCustomerClientsException::becauseOfHttpErrorWithStatusCode(
                googleAccount: $googleAccount,
                customerId: $customerId,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
