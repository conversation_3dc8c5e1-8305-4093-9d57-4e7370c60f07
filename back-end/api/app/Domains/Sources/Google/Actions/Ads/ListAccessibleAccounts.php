<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Exceptions\Ads\CannotListAccessibleAccountsException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class ListAccessibleAccounts
{
    /**
     * @throws CannotListAccessibleAccountsException
     * @throws ConnectionException
     */
    public function execute(GoogleAccount $googleAccount): array
    {
        $response = $this->getResponse($googleAccount);

        return $response->json('resourceNames', []);
    }

    /**
     * @throws CannotListAccessibleAccountsException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAccount $googleAccount): Response
    {
        $token = app(RefreshToken::class)->execute($googleAccount);

        if (! $token) {
            throw CannotListAccessibleAccountsException::becauseOfMissingToken($googleAccount);
        }

        $response = Http::withToken($token)
            ->withHeader('Developer-token', config('services.google.ads_developer_token'))
            ->get('https://googleads.googleapis.com/v18/customers:listAccessibleCustomers');

        if (! $response->successful()) {
            throw CannotListAccessibleAccountsException::becauseOfHttpErrorWithStatusCode(
                googleAccount: $googleAccount,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
