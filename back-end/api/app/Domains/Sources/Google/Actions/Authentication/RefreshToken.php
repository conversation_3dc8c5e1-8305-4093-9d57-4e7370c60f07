<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class RefreshToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(GoogleAccount $googleAccount): ?string
    {
        if (! $this->shouldRefreshToken($googleAccount)) {
            return $googleAccount->token;
        }

        $token = $this->refreshToken($googleAccount);

        $googleAccount->token = $token;
        $googleAccount->token_created_at = $token ? now() : null;

        $googleAccount->save();

        return $token;
    }

    protected function shouldRefreshToken(GoogleAccount $googleAccount): bool
    {
        if (! $googleAccount->token_created_at || ! $googleAccount->token) {
            return true;
        }

        $expiryThreshold = now()->subMinutes(55);

        return $googleAccount->token_created_at->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function refreshToken(GoogleAccount $googleAccount): ?string
    {
        $response = Http::asForm()
            ->connectTimeout(10)
            ->post(
                url: 'https://oauth2.googleapis.com/token',
                data: [
                    'client_id' => config('services.google.client_id'),
                    'client_secret' => config('services.google.client_secret'),
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $googleAccount->refresh_token,
                ]
            );

        if (! $response->successful()) {
            return null;
        }

        return $response->json('access_token');
    }
}
