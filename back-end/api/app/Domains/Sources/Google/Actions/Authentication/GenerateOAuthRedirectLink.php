<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Authentication;

use Laravel\Socialite\Facades\Socialite;
use <PERSON><PERSON>\Socialite\Two\GoogleProvider;

class GenerateOAuthRedirectLink
{
    private string $redirectUrl;

    public function execute(string $redirectUrl): string
    {
        $this->redirectUrl = $redirectUrl;

        return $this
            ->getGoogleProvider()
            ->redirect()
            ->getTargetUrl();
    }

    protected function getGoogleProvider(): GoogleProvider
    {
        /** @var GoogleProvider $provider */
        $provider = Socialite::driver('google');

        $provider->scopes([
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/adwords',
        ]);
        $provider->with([
            'access_type' => 'offline',
            'prompt' => 'consent select_account',
            'state' => [],
        ]);
        $provider->redirectUrl($this->redirectUrl);
        $provider->stateless();

        return $provider;
    }
}
