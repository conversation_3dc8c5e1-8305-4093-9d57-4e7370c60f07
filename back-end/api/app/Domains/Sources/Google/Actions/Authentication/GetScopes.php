<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Support\Facades\Http;

class GetScopes
{
    public const ENDPOINT = 'https://www.googleapis.com/oauth2/v1/tokeninfo';

    public function execute(GoogleAccount $googleAccount): array
    {
        $token = app(RefreshToken::class)->execute($googleAccount);

        if (! $token) {
            return [];
        }

        $response = Http::post(self::ENDPOINT, [
            'access_token' => $token,
        ]);

        if (! $response->successful()) {
            return [];
        }

        return explode(' ', $response->json('scope') ?? '');
    }
}
