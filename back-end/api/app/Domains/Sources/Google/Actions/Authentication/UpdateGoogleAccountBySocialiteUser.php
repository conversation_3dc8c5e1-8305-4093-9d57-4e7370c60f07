<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Authentication;

use App\Domains\Slack\Messages\NewGoogleAccountConnectedMessage;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Support\SlackNotification;
use Lara<PERSON>\Socialite\Two\User;

class UpdateGoogleAccountBySocialiteUser
{
    public function execute(User $user, ?array $scopes = null): GoogleAccount
    {
        $googleAccount = GoogleAccount::where('external_id', $user->getId())->first();

        if (! $googleAccount) {
            $googleAccount = new GoogleAccount;
        }

        $googleAccount->external_id = $user->getId();
        $googleAccount->email = $user->getEmail();
        $googleAccount->name = $user->getName();
        $googleAccount->image_url = $user->getAvatar();
        $googleAccount->refresh_token = $user->refreshToken;
        $googleAccount->token = $user->token;
        $googleAccount->token_created_at = now();
        $googleAccount->scopes = $scopes;

        $googleAccount->save();

        if ($googleAccount->wasRecentlyCreated) {
            SlackNotification::send(new NewGoogleAccountConnectedMessage($googleAccount));
        }

        return $googleAccount;
    }
}
