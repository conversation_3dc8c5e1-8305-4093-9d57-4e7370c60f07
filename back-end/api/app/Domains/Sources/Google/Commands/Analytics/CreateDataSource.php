<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Console\Command;

class CreateDataSource extends Command
{
    protected $signature = 'sources:google:analytics:data_source {googleAnalyticsPropertyId}';

    protected $description = 'Create Data Source for a Google Analytics Property';

    public function handle(): int
    {
        $googleAnalyticsPropertyId = $this->argument('googleAnalyticsPropertyId');
        $googleAnalyticsProperty = GoogleAnalyticsProperty::query()->find($googleAnalyticsPropertyId);

        if (! $googleAnalyticsProperty) {
            $this->error('Google Analytics Property not found');

            return 1;
        }

        $existingDataSource = DataSource::query()
            ->where('sourceable_id', $googleAnalyticsProperty->id)
            ->where('sourceable_type', GoogleAnalyticsProperty::class)
            ->exists();

        if ($existingDataSource) {
            $this->error('Data Source already exists for Google Analytics Property');

            return 1;
        }

        $title = $this->ask('What title?');
        $region = $this->ask('What region?');
        $businessUnit = $this->ask('What business_unit?');
        $channel = $this->ask('What channel?');

        DataSource::query()->create([
            'title' => $title,
            'region' => $region,
            'business_unit' => $businessUnit,
            'channel' => $channel,
            'sourceable_id' => $googleAnalyticsProperty->id,
            'sourceable_type' => GoogleAnalyticsProperty::class,
        ]);

        $this->info('Data source created for Google Analytics Property connected successfully');

        return 0;
    }
}
