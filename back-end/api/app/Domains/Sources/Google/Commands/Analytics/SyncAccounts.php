<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Console\Command;

class SyncAccounts extends Command
{
    protected $signature = 'sources:google:analytics:sync:accounts {googleAccountId}';

    protected $description = 'Sync Google Analytics accounts for a Google Account';

    public function handle(): int
    {
        $googleAccountId = $this->argument('googleAccountId');

        /** @var GoogleAccount|null $googleAccount */
        $googleAccount = GoogleAccount::find($googleAccountId);

        if (! $googleAccount) {
            $this->error('Google Account not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Google Analytics accounts for Google Account #%s (%s)',
            $googleAccount->id,
            $googleAccount->name
        ));

        app(\App\Domains\Sources\Google\Actions\Analytics\SyncAccounts::class)
            ->execute($googleAccount);

        $this->info('Google Analytics accounts synced successfully');

        return 0;
    }
}
