<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Actions\Analytics\ConvertEventsToDataSourceForProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Console\Command;

class GetEventsForProperty extends Command
{
    protected $signature = 'sources:google:analytics:sync:events {googleAnalyticsPropertyId}';

    protected $description = 'Sync Google Analytics events for a Google Analytics Property';

    public function handle(): int
    {
        $googleAnalyticsPropertyId = $this->argument('googleAnalyticsPropertyId');

        /** @var GoogleAnalyticsProperty|null $googleAnalyticsProperty */
        $googleAnalyticsProperty = GoogleAnalyticsProperty::find($googleAnalyticsPropertyId);

        if (! $googleAnalyticsProperty) {
            $this->error('Google Analytics Property not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Google Analytics events for Google Analytics Property #%s (%s / %s)',
            $googleAnalyticsProperty->id,
            $googleAnalyticsProperty->googleAnalyticsAccount->name,
            $googleAnalyticsProperty->name
        ));

        app(\App\Domains\Sources\Google\Actions\Analytics\GetEventsForProperty::class)
            ->execute($googleAnalyticsProperty);
        app(ConvertEventsToDataSourceForProperty::class)
            ->execute($googleAnalyticsProperty);

        $this->info('Google Analytics events synced successfully');

        return 0;
    }
}
