<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Illuminate\Console\Command;

class GetSessionsForProperty extends Command
{
    protected $signature = 'sources:google:analytics:sync:sessions {googleAnalyticsPropertyId}';

    protected $description = 'Sync Google Analytics sessions for a Google Analytics Property';

    public function handle(): int
    {
        $googleAnalyticsPropertyId = $this->argument('googleAnalyticsPropertyId');

        /** @var GoogleAnalyticsProperty|null $googleAnalyticsProperty */
        $googleAnalyticsProperty = GoogleAnalyticsProperty::find($googleAnalyticsPropertyId);

        if (! $googleAnalyticsProperty) {
            $this->error('Google Analytics Property not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Google Analytics sessions for Google Analytics Property #%s (%s / %s)',
            $googleAnalyticsProperty->id,
            $googleAnalyticsProperty->googleAnalyticsAccount->name,
            $googleAnalyticsProperty->name
        ));

        app(\App\Domains\Sources\Google\Actions\Analytics\GetSessionsForProperty::class)
            ->execute($googleAnalyticsProperty);

        $this->info('Google Analytics sessions synced successfully');

        return 0;
    }
}
