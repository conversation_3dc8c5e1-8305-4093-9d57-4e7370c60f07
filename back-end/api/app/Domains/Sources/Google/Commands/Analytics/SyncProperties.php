<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands\Analytics;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Illuminate\Console\Command;

class SyncProperties extends Command
{
    protected $signature = 'sources:google:analytics:sync:properties {googleAnalyticsAccountId}';

    protected $description = 'Sync Google Analytics properties for a Google Analytics Account';

    public function handle(): int
    {
        $googleAnalyticsAccountId = $this->argument('googleAnalyticsAccountId');

        /** @var GoogleAnalyticsAccount|null $googleAnalyticsAccount */
        $googleAnalyticsAccount = GoogleAnalyticsAccount::find($googleAnalyticsAccountId);

        if (! $googleAnalyticsAccount) {
            $this->error('Google Analytics Account not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Google Analytics properties for Google Analytics Account #%s (%s)',
            $googleAnalyticsAccount->id,
            $googleAnalyticsAccount->name
        ));

        app(\App\Domains\Sources\Google\Actions\Analytics\SyncProperties::class)
            ->execute($googleAnalyticsAccount);

        $this->info('Google Analytics properties synced successfully');

        return 0;
    }
}
