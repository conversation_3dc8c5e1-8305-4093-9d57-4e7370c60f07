<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Commands;

use App\Domains\Sources\Google\Jobs\SyncGoogleAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticAccounts;
use App\Domains\Sources\Google\Jobs\SyncGoogleAnalyticProperties;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class Sync extends Command
{
    protected $signature = 'sources:google:sync';

    protected $description = 'Sync all Google related connections';

    public function handle(): void
    {
        $this->info('Dispatching jobs to sync all Google connections');

        Bus::chain([
            new SyncGoogleAccounts,
            new SyncGoogleAnalyticAccounts,
            new SyncGoogleAnalyticProperties,
        ])->dispatch();

        $this->info('Jobs dispatched successfully');
    }
}
