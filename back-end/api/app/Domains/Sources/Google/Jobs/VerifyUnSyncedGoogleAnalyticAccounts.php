<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsAccountIsUnSyncedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class VerifyUnSyncedGoogleAnalyticAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        GoogleAnalyticsAccount::query()
            ->where('last_synced_at', '<', now()->subDay())
            ->chunk(100, function (Collection $googleAnalyticsAccounts) {
                foreach ($googleAnalyticsAccounts as $googleAnalyticsAccount) {
                    $mail = new GoogleAnalyticsAccountIsUnSyncedMail($googleAnalyticsAccount->id);
                    Mail::queue($mail);
                }
            });
    }
}
