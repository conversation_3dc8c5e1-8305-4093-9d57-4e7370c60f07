<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Analytics\SyncProperties;
use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsPropertiesException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class SyncGoogleAnalyticAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAnalyticsAccountId) {}

    /**
     * @throws CannotSyncGoogleAnalyticsPropertiesException
     * @throws ConnectionException
     */
    public function handle(): void
    {
        $googleAnalyticsAccount = GoogleAnalyticsAccount::query()
            ->findOrFail($this->googleAnalyticsAccountId);

        app(SyncProperties::class)->execute(
            googleAnalyticsAccount: $googleAnalyticsAccount
        );
    }
}
