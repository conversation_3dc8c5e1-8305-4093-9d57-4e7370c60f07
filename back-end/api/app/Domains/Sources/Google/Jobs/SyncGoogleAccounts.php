<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncGoogleAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (GoogleAccount $googleAccount) {
            $job = new SyncGoogleAccount($googleAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return GoogleAccount::query()
            ->chunk(100, function (Collection $googleAccounts) use ($closure) {
                foreach ($googleAccounts as $googleAccount) {
                    $closure($googleAccount);
                }
            });
    }
}
