<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncGoogleAnalyticProperties implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (GoogleAnalyticsProperty $googleAnalyticsProperty) {
            $job = new SyncGoogleAnalyticProperty($googleAnalyticsProperty->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return GoogleAnalyticsProperty::query()
            ->chunk(100, function (Collection $googleAnalyticsProperties) use ($closure) {
                foreach ($googleAnalyticsProperties as $googleAnalyticsProperty) {
                    $closure($googleAnalyticsProperty);
                }
            });
    }
}
