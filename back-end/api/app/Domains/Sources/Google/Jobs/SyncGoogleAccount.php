<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domains\Sources\Google\Actions\Analytics\SyncAccounts;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotSyncGoogleAnalyticsAccountsException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class SyncGoogleAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAccountId) {}

    /**
     * @throws ConnectionException
     * @throws CannotSyncGoogleAnalyticsAccountsException
     */
    public function handle(): void
    {
        $googleAccount = GoogleAccount::query()
            ->findOrFail($this->googleAccountId);

        $this->syncGoogleAnalytics($googleAccount);
        $this->syncGoogleAds($googleAccount);
    }

    /**
     * @throws CannotSyncGoogleAnalyticsAccountsException
     * @throws ConnectionException
     */
    public function syncGoogleAnalytics(GoogleAccount $googleAccount): void
    {
        app(SyncAccounts::class)->execute(
            googleAccount: $googleAccount
        );
    }

    public function syncGoogleAds(GoogleAccount $googleAccount): void
    {
        $accessibleAccounts = app(ListAccessibleAccounts::class)->execute(
            $googleAccount
        );

        foreach ($accessibleAccounts as $accessibleAccount) {
            $job = new SyncGoogleAdAccount(
                googleAccountId: $googleAccount->id,
                customerId: (int) basename($accessibleAccount)
            );
            dispatch($job);
        }
    }
}
