<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\SyncCustomerClients;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Exceptions\Ads\CannotGetCustomerClientsException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class SyncGoogleAdAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAccountId, public int $customerId, public ?int $loginCustomerId = null) {}

    /**
     * @throws ConnectionException
     * @throws CannotGetCustomerClientsException
     */
    public function handle(): void
    {
        $googleAccount = GoogleAccount::query()
            ->findOrFail($this->googleAccountId);

        $accounts = app(SyncCustomerClients::class)->execute(
            googleAccount: $googleAccount,
            customerId: $this->customerId,
            loginCustomerId: $this->loginCustomerId
        );

        foreach ($accounts as $account) {
            if ($account->external_id === $this->customerId) {
                continue;
            }

            if (! $account->is_manager) {
                continue;
            }

            $job = new self(
                googleAccountId: $this->googleAccountId,
                customerId: $account->external_id,
                loginCustomerId: $account->customer_id,
            );
            dispatch($job);
        }
    }
}
