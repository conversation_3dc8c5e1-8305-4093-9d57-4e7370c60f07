<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Authentication\GetScopes;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Support\Mails\Authentication\GoogleAccountMissingRemoteScopesMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class VerifyScopesForGoogleAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAccountId) {}

    public function handle(): void
    {
        $googleAccount = GoogleAccount::query()->findOrFail($this->googleAccountId);

        $localScopes = $googleAccount->scopes ?? [];
        $remoteScopes = app(GetScopes::class)->execute($googleAccount);

        $missingLocalScopes = array_diff($localScopes, $remoteScopes);

        if (empty($missingLocalScopes)) {
            return;
        }

        $googleAccount->deactivated_at = now();
        $googleAccount->save();

        $mail = new GoogleAccountMissingRemoteScopesMail($googleAccount->id, $missingLocalScopes);
        Mail::queue($mail);
    }
}
