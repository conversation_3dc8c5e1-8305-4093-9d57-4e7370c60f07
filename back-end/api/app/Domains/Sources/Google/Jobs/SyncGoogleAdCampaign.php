<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Ads\GetCampaignsWithMetrics;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncGoogleAdCampaign implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAdAccountId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $googleAdAccount = GoogleAdAccount::query()
            ->findOrFail($this->googleAdAccountId);

        app(GetCampaignsWithMetrics::class)->execute($googleAdAccount, $this->startDate, $this->endDate);
    }
}
