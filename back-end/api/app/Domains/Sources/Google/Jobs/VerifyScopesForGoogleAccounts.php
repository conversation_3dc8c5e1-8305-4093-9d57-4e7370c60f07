<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Queue\Queueable;

class VerifyScopesForGoogleAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        GoogleAccount::query()
            ->whereNull('deactivated_at')
            ->chunk(100, function (Collection $googleAccounts) {
                foreach ($googleAccounts as $googleAccount) {
                    $job = new VerifyScopesForGoogleAccount($googleAccount->id);
                    dispatch($job);
                }
            });
    }
}
