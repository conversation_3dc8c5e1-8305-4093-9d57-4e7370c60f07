<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Actions\Analytics\ConvertEventsToDataSourceForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetChannelGroupsForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetEventsForProperty;
use App\Domains\Sources\Google\Actions\Analytics\GetSessionsForProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetChannelGroupsForPropertyException;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetEventsForPropertyException;
use App\Domains\Sources\Google\Support\Exceptions\Analytics\CannotGetSessionsForPropertyException;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class SyncGoogleAnalyticProperty implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleAnalyticsPropertyId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    /**
     * @throws CannotGetSessionsForPropertyException
     * @throws CannotGetEventsForPropertyException
     * @throws ConnectionException|CannotGetChannelGroupsForPropertyException
     */
    public function handle(): void
    {
        $googleAnalyticsProperty = GoogleAnalyticsProperty::query()
            ->findOrFail($this->googleAnalyticsPropertyId);

        $this->syncSessions($googleAnalyticsProperty);
        $this->syncChannelGroups($googleAnalyticsProperty);
        $this->syncEvents($googleAnalyticsProperty);
    }

    /**
     * @throws CannotGetEventsForPropertyException
     * @throws ConnectionException
     */
    public function syncEvents(GoogleAnalyticsProperty $googleAnalyticsProperty): void
    {
        app(GetEventsForProperty::class)->execute(
            googleAnalyticsProperty: $googleAnalyticsProperty,
            startDate: $this->startDate,
            endDate: $this->endDate
        );
        app(ConvertEventsToDataSourceForProperty::class)->execute(
            googleAnalyticsProperty: $googleAnalyticsProperty
        );
    }

    /**
     * @throws CannotGetSessionsForPropertyException
     * @throws ConnectionException
     */
    public function syncSessions(GoogleAnalyticsProperty $googleAnalyticsProperty): void
    {
        app(GetSessionsForProperty::class)->execute(
            googleAnalyticsProperty: $googleAnalyticsProperty,
            startDate: $this->startDate,
            endDate: $this->endDate
        );
    }

    /**
     * @throws CannotGetChannelGroupsForPropertyException
     * @throws ConnectionException
     */
    public function syncChannelGroups(GoogleAnalyticsProperty $googleAnalyticsProperty): void
    {
        app(GetChannelGroupsForProperty::class)->execute(
            googleAnalyticsProperty: $googleAnalyticsProperty,
            startDate: $this->startDate,
            endDate: $this->endDate
        );
    }
}
