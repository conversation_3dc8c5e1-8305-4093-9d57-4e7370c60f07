<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Support\Mails\Ads\GoogleAdAccountIsUnSyncedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class VerifyUnSyncedGoogleAdAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        GoogleAdAccount::query()
            ->where('last_synced_at', '<', now()->subDay())
            ->chunk(100, function (Collection $googleAnalyticsAccounts) {
                foreach ($googleAnalyticsAccounts as $googleAnalyticsAccount) {
                    $mail = new GoogleAdAccountIsUnSyncedMail($googleAnalyticsAccount->id);
                    Mail::queue($mail);
                }
            });
    }
}
