<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Support\Mails\Analytics\GoogleAnalyticsPropertyIsUnSyncedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class VerifyUnSyncedGoogleAnalyticProperties implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->sendMailForUnSyncedPropertiesWithDataSource();
        $this->deleteUnSyncedPropertiesWithoutDataSource();
    }

    protected function baseQuery(): Builder
    {
        return GoogleAnalyticsProperty::query()
            ->where('last_synced_at', '<', now()->subDay());
    }

    protected function sendMailForUnSyncedPropertiesWithDataSource(): void
    {
        $this->baseQuery()
            ->whereHas('dataSource')
            ->chunk(100, function (Collection $googleAnalyticsProperties) {
                foreach ($googleAnalyticsProperties as $googleAnalyticsProperty) {
                    $mail = new GoogleAnalyticsPropertyIsUnSyncedMail($googleAnalyticsProperty->id);
                    Mail::queue($mail);
                }
            });
    }

    protected function deleteUnSyncedPropertiesWithoutDataSource(): void
    {
        $this->baseQuery()
            ->whereDoesntHave('dataSource')
            ->delete();
    }
}
