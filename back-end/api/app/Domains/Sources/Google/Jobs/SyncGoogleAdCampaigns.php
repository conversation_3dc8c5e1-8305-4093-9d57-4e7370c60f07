<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAdAccount;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncGoogleAdCampaigns implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (GoogleAdAccount $googleAdAccount) {
            $job = new SyncGoogleAdCampaign($googleAdAccount->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return GoogleAdAccount::query()
            ->where('is_manager', false)
            ->chunk(100, function (Collection $googleAdAccounts) use ($closure) {
                foreach ($googleAdAccounts as $googleAdAccount) {
                    $closure($googleAdAccount);
                }
            });
    }
}
