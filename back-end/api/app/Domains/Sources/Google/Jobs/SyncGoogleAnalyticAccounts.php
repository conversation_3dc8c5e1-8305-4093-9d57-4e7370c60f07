<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Jobs;

use App\Domains\Sources\Google\Models\GoogleAnalyticsAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncGoogleAnalyticAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (GoogleAnalyticsAccount $googleAnalyticsAccount) {
            $job = new SyncGoogleAnalyticAccount($googleAnalyticsAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return GoogleAnalyticsAccount::query()
            ->chunk(100, function (Collection $googleAnalyticsAccounts) use ($closure) {
                foreach ($googleAnalyticsAccounts as $googleAnalyticsAccount) {
                    $closure($googleAnalyticsAccount);
                }
            });
    }
}
