<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Support\Exceptions;

use Exception;

class CannotGetGoogleBusinessProfile extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(
        int $statusCode,
        string $body
    ): self {
        return new self(
            'Cannot get Google Business Profile. '.
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
