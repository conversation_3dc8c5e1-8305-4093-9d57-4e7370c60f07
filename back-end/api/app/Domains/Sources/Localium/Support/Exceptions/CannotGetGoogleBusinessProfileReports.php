<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Support\Exceptions;

use Exception;

class CannotGetGoogleBusinessProfileReports extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(
        int $statusCode,
        string $body
    ): self {
        return new self(
            'Cannot get Google Business Profile statistics. '.
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
