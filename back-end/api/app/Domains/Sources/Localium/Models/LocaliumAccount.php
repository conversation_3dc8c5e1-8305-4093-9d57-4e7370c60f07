<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Models;

use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocaliumAccount extends Model
{
    use HasFactory;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token' => 'encrypted',
            'last_synced_at' => 'datetime',
        ];
    }
}
