<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleBusinessProfile extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'sync_deactivated' => 'boolean',
            'last_synced_at' => 'datetime',
        ];
    }

    public function reports(): HasMany
    {
        return $this->hasMany(GoogleBusinessProfileReport::class);
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'id', 'sourceable_id')
            ->where('sourceable_type', self::class);
    }

    public function localiumAccount(): BelongsTo
    {
        return $this->belongsTo(LocaliumAccount::class, 'localium_account_id');
    }
}
