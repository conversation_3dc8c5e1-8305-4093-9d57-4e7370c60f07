<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Actions;

use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfileReport;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use App\Domains\Sources\Localium\Support\Exceptions\CannotGetGoogleBusinessProfileReports;
use App\Support\Scopes\SyncDeactivatedScope;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetGoogleBusinessProfileReports
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    public function execute(LocaliumAccount $account, array $locationIds, string $timezone = 'Europe/Amsterdam', ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): void
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($account, $locationIds, $timezone);
        $statistics = $response->json('data', []);

        $this->processStatistics($statistics);
    }

    protected function processStatistics(array $statistics): void
    {
        foreach ($statistics as $statistic) {
            $this->processStatistic($statistic);
        }
    }

    protected function processStatistic(array $statistic): void
    {
        $profile = GoogleBusinessProfile::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('external_id', $statistic['location_id'])
            ->first();

        if (! $profile) {
            return;
        }

        GoogleBusinessProfileReport::query()->updateOrCreate(
            [
                'google_business_profile_id' => $profile->id,
                'date' => Carbon::createFromFormat('Ymd', $statistic['date'])->startOfDay(),
            ],
            [
                'phone_calls' => $statistic['call_clicks'],
                'views_directions' => $statistic['actions_driving_directions'],
                'views_maps' => $statistic['business_impressions_desktop_maps'] + $statistic['business_impressions_mobile_maps'],
                'views_google' => $statistic['business_impressions_desktop_search'] + $statistic['business_impressions_mobile_search'],
            ]
        );
    }

    /**
     * @throws CannotGetGoogleBusinessProfileReports
     * @throws ConnectionException
     */
    protected function getResponse(LocaliumAccount $account, array $locationIds, string $timezone): Response
    {
        $response = Http::withToken($account->token)
            ->withHeaders([
                'User' => $account->user_id,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->post('https://app.localium.com/api/api/v1/google/data-studio/statistics', [
                'location_ids' => $locationIds,
                'time_zone' => $timezone,
                'start' => ($this->startDate ?? now()->subMonths(3))->format('Y-m-d'),
                'end' => ($this->endDate ?? now())->format('Y-m-d'),
            ]);

        if (! $response->successful()) {
            throw CannotGetGoogleBusinessProfileReports::becauseOfHttpErrorWithStatusCode(
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
