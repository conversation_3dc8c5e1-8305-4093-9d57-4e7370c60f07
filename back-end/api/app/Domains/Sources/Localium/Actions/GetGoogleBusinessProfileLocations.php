<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Actions;

use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use App\Domains\Sources\Localium\Support\Exceptions\CannotGetGoogleBusinessProfile;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetGoogleBusinessProfileLocations
{
    public function execute(LocaliumAccount $account): void
    {
        $response = $this->getResponse($account);
        $locations = $response->json('data.locations', []);

        $this->processLocations(
            account: $account,
            locations: $locations
        );
    }

    protected function processLocations(
        LocaliumAccount $account,
        array $locations
    ): void {
        foreach ($locations as $location) {
            $this->processLocation($account, $location);
        }
    }

    protected function processLocation(
        LocaliumAccount $account,
        array $location
    ): void {
        $externalId = $location['id'];

        GoogleBusinessProfile::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->updateOrCreate(
                [
                    'external_id' => $externalId,
                    'localium_account_id' => $account->id,
                ],
                [
                    'name' => $location['name'],
                    'last_synced_at' => now(),
                ]
            );
    }

    /**
     * @throws CannotGetGoogleBusinessProfile
     * @throws ConnectionException
     */
    protected function getResponse(LocaliumAccount $account): Response
    {
        $response = Http::withToken($account->token)->withHeaders([
            'User' => $account->user_id,
        ])->get('https://app.localium.com/api/api/v1/google/data-studio/config');

        if (! $response->successful()) {
            throw CannotGetGoogleBusinessProfile::becauseOfHttpErrorWithStatusCode(
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
