<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Jobs;

use App\Domains\Sources\Localium\Models\LocaliumAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLocaliumAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (LocaliumAccount $localiumAccount) {
            $job = new SyncLocaliumAccount($localiumAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LocaliumAccount::query()
            ->chunk(100, function (Collection $localiumAccounts) use ($closure) {
                foreach ($localiumAccounts as $localiumAccount) {
                    $closure($localiumAccount);
                }
            });
    }
}
