<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Jobs;

use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class SyncGoogleBusinessProfileReports implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $delayFactor = 0;

        $this->query(function (GoogleBusinessProfile $googleBusinessProfile) use (&$delayFactor) {
            $job = new SyncGoogleBusinessProfileReport($googleBusinessProfile->id, $this->startDate, $this->endDate);

            dispatch($job->delay(Carbon::now()->addSeconds($delayFactor)));

            $delayFactor += 30;
        });
    }

    protected function query(Closure $closure): bool
    {
        return GoogleBusinessProfile::query()
            ->chunk(100, function (Collection $googleBusinessProfiles) use ($closure) {
                foreach ($googleBusinessProfiles as $googleBusinessProfile) {
                    $closure($googleBusinessProfile);
                }
            });
    }
}
