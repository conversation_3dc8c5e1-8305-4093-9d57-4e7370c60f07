<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Jobs;

use App\Domains\Sources\Localium\Actions\GetGoogleBusinessProfileReports;
use App\Domains\Sources\Localium\Models\GoogleBusinessProfile;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncGoogleBusinessProfileReport implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $googleBusinessProfileId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $googleBusinessProfile = GoogleBusinessProfile::query()
            ->findOrFail($this->googleBusinessProfileId);

        app(GetGoogleBusinessProfileReports::class)
            ->execute($googleBusinessProfile->localiumAccount, [$googleBusinessProfile->external_id], startDate: $this->startDate, endDate: $this->endDate);
    }
}
