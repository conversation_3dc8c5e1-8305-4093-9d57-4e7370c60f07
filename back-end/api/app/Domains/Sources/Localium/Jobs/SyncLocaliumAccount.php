<?php

declare(strict_types=1);

namespace App\Domains\Sources\Localium\Jobs;

use App\Domains\Sources\Localium\Actions\GetGoogleBusinessProfileLocations;
use App\Domains\Sources\Localium\Models\LocaliumAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLocaliumAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(
        protected int $localiumAccountId
    ) {}

    public function handle(): void
    {
        $localiumAccount = LocaliumAccount::findOrFail($this->localiumAccountId);
        app(GetGoogleBusinessProfileLocations::class)->execute($localiumAccount);
    }
}
