<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Commands\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Console\Command;

class SyncOwnedFacebookAdAccounts extends Command
{
    protected $signature = 'sources:meta:business:sync:ad-accounts {facebookBusinessId}';

    protected $description = 'Sync Facebook Ad Accounts for a Facebook Business';

    public function handle(): int
    {
        $facebookBusinessId = $this->argument('facebookBusinessId');

        /** @var FacebookBusiness|null $facebookBusiness */
        $facebookBusiness = FacebookBusiness::find($facebookBusinessId);

        if (! $facebookBusiness) {
            $this->error('Facebook Business not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Facebook Ad Accounts for Facebook Business #%s (%s)',
            $facebookBusiness->id,
            $facebookBusiness->name
        ));

        $this->callOwnedAction($facebookBusiness);
        $this->callAccessibleAction($facebookBusiness);

        $this->info('Facebook Business synced successfully');

        return 0;
    }

    protected function callOwnedAction(FacebookBusiness $facebookBusiness, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetOwnedFacebookAdAccounts::class)
            ->execute($facebookBusiness, $cursor);

        if ($nextPageCursor) {
            $this->callOwnedAction($facebookBusiness, $nextPageCursor);
        }
    }

    protected function callAccessibleAction(FacebookBusiness $facebookBusiness, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetAccessibleFacebookAdAccounts::class)
            ->execute($facebookBusiness, $cursor);

        if ($nextPageCursor) {
            $this->callAccessibleAction($facebookBusiness, $nextPageCursor);
        }
    }
}
