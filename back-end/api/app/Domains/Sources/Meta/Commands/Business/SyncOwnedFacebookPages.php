<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Commands\Business;

use App\Domains\Sources\Meta\Actions\Business\GetAccessibleFacebookPages;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Console\Command;

class SyncOwnedFacebookPages extends Command
{
    protected $signature = 'sources:meta:business:sync:pages {facebookBusinessId}';

    protected $description = 'Sync Facebook Pages for a Facebook Business';

    public function handle(): int
    {
        $facebookBusinessId = $this->argument('facebookBusinessId');

        /** @var FacebookBusiness|null $facebookBusiness */
        $facebookBusiness = FacebookBusiness::find($facebookBusinessId);

        if (! $facebookBusiness) {
            $this->error('Facebook Business not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Facebook Pages for Facebook Business #%s (%s)',
            $facebookBusiness->id,
            $facebookBusiness->name
        ));

        $this->callOwnedAction($facebookBusiness);
        $this->callAccessibleAction($facebookBusiness);

        $this->info('Facebook Business synced successfully');

        return 0;
    }

    protected function callOwnedAction(FacebookBusiness $facebookBusiness, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetOwnedFacebookPages::class)
            ->execute($facebookBusiness, $cursor);

        if ($nextPageCursor) {
            $this->callOwnedAction($facebookBusiness, $nextPageCursor);
        }
    }

    protected function callAccessibleAction(FacebookBusiness $facebookBusiness, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetAccessibleFacebookPages::class)
            ->execute($facebookBusiness, $cursor);

        if ($nextPageCursor) {
            $this->callAccessibleAction($facebookBusiness, $nextPageCursor);
        }
    }
}
