<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Commands\Insight;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookAdInsights;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Illuminate\Console\Command;

class SyncFacebookAdInsights extends Command
{
    protected $signature = 'sources:meta:insights:sync {facebookAdAccountId}';

    protected $description = 'Sync Facebook Ad Insights for a Facebook Ad Account';

    public function handle(): int
    {
        $facebookAdAccountId = $this->argument('facebookAdAccountId');

        /** @var FacebookAdAccount|null $facebookAdAccount */
        $facebookAdAccount = FacebookAdAccount::find($facebookAdAccountId);

        if (! $facebookAdAccount) {
            $this->error('Facebook Ad Account not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Facebook Ad Insights for Facebook Ad Account #%s (%s)',
            $facebookAdAccount->id,
            $facebookAdAccount->name
        ));

        $this->callAdAction($facebookAdAccount);
        $this->callCampaignAction($facebookAdAccount);

        $this->info('Facebook Ad Account synced successfully');

        return 0;
    }

    protected function callAdAction(FacebookAdAccount $facebookAdAccount, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::AD, $cursor);

        if ($nextPageCursor) {
            $this->callAdAction($facebookAdAccount, $nextPageCursor);
        }
    }

    protected function callCampaignAction(FacebookAdAccount $facebookAdAccount, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetFacebookAdInsights::class)
            ->execute($facebookAdAccount, Level::CAMPAIGN, $cursor);

        if ($nextPageCursor) {
            $this->callCampaignAction($facebookAdAccount, $nextPageCursor);
        }
    }
}
