<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Commands\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Console\Command;

class SyncPages extends Command
{
    protected $signature = 'sources:meta:me:sync:pages {facebookAccountId}';

    protected $description = 'Sync Facebook Pages for a Facebook Account';

    public function handle(): int
    {
        $facebookAccountId = $this->argument('facebookAccountId');

        /** @var FacebookAccount|null $facebookAccount */
        $facebookAccount = FacebookAccount::find($facebookAccountId);

        if (! $facebookAccount) {
            $this->error('Facebook Account not found');

            return 1;
        }

        $this->info(sprintf(
            'Syncing Facebook Pages for Facebook Account #%s (%s)',
            $facebookAccount->id,
            $facebookAccount->name
        ));

        $this->callAction($facebookAccount);

        $this->info('Facebook Account synced successfully');

        return 0;
    }

    protected function callAction(FacebookAccount $facebookAccount, ?string $cursor = null): void
    {
        $nextPageCursor = app(GetFacebookPages::class)
            ->execute($facebookAccount, $cursor);

        if ($nextPageCursor) {
            $this->callAction($facebookAccount, $nextPageCursor);
        }
    }
}
