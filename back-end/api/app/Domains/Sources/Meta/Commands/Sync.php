<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Commands;

use App\Domains\Sources\Meta\Jobs\SyncFacebookAccounts;
use App\Domains\Sources\Meta\Jobs\SyncFacebookBusinesses;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class Sync extends Command
{
    protected $signature = 'sources:meta:sync';

    protected $description = 'Sync all Facebook related connections';

    public function handle(): void
    {
        $this->info('Dispatching jobs to sync all Facebook connections');

        Bus::chain([
            new SyncFacebookAccounts,
            new SyncFacebookBusinesses,
        ])->dispatch();

        $this->info('Jobs dispatched successfully');
    }
}
