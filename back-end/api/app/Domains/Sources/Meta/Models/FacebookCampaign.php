<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FacebookCampaign extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    public function facebookAdAccount(): BelongsTo
    {
        return $this->belongsTo(facebookAdAccount::class);
    }

    public function facebookAds(): HasMany
    {
        return $this->hasMany(FacebookAd::class);
    }

    public function facebookCampaignInsights(): HasMany
    {
        return $this->hasMany(FacebookCampaignInsight::class);
    }
}
