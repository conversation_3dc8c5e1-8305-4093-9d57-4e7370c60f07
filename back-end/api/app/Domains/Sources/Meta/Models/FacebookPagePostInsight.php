<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class FacebookPagePostInsight extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function facebookPost(): BelongsTo
    {
        return $this->belongsTo(FacebookPagePost::class);
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: FacebookPagePost::class,
                firstKey: 'id',                         // Foreign key on FacebookPage table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'facebook_page_post_id',
                secondLocalKey: 'facebook_page_id'      // Local key on FacebookPagePost table
            )
            ->where('sourceable_type', FacebookPage::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
