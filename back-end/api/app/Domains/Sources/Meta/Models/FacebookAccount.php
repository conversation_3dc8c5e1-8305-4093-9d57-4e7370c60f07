<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FacebookAccount extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token_created_at' => 'datetime',
        ];
    }

    public function facebookPages(): HasMany
    {
        return $this->hasMany(FacebookPage::class);
    }

    public function facebookAdAccounts(): HasMany
    {
        return $this->hasMany(FacebookAdAccount::class);
    }

    public function facebookBusinesses(): HasMany
    {
        return $this->hasMany(FacebookBusiness::class);
    }
}
