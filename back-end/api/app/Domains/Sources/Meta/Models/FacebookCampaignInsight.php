<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class FacebookCampaignInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
            'action_types' => 'json',
        ];
    }

    public function facebookCampaign(): BelongsTo
    {
        return $this->belongsTo(facebookCampaign::class);
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: FacebookCampaign::class,
                firstKey: 'id',                         // Foreign key on FacebookCampaign table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'facebook_campaign_id',
                secondLocalKey: 'facebook_ad_account_id'  // Local key on FacebookCampaign table
            )
            ->where('sourceable_type', FacebookAdAccount::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
