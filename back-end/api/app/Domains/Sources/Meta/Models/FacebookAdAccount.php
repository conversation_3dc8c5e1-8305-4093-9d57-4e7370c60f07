<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class FacebookAdAccount extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'last_synced_at' => 'datetime',
        ];
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }

    public function facebookAccount(): BelongsTo
    {
        return $this->belongsTo(FacebookAccount::class);
    }

    public function facebookBusiness(): BelongsTo
    {
        return $this->belongsTo(FacebookBusiness::class);
    }

    public function facebookCampaigns(): HasMany
    {
        return $this->hasMany(FacebookCampaign::class);
    }

    public function facebookAds(): HasMany
    {
        return $this->hasMany(FacebookAd::class);
    }
}
