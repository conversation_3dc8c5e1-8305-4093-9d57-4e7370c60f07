<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class InstagramAccount extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'insights_last_synced_at' => 'datetime',
        ];
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }

    public function facebookPage(): BelongsTo
    {
        return $this->belongsTo(FacebookPage::class);
    }

    public function instagramAccountInsights(): HasMany
    {
        return $this->hasMany(InstagramAccountInsight::class);
    }

    public function instagramMediaItems(): HasMany
    {
        return $this->hasMany(InstagramMediaItem::class);
    }
}
