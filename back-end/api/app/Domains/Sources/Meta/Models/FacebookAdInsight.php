<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class FacebookAdInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'action_types' => 'json',
        ];
    }

    public function facebookAd(): BelongsTo
    {
        return $this->belongsTo(facebookAd::class);
    }
}
