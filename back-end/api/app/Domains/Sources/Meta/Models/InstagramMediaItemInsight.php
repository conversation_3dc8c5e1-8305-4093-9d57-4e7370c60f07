<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class InstagramMediaItemInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function instagramMediaItem(): BelongsTo
    {
        return $this->belongsTo(InstagramMediaItem::class);
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: InstagramMediaItem::class,
                firstKey: 'id',                         // Foreign key on InstagramMediaItem table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'instagram_media_item_id',
                secondLocalKey: 'instagram_account_id'  // Local key on InstagramMediaItem table
            )
            ->where('sourceable_type', InstagramAccount::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
