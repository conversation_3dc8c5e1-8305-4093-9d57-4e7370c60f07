<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FacebookPagePost extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
            'insights_last_synced_at' => 'datetime',
        ];
    }

    public function facebookPage(): BelongsTo
    {
        return $this->belongsTo(FacebookPage::class);
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'facebook_page_id', 'sourceable_id')
            ->where('sourceable_type', FacebookPage::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
