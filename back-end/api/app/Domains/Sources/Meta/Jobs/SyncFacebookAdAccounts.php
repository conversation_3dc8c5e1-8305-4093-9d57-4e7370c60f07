<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookAdInsight;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncFacebookAdAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (FacebookAdAccount $facebookAdAccount) {
            $syncFacebookAdInsightCampaignsJob = new SyncFacebookAdInsight($facebookAdAccount->id, Level::CAMPAIGN, startDate: $this->startDate, endDate: $this->endDate);
            dispatch($syncFacebookAdInsightCampaignsJob);
            $syncFacebookAdInsightAdsJob = new SyncFacebookAdInsight($facebookAdAccount->id, Level::AD, startDate: $this->startDate, endDate: $this->endDate);
            dispatch($syncFacebookAdInsightAdsJob);
        });
    }

    protected function query(Closure $closure): bool
    {
        return FacebookAdAccount::query()
            ->chunk(100, function (Collection $facebookAdAccounts) use ($closure) {
                foreach ($facebookAdAccounts as $facebookAdAccount) {
                    $closure($facebookAdAccount);
                }
            });
    }
}
