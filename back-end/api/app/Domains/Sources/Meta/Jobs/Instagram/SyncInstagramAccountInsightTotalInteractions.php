<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncInstagramAccountInsightTotalInteractions implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (InstagramAccountInsight $instagramAccountInsight) {
            $totalInteractions = InstagramMediaItemInsight::query()
                ->whereHas('instagramMediaItem', function ($query) use ($instagramAccountInsight) {
                    $query->where('instagram_account_id', $instagramAccountInsight->instagram_account_id);
                })
                ->where('date', $instagramAccountInsight->date->format('Y-m-d'))
                ->sum('total_interactions');

            $instagramAccountInsight->total_interactions = $totalInteractions;
            $instagramAccountInsight->save();
        });
    }

    protected function query(Closure $closure): bool
    {
        return InstagramAccountInsight::query()
            ->where('date', now()->startOfDay()->format('Y-m-d'))
            ->chunk(100, function (Collection $instagramAccountInsights) use ($closure) {
                foreach ($instagramAccountInsights as $instagramAccountInsight) {
                    $closure($instagramAccountInsight);
                }
            });
    }
}
