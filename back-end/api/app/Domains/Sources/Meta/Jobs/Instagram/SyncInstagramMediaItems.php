<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItems as Action;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncInstagramMediaItems implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $instagramAccountId, public ?string $cursor = null) {}

    public function handle(): void
    {
        $instagramAccount = InstagramAccount::query()
            ->findOrFail($this->instagramAccountId);

        $nextPageCursor = app(Action::class)->execute(
            instagramAccount: $instagramAccount,
            cursor: $this->cursor
        );

        if ($nextPageCursor) {
            $job = new self(
                instagramAccountId: $this->instagramAccountId,
                cursor: $nextPageCursor
            );
            dispatch($job);
        }

        if (! $nextPageCursor) {
            $this->dispatchInstagramMediaItemInsights($instagramAccount);
        }
    }

    protected function dispatchInstagramMediaItemInsights(InstagramAccount $instagramAccount): void
    {
        InstagramMediaItem::query()
            ->where('instagram_account_id', $instagramAccount->id)
            ->chunk(25, function (Collection $facebookAdAccounts) use ($instagramAccount) {
                $job = new SyncInstagramMediaItemInsights(
                    instagramAccountId: $instagramAccount->id,
                    instagramMediaItemIds: $facebookAdAccounts->pluck('id')->all()
                );
                dispatch($job);
            });
    }
}
