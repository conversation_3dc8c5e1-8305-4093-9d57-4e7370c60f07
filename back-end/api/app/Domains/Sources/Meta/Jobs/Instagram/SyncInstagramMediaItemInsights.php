<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Instagram;

use App\Domains\Sources\Meta\Actions\Instagram\GetInstagramMediaItemInsights;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncInstagramMediaItemInsights implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $instagramAccountId, public array $instagramMediaItemIds) {}

    public function handle(): void
    {
        $instagramAccount = InstagramAccount::findOrFail($this->instagramAccountId);
        $instagramMediaItems = InstagramMediaItem::query()
            ->whereIn('id', $this->instagramMediaItemIds)
            ->get()
            ->all();

        app(GetInstagramMediaItemInsights::class)->execute(
            instagramAccount: $instagramAccount,
            instagramMediaItems: $instagramMediaItems
        );
    }
}
