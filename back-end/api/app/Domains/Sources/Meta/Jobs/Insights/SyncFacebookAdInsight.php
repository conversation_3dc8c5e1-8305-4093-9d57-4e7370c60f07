<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookAdInsights as Action;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncFacebookAdInsight implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookAdAccountId, public Level $level, public ?string $cursor = null, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $facebookAdAccount = FacebookAdAccount::query()
            ->findOrFail($this->facebookAdAccountId);

        $nextPageCursor = app(Action::class)->execute(
            facebookAdAccount: $facebookAdAccount,
            level: $this->level,
            cursor: $this->cursor,
            startDate: $this->startDate,
            endDate: $this->endDate,
        );

        if ($nextPageCursor) {
            $job = new self(
                facebookAdAccountId: $this->facebookAdAccountId,
                level: $this->level,
                cursor: $nextPageCursor,
                startDate: $this->startDate,
                endDate: $this->endDate
            );
            dispatch($job);
        }
    }
}
