<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetInstagramAccountInsights as Action;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncInstagramAccountInsight implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $instagramAccountId, public ?string $cursor = null, private readonly ?CarbonInterface $date = null) {}

    public function handle(): void
    {
        $instagramAccount = InstagramAccount::query()
            ->findOrFail($this->instagramAccountId);

        $nextPageCursor = app(Action::class)->execute(
            instagramAccount: $instagramAccount,
            cursor: $this->cursor,
            date: $this->date,
        );

        if ($nextPageCursor) {
            $job = new self(
                instagramAccountId: $this->instagramAccountId,
                cursor: $nextPageCursor,
                date: $this->date,
            );
            dispatch($job);
        }
    }
}
