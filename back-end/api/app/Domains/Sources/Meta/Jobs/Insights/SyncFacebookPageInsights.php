<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Insights;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookPageInsights;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncFacebookPageInsights implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookPageId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $facebookPage = FacebookPage::query()
            ->findOrFail($this->facebookPageId);

        app(GetFacebookPageInsights::class)->execute(
            facebookPage: $facebookPage,
            startDate: $this->startDate,
            endDate: $this->endDate
        );
    }
}
