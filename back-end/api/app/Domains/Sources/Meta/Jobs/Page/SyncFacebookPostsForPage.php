<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Page;

use App\Domains\Sources\Meta\Actions\Page\GetFacebookPosts;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncFacebookPostsForPage implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookPageId) {}

    public function handle(): void
    {
        $facebookPage = FacebookPage::query()
            ->findOrFail($this->facebookPageId);

        app(GetFacebookPosts::class)->execute(
            facebookPage: $facebookPage,
        );
    }
}
