<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Page;

use App\Domains\Sources\Meta\Models\FacebookPagePost;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncFacebookPosts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (FacebookPagePost $facebookPagePost) {
            $syncFacebookPostInsights = new SyncFacebookPostInsights($facebookPagePost->id);
            dispatch($syncFacebookPostInsights);
        });
    }

    protected function query(Closure $closure): bool
    {
        return FacebookPagePost::query()
            ->chunk(100, function (Collection $facebookPosts) use ($closure) {
                foreach ($facebookPosts as $facebookPost) {
                    $closure($facebookPost);
                }
            });
    }
}
