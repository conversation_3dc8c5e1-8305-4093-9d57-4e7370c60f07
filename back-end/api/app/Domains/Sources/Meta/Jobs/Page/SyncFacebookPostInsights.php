<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Page;

use App\Domains\Sources\Meta\Actions\Insights\GetFacebookPostInsights;
use App\Domains\Sources\Meta\Models\FacebookPagePost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncFacebookPostInsights implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookPagePostId) {}

    public function handle(): void
    {
        $facebookPagePost = FacebookPagePost::query()
            ->findOrFail($this->facebookPagePostId);

        app(GetFacebookPostInsights::class)->execute(
            facebookPagePost: $facebookPagePost,
        );
    }
}
