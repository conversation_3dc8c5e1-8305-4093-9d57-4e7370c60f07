<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Me\GetFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookBusinesses;
use App\Domains\Sources\Meta\Jobs\Me\GetFacebookPages;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncFacebookAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookAccountId, public ?string $cursor = null) {}

    public function handle(): void
    {
        $getFacebookPagesJob = new GetFacebookPages(
            facebookAccountId: $this->facebookAccountId
        );
        dispatch($getFacebookPagesJob);

        $getFacebookAdAccountsJob = new GetFacebookAdAccounts(
            facebookAccountId: $this->facebookAccountId
        );
        dispatch($getFacebookAdAccountsJob);

        $getFacebookBusinessesJob = new GetFacebookBusinesses(
            facebookAccountId: $this->facebookAccountId
        );
        dispatch($getFacebookBusinessesJob);
    }
}
