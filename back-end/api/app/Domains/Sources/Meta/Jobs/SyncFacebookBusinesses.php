<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncAccessibleFacebookPages;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Jobs\Business\SyncOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncFacebookBusinesses implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (FacebookBusiness $facebookBusiness) {
            $syncOwnedFacebookPagesJob = new SyncOwnedFacebookPages($facebookBusiness->id);
            dispatch($syncOwnedFacebookPagesJob);
            $syncAccessibleFacebookPagesJob = new SyncAccessibleFacebookPages($facebookBusiness->id);
            dispatch($syncAccessibleFacebookPagesJob);

            $syncOwnedFacebookAdAccountsJob = new SyncOwnedFacebookAdAccounts($facebookBusiness->id);
            dispatch($syncOwnedFacebookAdAccountsJob);
            $syncAccessibleFacebookAdAccountsJob = new SyncAccessibleFacebookAdAccounts($facebookBusiness->id);
            dispatch($syncAccessibleFacebookAdAccountsJob);
        });
    }

    protected function query(Closure $closure): bool
    {
        return FacebookBusiness::query()
            ->chunk(100, function (Collection $facebookBusinesses) use ($closure) {
                foreach ($facebookBusinesses as $facebookBusiness) {
                    $closure($facebookBusiness);
                }
            });
    }
}
