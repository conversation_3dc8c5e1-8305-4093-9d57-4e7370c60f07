<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncFacebookPageInsights;
use App\Domains\Sources\Meta\Jobs\Page\SyncFacebookPostsForPage;
use App\Domains\Sources\Meta\Models\FacebookPage;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncFacebookPages implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (FacebookPage $facebookPage) {
            $syncFacebookPageInsights = new SyncFacebookPageInsights($facebookPage->id, $this->startDate, $this->endDate);
            dispatch($syncFacebookPageInsights);

            $syncFacebookPagePosts = new SyncFacebookPostsForPage($facebookPage->id);
            dispatch($syncFacebookPagePosts);
        });
    }

    protected function query(Closure $closure): bool
    {
        return FacebookPage::query()
            ->chunk(100, function (Collection $facebookPages) use ($closure) {
                foreach ($facebookPages as $facebookPage) {
                    $closure($facebookPage);
                }
            });
    }
}
