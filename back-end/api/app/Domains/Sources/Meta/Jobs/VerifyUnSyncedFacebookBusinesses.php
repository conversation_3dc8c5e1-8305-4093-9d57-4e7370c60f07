<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Support\Mails\Me\FacebookBusinessIsUnSyncedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class VerifyUnSyncedFacebookBusinesses implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        FacebookBusiness::query()
            ->where('last_synced_at', '<', now()->subDay())
            ->chunk(100, function (Collection $facebookBusinesses) {
                foreach ($facebookBusinesses as $facebookBusiness) {
                    $mail = new FacebookBusinessIsUnSyncedMail($facebookBusiness->id);
                    Mail::queue($mail);
                }
            });
    }
}
