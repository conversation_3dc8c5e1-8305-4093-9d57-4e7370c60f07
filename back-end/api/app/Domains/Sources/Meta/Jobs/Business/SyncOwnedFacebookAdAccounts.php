<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Business;

use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookAdAccounts;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncOwnedFacebookAdAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookBusinessId, public ?string $cursor = null) {}

    public function handle(): void
    {
        $facebookBusiness = FacebookBusiness::query()
            ->findOrFail($this->facebookBusinessId);

        $this->syncFacebookAdAccounts($facebookBusiness);
    }

    public function syncFacebookAdAccounts(FacebookBusiness $facebookBusiness): void
    {
        $nextPageCursor = app(GetOwnedFacebookAdAccounts::class)->execute(
            facebookBusiness: $facebookBusiness,
            cursor: $this->cursor
        );

        if ($nextPageCursor) {
            $job = new self(
                facebookBusinessId: $this->facebookBusinessId,
                cursor: $nextPageCursor
            );
            dispatch($job);
        }
    }
}
