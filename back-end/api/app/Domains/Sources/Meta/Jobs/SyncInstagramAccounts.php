<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Jobs\Insights\SyncInstagramAccountInsight;
use App\Domains\Sources\Meta\Jobs\Instagram\SyncInstagramMediaItems;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncInstagramAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $date = null) {}

    public function handle(): void
    {
        $this->query(function (InstagramAccount $instagramAccount) {
            $syncInstagramAccountInsightJob = new SyncInstagramAccountInsight($instagramAccount->id, date: $this->date);
            dispatch($syncInstagramAccountInsightJob);

            $syncInstagramMediaItemsJob = new SyncInstagramMediaItems($instagramAccount->id);
            dispatch($syncInstagramMediaItemsJob);
        });
    }

    protected function query(Closure $closure): bool
    {
        return InstagramAccount::query()
            ->chunk(100, function (Collection $instagramAccounts) use ($closure) {
                foreach ($instagramAccounts as $instagramAccount) {
                    $closure($instagramAccount);
                }
            });
    }
}
