<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncFacebookAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (FacebookAccount $facebookAccount) {
            $job = new SyncFacebookAccount($facebookAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return FacebookAccount::query()
            ->chunk(100, function (Collection $facebookAccounts) use ($closure) {
                foreach ($facebookAccounts as $facebookAccount) {
                    $closure($facebookAccount);
                }
            });
    }
}
