<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Jobs\Me;

use App\Domains\Sources\Meta\Actions\Me\GetFacebookAdAccounts as Action;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GetFacebookAdAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $facebookAccountId, public ?string $cursor = null) {}

    public function handle(): void
    {
        $facebookAccount = FacebookAccount::query()
            ->findOrFail($this->facebookAccountId);

        $nextPageCursor = app(Action::class)->execute(
            facebookAccount: $facebookAccount,
            cursor: $this->cursor
        );

        if ($nextPageCursor) {
            $job = new self(
                facebookAccountId: $this->facebookAccountId,
                cursor: $nextPageCursor
            );
            dispatch($job);
        }
    }
}
