<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use App\Domains\Sources\Meta\Support\Exceptions\Business\CannotGetFacebookPagesForFacebookBusiness;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetOwnedFacebookPages
{
    /**
     * @throws CannotGetFacebookPagesForFacebookBusiness
     * @throws ConnectionException
     */
    public function execute(FacebookBusiness $facebookBusiness, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookBusiness, $cursor);
        $pages = $response->json('data', []);

        $this->processRows(
            facebookBusiness: $facebookBusiness,
            pages: $pages
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookBusiness $facebookBusiness, array $pages): void
    {
        foreach ($pages as $page) {
            $this->processRow($facebookBusiness, $page);
        }
    }

    protected function processRow(FacebookBusiness $facebookBusiness, array $page): void
    {
        $accessToken = $page['access_token'] ?? null;

        $facebookPage = FacebookPage::query()->withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate(
            [
                'facebook_business_id' => $facebookBusiness->id,
                'external_id' => $page['id'],
            ],
            [
                'name' => $page['name'],
                'token' => $accessToken,
                'token_created_at' => $accessToken ? now() : null,
                'last_synced_at' => now(),
            ]
        );

        self::processInstagramAccount(
            facebookPage: $facebookPage,
            page: $page
        );
    }

    public static function processInstagramAccount(FacebookPage $facebookPage, array $page): void
    {
        $instagramBusinessAccount = $page['instagram_business_account'] ?? null;

        if (! $instagramBusinessAccount) {
            $facebookPage->instagramAccount()->delete();

            return;
        }

        $instagramAccount = InstagramAccount::query()->withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate([
            'facebook_page_id' => $facebookPage->id,
            'external_id' => $instagramBusinessAccount['id'],
        ], [
            'username' => $instagramBusinessAccount['username'],
            'name' => $instagramBusinessAccount['name'] ?? null,
            'follower_count' => $instagramBusinessAccount['followers_count'],
        ]);

        self::processInstagramAccountInsights($instagramAccount);
    }

    public static function processInstagramAccountInsights(InstagramAccount $instagramAccount): void
    {
        if ($instagramAccount->follower_count <= 0) {
            $instagramAccount->follower_count = self::getMostRecentFollowerCount($instagramAccount);
            $instagramAccount->save();
        }

        InstagramAccountInsight::query()->updateOrCreate([
            'instagram_account_id' => $instagramAccount->id,
            'date' => now()->format('Y-m-d'),
        ], [
            'followers' => $instagramAccount->follower_count,
        ]);
    }

    /**
     * @throws CannotGetFacebookPagesForFacebookBusiness
     * @throws ConnectionException
     */
    protected function getResponse(FacebookBusiness $facebookBusiness, ?string $cursor): Response
    {
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookBusiness->facebookAccount);

        if (! $token) {
            throw CannotGetFacebookPagesForFacebookBusiness::becauseOfMissingToken($facebookBusiness);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'fields' => implode(',', [
                'access_token',
                'name',
                'instagram_business_account{followers_count,name,username}',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl($facebookBusiness),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetFacebookPagesForFacebookBusiness::becauseOfHttpErrorWithStatusCode(
                facebookBusiness: $facebookBusiness,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/owned_pages', $facebookBusiness->external_id);
    }

    private static function getMostRecentFollowerCount(InstagramAccount $account): int
    {
        return InstagramAccountInsight::query()
            ->where('instagram_account_id', $account->id)
            ->where('followers', '>', 0)
            ->orderBy('date', 'DESC')
            ->first()
            ?->followers ?? 0;
    }
}
