<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Models\FacebookBusiness;

class GetAccessibleFacebookAdAccounts extends GetOwnedFacebookAdAccounts
{
    public function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/client_ad_accounts', $facebookBusiness->external_id);
    }
}
