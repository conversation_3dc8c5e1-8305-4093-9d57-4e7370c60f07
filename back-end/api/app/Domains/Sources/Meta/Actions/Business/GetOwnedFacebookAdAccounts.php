<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Business;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Support\Exceptions\Business\CannotGetFacebookAdAccountsForFacebookBusiness;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetOwnedFacebookAdAccounts
{
    /**
     * @throws CannotGetFacebookAdAccountsForFacebookBusiness
     * @throws ConnectionException
     */
    public function execute(FacebookBusiness $facebookBusiness, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookBusiness, $cursor);
        $pages = $response->json('data', []);

        $this->processRows(
            facebookBusiness: $facebookBusiness,
            pages: $pages
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookBusiness $facebookBusiness, array $pages): void
    {
        foreach ($pages as $page) {
            $this->processRow($facebookBusiness, $page);
        }
    }

    protected function processRow(FacebookBusiness $facebookBusiness, array $page): void
    {
        FacebookAdAccount::withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate(
            [
                'facebook_business_id' => $facebookBusiness->id,
                'external_id' => $page['id'],
            ],
            [
                'name' => $page['name'],
                'last_synced_at' => now(),
            ]
        );
    }

    /**
     * @throws CannotGetFacebookAdAccountsForFacebookBusiness
     * @throws ConnectionException
     */
    protected function getResponse(FacebookBusiness $facebookBusiness, ?string $cursor): Response
    {
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookBusiness->facebookAccount);

        if (! $token) {
            throw CannotGetFacebookAdAccountsForFacebookBusiness::becauseOfMissingToken($facebookBusiness);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'fields' => implode(',', [
                'name',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl($facebookBusiness),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetFacebookAdAccountsForFacebookBusiness::becauseOfHttpErrorWithStatusCode(
                facebookBusiness: $facebookBusiness,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(FacebookBusiness $facebookBusiness): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/owned_ad_accounts', $facebookBusiness->external_id);
    }
}
