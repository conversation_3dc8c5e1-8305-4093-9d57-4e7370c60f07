<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Models\FacebookPagePost;
use App\Domains\Sources\Meta\Models\FacebookPagePostInsight;
use App\Domains\Sources\Meta\Support\Exceptions\Insights\CannotGetPageInsightsForFacebookPosts;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class GetFacebookPostInsights
{
    /**
     * @throws CannotGetPageInsightsForFacebookPosts
     */
    public function execute(FacebookPagePost $facebookPagePost): void
    {
        $response = $this->getResponse($facebookPagePost);

        if (! $response) {
            return;
        }

        $dataPoints = $response->json('data', []);

        $facebookPagePost->insights_last_synced_at = now();
        $facebookPagePost->save();

        $this->processDataPoints(
            facebookPagePost: $facebookPagePost,
            dataPoints: $dataPoints
        );
    }

    protected function processDataPoints(FacebookPagePost $facebookPagePost, array $dataPoints): void
    {
        foreach ($dataPoints as $dataPoint) {
            $this->processDataPoint(
                facebookPagePost: $facebookPagePost,
                dataPoint: $dataPoint
            );
        }
    }

    protected function processDataPoint(FacebookPagePost $facebookPagePost, array $dataPoint): void
    {
        $currentTotal = $this->getMetricTotal($facebookPagePost);

        foreach (($dataPoint['values'] ?? []) as $value) {
            FacebookPagePostInsight::query()->updateOrCreate([
                'facebook_page_post_id' => $facebookPagePost->id,
                'date' => now()->startOfDay(),
            ], [
                $dataPoint['name'] => $value['value'] - ($currentTotal[$dataPoint['name']] ?? 0),
            ]);
        }
    }

    protected function getMetricTotal(FacebookPagePost $facebookPagePost): ?array
    {
        $totals = FacebookPagePostInsight::query()
            ->where('facebook_page_post_id', $facebookPagePost->id)
            ->where('date', '<', now()->format('Y-m-d'))
            ->addSelect(DB::raw('SUM(post_clicks) as post_clicks'))
            ->addSelect(DB::raw('SUM(post_impressions_unique) as post_impressions_unique'))
            ->groupBy([
                'facebook_page_post_id',
            ])
            ->first();

        if (! $totals) {
            return null;
        }

        return [
            'post_clicks' => (int) ($totals['post_clicks'] ?? 0),
            'post_impressions_unique' => (int) ($totals['post_impressions_unique'] ?? 0),
        ];
    }

    /**
     * @throws CannotGetPageInsightsForFacebookPosts
     */
    protected function getResponse(FacebookPagePost $facebookPagePost): ?Response
    {

        if (! $facebookPagePost->facebookPage->token) {
            return null;
        }

        $parameters = [
            'access_token' => $facebookPagePost->facebookPage->token,
            'metric' => implode(',', [
                'post_clicks',
                'post_impressions_unique',
            ]),
        ];

        $response = Http::get(
            url: $this->getUrl($facebookPagePost),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetPageInsightsForFacebookPosts::becauseOfHttpErrorWithStatusCode(
                facebookPagePost: $facebookPagePost,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(FacebookPagePost $facebookPagePost): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights', $facebookPagePost->external_id);
    }
}
