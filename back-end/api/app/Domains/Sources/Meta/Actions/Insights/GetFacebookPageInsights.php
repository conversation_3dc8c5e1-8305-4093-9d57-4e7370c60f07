<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPageInsight;
use App\Domains\Sources\Meta\Support\Exceptions\Insights\CannotGetPageInsightsForFacebookPages;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class GetFacebookPageInsights
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    /**
     * @throws CannotGetPageInsightsForFacebookPages
     */
    public function execute(FacebookPage $facebookPage, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): void
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($facebookPage);

        if (! $response) {
            return;
        }

        $dataPoints = $response->json('data', []);

        $facebookPage->insights_last_synced_at = now();
        $facebookPage->save();

        $this->processDataPoints(
            facebookPage: $facebookPage,
            dataPoints: $dataPoints
        );
    }

    protected function processDataPoints(FacebookPage $facebookPage, array $dataPoints): void
    {
        foreach ($dataPoints as $dataPoint) {
            $this->processDataPoint(
                facebookPage: $facebookPage,
                dataPoint: $dataPoint
            );
        }
    }

    protected function processDataPoint(FacebookPage $facebookPage, array $dataPoint): void
    {
        foreach (($dataPoint['values'] ?? []) as $value) {
            FacebookPageInsight::query()->updateOrCreate([
                'facebook_page_id' => $facebookPage->id,
                'date' => Carbon::parse($value['end_time']),
            ], [
                $dataPoint['name'] => $value['value'],
            ]);
        }
    }

    /**
     * @throws CannotGetPageInsightsForFacebookPages
     */
    protected function getResponse(FacebookPage $facebookPage): ?Response
    {
        if (! $facebookPage->token) {
            return null;
        }

        $parameters = [
            'access_token' => $facebookPage->token,
            'period' => 'day',
            'metric' => implode(',', [
                'page_post_engagements',
                'page_impressions_unique',
                'page_fans',
                'page_fan_adds',
                'page_fan_removes',
            ]),
        ];

        if ((bool) $this->startDate && (bool) $this->endDate) {
            $parameters['since'] = $this->startDate->toDateTime();
            $parameters['until'] = $this->endDate->toDateTime();
        } else {
            $parameters['date_preset'] = 'last_30d';
        }

        $response = Http::get(
            url: $this->getUrl($facebookPage),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetPageInsightsForFacebookPages::becauseOfHttpErrorWithStatusCode(
                facebookPage: $facebookPage,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(FacebookPage $facebookPage): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights', $facebookPage->external_id);
    }
}
