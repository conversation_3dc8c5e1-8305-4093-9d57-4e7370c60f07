<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Me;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookBusiness;
use App\Domains\Sources\Meta\Support\Exceptions\Me\CannotGetFacebookBusinessesForFacebookAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetFacebookBusinesses
{
    /**
     * @throws CannotGetFacebookBusinessesForFacebookAccount
     * @throws ConnectionException
     */
    public function execute(FacebookAccount $facebookAccount, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookAccount, $cursor);
        $businesses = $response->json('data', []);

        $this->processRows(
            facebookAccount: $facebookAccount,
            businesses: $businesses
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookAccount $facebookAccount, array $businesses): void
    {
        foreach ($businesses as $business) {
            $this->processRow($facebookAccount, $business);
        }
    }

    protected function processRow(FacebookAccount $facebookAccount, array $business): void
    {
        FacebookBusiness::updateOrCreate(
            [
                'facebook_account_id' => $facebookAccount->id,
                'external_id' => $business['id'],
            ],
            [
                'name' => $business['name'],
                'last_synced_at' => now(),
            ]
        );
    }

    /**
     * @throws CannotGetFacebookBusinessesForFacebookAccount
     * @throws ConnectionException
     */
    protected function getResponse(FacebookAccount $facebookAccount, ?string $cursor): Response
    {
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);

        if (! $token) {
            throw CannotGetFacebookBusinessesForFacebookAccount::becauseOfMissingToken($facebookAccount);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: 'https://graph.facebook.com/v23.0/me/businesses',
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetFacebookBusinessesForFacebookAccount::becauseOfHttpErrorWithStatusCode(
                facebookAccount: $facebookAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
