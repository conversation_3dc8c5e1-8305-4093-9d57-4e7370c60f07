<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Me;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Actions\Business\GetOwnedFacebookPages;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Support\Exceptions\Me\CannotGetAccountsForFacebookAccounts;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetFacebookPages
{
    /**
     * @throws CannotGetAccountsForFacebookAccounts
     * @throws ConnectionException
     */
    public function execute(FacebookAccount $facebookAccount, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookAccount, $cursor);
        $pages = $response->json('data', []);

        $this->processRows(
            facebookAccount: $facebookAccount,
            pages: $pages
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookAccount $facebookAccount, array $pages): void
    {
        foreach ($pages as $business) {
            $this->processRow($facebookAccount, $business);
        }
    }

    protected function processRow(FacebookAccount $facebookAccount, array $page): void
    {
        $accessToken = $page['access_token'] ?? null;

        $facebookPage = FacebookPage::query()->withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate(
            [
                'facebook_account_id' => $facebookAccount->id,
                'external_id' => $page['id'],
            ],
            [
                'name' => $page['name'],
                'token' => $accessToken,
                'token_created_at' => $accessToken ? now() : null,
                'last_synced_at' => now(),
            ]
        );

        GetOwnedFacebookPages::processInstagramAccount(
            facebookPage: $facebookPage,
            page: $page
        );
    }

    /**
     * @throws CannotGetAccountsForFacebookAccounts
     * @throws ConnectionException
     */
    protected function getResponse(FacebookAccount $facebookAccount, ?string $cursor): Response
    {
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);

        if (! $token) {
            throw CannotGetAccountsForFacebookAccounts::becauseOfMissingToken($facebookAccount);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'fields' => implode(',', [
                'access_token',
                'name',
                'instagram_business_account{followers_count,name,username}',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: 'https://graph.facebook.com/v23.0/me/accounts',
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetAccountsForFacebookAccounts::becauseOfHttpErrorWithStatusCode(
                facebookAccount: $facebookAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
