<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Authentication;

use Lara<PERSON>\Socialite\Facades\Socialite;
use <PERSON>vel\Socialite\Two\FacebookProvider;

class GenerateOAuthRedirectLink
{
    private string $redirectUrl;

    public function execute(string $redirectUrl): string
    {
        $this->redirectUrl = $redirectUrl;

        return $this
            ->getFacebookProvider()
            ->redirect()
            ->getTargetUrl();
    }

    protected function getFacebookProvider(): FacebookProvider
    {
        /** @var FacebookProvider $provider */
        $provider = Socialite::driver('facebook');

        $provider->with(['config_id' => config('services.facebook.config_id')]);
        $provider->redirectUrl($this->redirectUrl);
        $provider->stateless();

        return $provider;
    }
}
