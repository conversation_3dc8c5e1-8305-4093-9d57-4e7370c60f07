<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Authentication;

use App\Domains\Slack\Messages\NewMetaAccountConnectedMessage;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Support\SlackNotification;
use Laravel\Socialite\Two\User;

class UpdateFacebookAccountBySocialiteUser
{
    public function execute(User $user): FacebookAccount
    {
        $facebookAccount = FacebookAccount::where('external_id', $user->getId())->first();

        if (! $facebookAccount) {
            $facebookAccount = new FacebookAccount;
        }

        $facebookAccount->external_id = $user->getId();
        $facebookAccount->email = $user->getEmail();
        $facebookAccount->name = $user->getName();
        $facebookAccount->image_url = $user->getAvatar();
        $facebookAccount->token = $user->token;
        $facebookAccount->token_created_at = now();

        $facebookAccount->save();

        if ($facebookAccount->wasRecentlyCreated) {
            SlackNotification::send(new NewMetaAccountConnectedMessage($facebookAccount));
        }

        return $facebookAccount;
    }
}
