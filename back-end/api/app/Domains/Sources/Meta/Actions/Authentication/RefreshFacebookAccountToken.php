<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Authentication;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class RefreshFacebookAccountToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(FacebookAccount $facebookAccount): ?string
    {
        if (! $this->shouldRefreshToken($facebookAccount)) {
            return $facebookAccount->token;
        }

        $token = $this->refreshToken($facebookAccount);

        if (! $token) {
            return $facebookAccount->token;
        }

        $facebookAccount->token = $token;
        $facebookAccount->token_created_at = $token ? now() : null;

        $facebookAccount->save();

        return $token;
    }

    protected function shouldRefreshToken(FacebookAccount $facebookAccount): bool
    {
        if (! $facebookAccount->token_created_at || ! $facebookAccount->token) {
            return true;
        }

        $expiryThreshold = now()->subMonth();

        return $facebookAccount->token_created_at->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function refreshToken(FacebookAccount $facebookAccount): ?string
    {
        $response = Http::asForm()
            ->connectTimeout(10)
            ->post(
                url: 'https://graph.facebook.com/v23.0/oauth/access_token',
                data: [
                    'client_id' => config('services.facebook.client_id'),
                    'client_secret' => config('services.facebook.client_secret'),
                    'grant_type' => 'fb_exchange_token',
                    'fb_exchange_token' => $facebookAccount->token,
                ]
            );

        if (! $response->successful()) {
            return null;
        }

        return $response->json('access_token');
    }
}
