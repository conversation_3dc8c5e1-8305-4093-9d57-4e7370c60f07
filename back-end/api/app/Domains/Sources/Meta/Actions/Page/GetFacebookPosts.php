<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Page;

use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\FacebookPagePost;
use App\Domains\Sources\Meta\Support\Exceptions\Page\CannotGetPostsForFacebookPage;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetFacebookPosts
{
    /**
     * @throws CannotGetPostsForFacebookPage
     * @throws ConnectionException
     */
    public function execute(FacebookPage $facebookPage, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookPage, $cursor);
        $posts = $response->json('data', []);

        $this->processRows(
            facebookPage: $facebookPage,
            posts: $posts
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookPage $facebookPage, array $posts): void
    {
        foreach ($posts as $post) {
            $this->processRow($facebookPage, $post);
        }
    }

    protected function processRow(FacebookPage $facebookPage, array $post): void
    {
        FacebookPagePost::query()->updateOrCreate(
            [
                'facebook_page_id' => $facebookPage->id,
                'external_id' => $post['id'],
            ],
            [
                'message' => $post['message'] ?? null,
                'date' => isset($post['created_time']) ? now()->parse($post['created_time']) : null,
                'url' => $post['permalink_url'] ?? null,
                'media_url' => $post['full_picture'] ?? null,
                'is_hidden' => $post['is_hidden'] ?? false,
                'is_published' => $post['is_published'] ?? false,
            ]
        );
    }

    /**
     * @throws CannotGetPostsForFacebookPage
     * @throws ConnectionException
     */
    protected function getResponse(FacebookPage $facebookPage, ?string $cursor): Response
    {
        $token = $facebookPage->token;

        if (! $token) {
            throw CannotGetPostsForFacebookPage::becauseOfMissingToken($facebookPage);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'fields' => implode(',', [
                'id',
                'message',
                'created_time',
                'full_picture',
                'permalink_url',
                'is_hidden',
                'is_published',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: sprintf('https://graph.facebook.com/v23.0/%s/posts', $facebookPage->external_id),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetPostsForFacebookPage::becauseOfHttpErrorWithStatusCode(
                facebookPage: $facebookPage,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
