<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Insights;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use Exception;

class CannotGetInsightsForInstagramAccount extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(InstagramAccount $instagramAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get insights for Instagram Account #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $instagramAccount->id,
            $statusCode,
            $body
        ));
    }
}
