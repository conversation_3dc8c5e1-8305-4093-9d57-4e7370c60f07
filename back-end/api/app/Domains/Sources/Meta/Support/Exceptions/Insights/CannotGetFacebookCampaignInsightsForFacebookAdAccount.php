<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Insights;

use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use Exception;

class CannotGetFacebookCampaignInsightsForFacebookAdAccount extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(FacebookAdAccount $facebookAdAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get insights for Facebook Ad Account #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $facebookAdAccount->id,
            $statusCode,
            $body
        ));
    }

    public static function becauseOfMissingToken(FacebookAdAccount $facebookAdAccount): self
    {
        return new self(sprintf(
            'Cannot get insights for Facebook Ad Account #%s. Could not retrieve token.',
            $facebookAdAccount->id
        ));
    }
}
