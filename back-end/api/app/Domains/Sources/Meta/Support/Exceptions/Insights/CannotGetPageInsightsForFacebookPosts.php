<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Insights;

use App\Domains\Sources\Meta\Models\FacebookPagePost;
use Exception;

class CannotGetPageInsightsForFacebookPosts extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(FacebookPagePost $facebookPagePost, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get insights for Facebook Post #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $facebookPagePost->id,
            $statusCode,
            $body
        ));
    }
}
