<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Me;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Exception;

class CannotGetAccountsForFacebookAccounts extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(FacebookAccount $facebookAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get pages for Facebook Account #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $facebookAccount->id,
            $statusCode,
            $body
        ));
    }

    public static function becauseOfMissingToken(FacebookAccount $facebookAccount): self
    {
        return new self(sprintf(
            'Cannot get pages for Facebook Account #%s. Could not retrieve token.',
            $facebookAccount->id
        ));
    }
}
