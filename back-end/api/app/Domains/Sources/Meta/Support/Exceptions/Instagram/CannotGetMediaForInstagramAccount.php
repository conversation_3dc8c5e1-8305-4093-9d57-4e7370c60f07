<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Instagram;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use Exception;

class CannotGetMediaForInstagramAccount extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(InstagramAccount $instagramAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get media items for Instagram Account #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $instagramAccount->id,
            $statusCode,
            $body
        ));
    }
}
