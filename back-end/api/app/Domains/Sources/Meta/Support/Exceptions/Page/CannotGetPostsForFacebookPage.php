<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Page;

use App\Domains\Sources\Meta\Models\FacebookPage;
use Exception;

class CannotGetPostsForFacebookPage extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(FacebookPage $facebookPage, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get posts for Facebook Page #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $facebookPage->id,
            $statusCode,
            $body
        ));
    }

    public static function becauseOfMissingToken(FacebookPage $facebookPage): self
    {
        return new self(sprintf(
            'Cannot get posts for Facebook Page #%s. Could not retrieve token.',
            $facebookPage->id
        ));
    }
}
