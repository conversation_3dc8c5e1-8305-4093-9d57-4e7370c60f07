<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Exceptions\Business;

use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Exception;

class CannotGetFacebookAdAccountsForFacebookBusiness extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(FacebookBusiness $facebookBusiness, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get ad accounts for Facebook Business #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $facebookBusiness->id,
            $statusCode,
            $body
        ));
    }

    public static function becauseOfMissingToken(FacebookBusiness $facebookBusiness): self
    {
        return new self(sprintf(
            'Cannot get ad accounts for Facebook Business #%s. Could not retrieve token.',
            $facebookBusiness->id
        ));
    }
}
