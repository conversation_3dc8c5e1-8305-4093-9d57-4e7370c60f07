<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class FacebookAdAccountIsUnSyncedMail extends Mailable
{
    public function __construct(public int $facebookAdAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Facebook AdAccount #%s is not being synchronised', $this->facebookAdAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.facebook.business.facebook-ad-account-is-un-synced-mail',
            with: [
                'facebookAdAccount' => FacebookAdAccount::query()->findOrFail($this->facebookAdAccountId),
            ],
        );
    }
}
