<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Mails\Me;

use App\Domains\Sources\Meta\Models\FacebookBusiness;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class FacebookBusinessIsUnSyncedMail extends Mailable
{
    public function __construct(public int $facebookBusinessId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Facebook Business #%s is not being synchronised', $this->facebookBusinessId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.facebook.me.facebook-business-is-un-synced-mail',
            with: [
                'facebookBusiness' => FacebookBusiness::query()->findOrFail($this->facebookBusinessId),
            ],
        );
    }
}
