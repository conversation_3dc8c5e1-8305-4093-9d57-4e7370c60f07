<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class InstagramAccountInsightsIsUnSyncedMail extends Mailable
{
    public function __construct(public int $instagramAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Instagram Account #%s insights is not being synchronised', $this->instagramAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.facebook.business.instagram-account-insights-is-un-synced-mail',
            with: [
                'instagramAccount' => InstagramAccount::query()->findOrFail($this->instagramAccountId),
            ],
        );
    }
}
