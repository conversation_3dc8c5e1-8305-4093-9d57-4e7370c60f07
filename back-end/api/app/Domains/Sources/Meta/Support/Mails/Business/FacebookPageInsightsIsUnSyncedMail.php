<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Support\Mails\Business;

use App\Domains\Sources\Meta\Models\FacebookPage;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class FacebookPageInsightsIsUnSyncedMail extends Mailable
{
    public function __construct(public int $facebookPageId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Facebook Page #%s is not having it Insights being synchronised', $this->facebookPageId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.facebook.business.facebook-page-insights-is-un-synced-mail',
            with: [
                'facebookPage' => FacebookPage::query()->findOrFail($this->facebookPageId),
            ],
        );
    }
}
