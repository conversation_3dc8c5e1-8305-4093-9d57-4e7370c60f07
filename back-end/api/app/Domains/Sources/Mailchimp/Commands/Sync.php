<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Commands;

use App\Domains\Sources\Mailchimp\Jobs\SyncMailchimpAccounts;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class Sync extends Command
{
    protected $signature = 'sources:mailchimp:sync';

    protected $description = 'Sync all Mailchimp related connections';

    public function handle(): void
    {
        $this->info('Dispatching jobs to sync all Mailchimp connections');

        Bus::chain([
            new SyncMailchimpAccounts,
        ])->dispatch();

        $this->info('Jobs dispatched successfully');
    }
}
