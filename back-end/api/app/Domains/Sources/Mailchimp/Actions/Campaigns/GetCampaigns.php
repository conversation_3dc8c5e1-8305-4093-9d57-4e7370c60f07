<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Actions\Campaigns;

use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Support\Enums\Campaign\Status;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpCampaignException;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class GetCampaigns
{
    public const PER_PAGE = 1000;

    /**
     * @throws CannotSyncMailchimpCampaignException|ConnectionException
     */
    public function execute(MailchimpAccount $mailchimpAccount, int $page = 1, ?CarbonInterface $startDate = null): array
    {
        $endpoint = sprintf(
            app(GetBaseEndpoint::class)->execute($mailchimpAccount),
            'campaigns'
        );

        $response = Http::withBasicAuth('user', $mailchimpAccount->api_key)
            ->get($endpoint, [
                'offset' => ($page - 1) * self::PER_PAGE,
                'count' => self::PER_PAGE,
                'status' => 'sent',
                'since_send_time' => ($startDate ?? now()->subMonths(12))->startOfDay()->toIso8601String(),
            ]);

        if (! $response->successful()) {
            throw CannotSyncMailchimpCampaignException::becauseOfHttpErrorWithStatusCode(
                $mailchimpAccount,
                $response->status(),
                $response->body()
            );
        }

        $body = $response->json();

        return [
            'current_page' => $page,
            'total_pages' => $this->getTotalPages($body),
            'items' => array_map(function (array $item) {
                return [
                    'id' => $item['id'],
                    'status' => Status::from($item['status']),
                    'send_time' => $item['send_time'],
                    'title' => $item['settings']['title'],
                    'recipients' => [
                        'list_id' => $item['recipients']['list_id'],
                        'list_is_active' => $item['recipients']['list_is_active'],
                        'list_name' => $item['recipients']['list_name'],
                    ],
                ];
            }, $body['campaigns']),
        ];
    }

    protected function getTotalPages(array $body): int
    {
        $totalItems = $body['total_items'];

        return $totalItems <= self::PER_PAGE
            ? 1
            : (int) ceil($totalItems / self::PER_PAGE);
    }
}
