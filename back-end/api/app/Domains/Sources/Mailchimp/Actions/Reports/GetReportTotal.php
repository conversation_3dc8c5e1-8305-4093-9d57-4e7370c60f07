<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Actions\Reports;

use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Support\Facades\DB;

class GetReportTotal
{
    public function execute(MailchimpCampaign $mailchimpCampaign): ?array
    {
        $totals = MailchimpReport::query()
            ->where('mailchimp_campaign_id', $mailchimpCampaign->id)
            ->where('date', '<', now()->format('Y-m-d'))
            ->addSelect(DB::raw('SUM(emails_sent) as emails_sent'))
            ->addSelect(DB::raw('SUM(clicks_total) as clicks_total'))
            ->addSelect(DB::raw('SUM(unique_clicks) as unique_clicks'))
            ->addSelect(DB::raw('SUM(opens_total) as opens_total'))
            ->addSelect(DB::raw('SUM(unique_opens) as unique_opens'))
            ->groupBy([
                'mailchimp_campaign_id',
            ])
            ->first();

        if (! $totals) {
            return null;
        }

        return [
            'emails_sent' => (int) ($totals['emails_sent'] ?? 0),
            'clicks_total' => (int) ($totals['clicks_total'] ?? 0),
            'unique_clicks' => (int) ($totals['unique_clicks'] ?? 0),
            'opens_total' => (int) ($totals['opens_total'] ?? 0),
            'unique_opens' => (int) ($totals['unique_opens'] ?? 0),
        ];
    }
}
