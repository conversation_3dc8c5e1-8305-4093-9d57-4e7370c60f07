<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Actions\Reports;

use App\Domains\Sources\Mailchimp\Actions\GetBaseEndpoint;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpReportException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class GetReport
{
    /**
     * @throws CannotSyncMailchimpReportException
     * @throws ConnectionException
     */
    public function execute(MailchimpCampaign $mailchimpCampaign): array
    {
        $endpoint = sprintf(
            app(GetBaseEndpoint::class)->execute($mailchimpCampaign->mailchimpAccount),
            sprintf('reports/%s', $mailchimpCampaign->external_id)
        );

        $response = Http::withBasicAuth('user', $mailchimpCampaign->mailchimpAccount->api_key)
            ->get($endpoint);

        if (! $response->successful()) {
            throw CannotSyncMailchimpReportException::becauseOfHttpErrorWithStatusCode(
                $mailchimpCampaign,
                $response->status(),
                $response->body()
            );
        }

        return [
            'emails_sent' => $response->json('emails_sent'),
            'clicks_total' => $response->json('clicks.clicks_total'),
            'unique_clicks' => $response->json('clicks.unique_clicks'),
            'click_rate' => $response->json('clicks.click_rate'),
            'opens_total' => $response->json('opens.opens_total'),
            'unique_opens' => $response->json('opens.unique_opens'),
            'open_rate' => $response->json('opens.open_rate'),
        ];
    }
}
