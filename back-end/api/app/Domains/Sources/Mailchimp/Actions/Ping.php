<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Actions;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Support\Facades\Http;

class Ping
{
    public function execute(MailchimpAccount $mailchimpAccount): bool
    {
        $endpoint = sprintf(
            app(GetBaseEndpoint::class)->execute($mailchimpAccount),
            'ping'
        );

        $response = Http::withBasicAuth('user', $mailchimpAccount->api_key)
            ->get($endpoint);

        if (! $response->successful()) {
            return false;
        }

        return true;
    }
}
