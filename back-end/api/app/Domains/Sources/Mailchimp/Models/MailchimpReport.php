<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class MailchimpReport extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function mailchimpCampaign(): BelongsTo
    {
        return $this->belongsTo(MailchimpCampaign::class, 'mailchimp_campaign_id');
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: MailchimpCampaign::class,
                firstKey: 'id',                         // Foreign key on MailchimpCampaign table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'mailchimp_campaign_id',
                secondLocalKey: 'mailchimp_audience_id'  // Local key on MailchimpCampaign table
            )
            ->where('sourceable_type', MailchimpAudience::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
