<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Models;

use App\Domains\Sources\Mailchimp\Support\Enums\Campaign\Status;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MailchimpCampaign extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'status' => Status::class,
        ];
    }

    public function mailchimpAccount(): BelongsTo
    {
        return $this->belongsTo(MailchimpAccount::class, 'mailchimp_account_id');
    }

    public function mailchimpAudience(): BelongsTo
    {
        return $this->belongsTo(MailchimpAudience::class, 'mailchimp_audience_id');
    }

    public function mailchimpReports(): HasMany
    {
        return $this->hasMany(MailchimpReport::class);
    }
}
