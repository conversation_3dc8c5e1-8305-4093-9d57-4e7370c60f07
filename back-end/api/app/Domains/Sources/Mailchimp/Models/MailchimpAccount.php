<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MailchimpAccount extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'api_key' => 'encrypted',
            'last_pinged_at' => 'datetime',
        ];
    }

    public function mailchimpCampaigns(): HasMany
    {
        return $this->hasMany(MailchimpCampaign::class);
    }
}
