<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Models;

use App\Domains\Dashboard\Models\DataSource;
use App\Support\Traits\SyncDeactivatedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class MailchimpAudience extends Model
{
    use HasFactory;
    use SyncDeactivatedTrait;

    protected $guarded = [];

    public function mailchimpAccount(): BelongsTo
    {
        return $this->belongsTo(MailchimpAccount::class);
    }

    public function mailchimpCampaigns(): HasMany
    {
        return $this->hasMany(MailchimpCampaign::class, 'mailchimp_audience_id');
    }

    public function dataSource(): MorphOne
    {
        return $this->morphOne(DataSource::class, 'sourceable');
    }
}
