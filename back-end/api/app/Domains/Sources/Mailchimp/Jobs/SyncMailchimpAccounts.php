<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncMailchimpAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (MailchimpAccount $mailchimpAccount) {
            $job = new SyncMailchimpAccount($mailchimpAccount->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return MailchimpAccount::query()
            ->chunk(100, function (Collection $mailchimpAccounts) use ($closure) {
                foreach ($mailchimpAccounts as $mailchimpAccount) {
                    $closure($mailchimpAccount);
                }
            });
    }
}
