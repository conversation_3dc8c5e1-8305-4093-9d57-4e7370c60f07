<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Ping;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Support\Exceptions\CannotSyncMailchimpAccountException;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncMailchimpAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $mailchimpAccountId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    /**
     * @throws CannotSyncMailchimpAccountException
     */
    public function handle(): void
    {
        $mailchimpAccount = MailchimpAccount::query()
            ->findOrFail($this->mailchimpAccountId);

        if (! app(Ping::class)->execute($mailchimpAccount)) {
            throw CannotSyncMailchimpAccountException::becauseOfFailingPing($mailchimpAccount);
        }

        $mailchimpAccount->last_pinged_at = now();
        $mailchimpAccount->save();

        $job = new SyncMailchimpCampaigns($mailchimpAccount->id, startDate: $this->startDate, endDate: $this->endDate);
        dispatch($job);
    }
}
