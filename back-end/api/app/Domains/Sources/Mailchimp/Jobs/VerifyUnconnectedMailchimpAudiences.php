<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Support\Mails\MailchimpAudienceNotConnectedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class VerifyUnconnectedMailchimpAudiences implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $unconnectedAudienceIds = MailchimpAudience::query()
            ->doesntHave('dataSource')
            ->pluck('id')
            ->toArray();

        if (count($unconnectedAudienceIds) === 0) {
            return;
        }

        $mail = new MailchimpAudienceNotConnectedMail($unconnectedAudienceIds);
        Mail::queue($mail);
    }
}
