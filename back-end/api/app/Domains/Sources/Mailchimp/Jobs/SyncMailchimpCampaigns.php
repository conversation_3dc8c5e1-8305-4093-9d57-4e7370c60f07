<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Campaigns\GetCampaigns;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Support\Scopes\SyncDeactivatedScope;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Carbon;

class SyncMailchimpCampaigns implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $mailchimpAccountId, public int $page = 1, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $mailchimpAccount = MailchimpAccount::query()
            ->findOrFail($this->mailchimpAccountId);

        $response = app(GetCampaigns::class)->execute(
            mailchimpAccount: $mailchimpAccount,
            page: $this->page,
            startDate: $this->startDate
        );

        $this->processCampaigns($response['items']);

        if ($response['total_pages'] > $response['current_page']) {
            $job = new SyncMailchimpCampaigns($this->mailchimpAccountId, $response['current_page'] + 1);
            dispatch($job);
        }
    }

    protected function processCampaigns(array $campaigns): void
    {
        foreach ($campaigns as $campaign) {
            $this->processCampaign($campaign);
        }
    }

    protected function processCampaign(array $campaign): void
    {
        $mailchimpAudience = MailchimpAudience::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->updateOrCreate([
                'mailchimp_account_id' => $this->mailchimpAccountId,
                'external_id' => $campaign['recipients']['list_id'],
            ], [
                'name' => $campaign['recipients']['list_name'],
                'is_active' => $campaign['recipients']['list_is_active'],
            ]);

        $mailchimpCampaign = MailchimpCampaign::query()
            ->updateOrCreate([
                'mailchimp_account_id' => $this->mailchimpAccountId,
                'external_id' => $campaign['id'],
            ], [
                'mailchimp_audience_id' => $mailchimpAudience->id,
                'status' => $campaign['status'],
                'send_time' => Carbon::parse($campaign['send_time']),
                'title' => $campaign['title'],
            ]);

        $job = new SyncMailchimpReport($mailchimpCampaign->id);
        dispatch($job);
    }
}
