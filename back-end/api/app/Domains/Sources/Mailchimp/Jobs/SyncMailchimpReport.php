<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Jobs;

use App\Domains\Sources\Mailchimp\Actions\Reports\GetReport;
use App\Domains\Sources\Mailchimp\Actions\Reports\GetReportTotal;
use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use App\Domains\Sources\Mailchimp\Models\MailchimpReport;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncMailchimpReport implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $mailchimpCampaignId) {}

    public function handle(): void
    {
        $mailchimpCampaign = MailchimpCampaign::query()
            ->findOrFail($this->mailchimpCampaignId);

        $response = app(GetReport::class)->execute(
            $mailchimpCampaign,
        );

        $this->processReport($mailchimpCampaign, $response);
    }

    protected function processReport(MailchimpCampaign $mailchimpCampaign, array $report): void
    {
        $currentTotal = app(GetReportTotal::class)->execute($mailchimpCampaign);

        MailchimpReport::query()
            ->updateOrCreate([
                'mailchimp_campaign_id' => $this->mailchimpCampaignId,
                'date' => now()->format('Y-m-d'),
            ], [
                'emails_sent' => $report['emails_sent'] - ($currentTotal['emails_sent'] ?? 0),
                'clicks_total' => $report['clicks_total'] - ($currentTotal['clicks_total'] ?? 0),
                'unique_clicks' => $report['unique_clicks'] - ($currentTotal['unique_clicks'] ?? 0),
                'click_rate' => round($report['click_rate'] * 100, 2),
                'opens_total' => $report['opens_total'] - ($currentTotal['opens_total'] ?? 0),
                'unique_opens' => $report['unique_opens'] - ($currentTotal['unique_opens'] ?? 0),
                'open_rate' => round($report['open_rate'] * 100, 2),
            ]);
    }
}
