<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Support\Mails;

use App\Domains\Sources\Mailchimp\Models\MailchimpAudience;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class MailchimpAudienceNotConnectedMail extends Mailable
{
    /**
     * @param  array<int>  $audienceIds
     */
    public function __construct(public array $audienceIds) {}

    public function envelope(): Envelope
    {
        $count = count($this->audienceIds);

        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('%d Mailchimp %s not connected to a data source',
                $count,
                $count === 1 ? 'Audience is' : 'Audiences are'
            ),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.mailchimp.mailchimp-audience-not-connected-mail',
            with: [
                'mailchimpAudiences' => MailchimpAudience::query()->findMany($this->audienceIds),
            ],
        );
    }
}
