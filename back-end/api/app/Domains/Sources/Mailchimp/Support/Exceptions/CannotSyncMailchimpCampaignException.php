<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Support\Exceptions;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Exception;

class CannotSyncMailchimpCampaignException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(MailchimpAccount $mailchimpAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get campaigns for Mailchimp account #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $mailchimpAccount->id,
            $statusCode,
            $body
        ));
    }
}
