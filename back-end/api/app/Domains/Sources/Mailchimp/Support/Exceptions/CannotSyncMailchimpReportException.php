<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Support\Exceptions;

use App\Domains\Sources\Mailchimp\Models\MailchimpCampaign;
use Exception;

class CannotSyncMailchimpReportException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(MailchimpCampaign $mailchimpCampaign, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get report for Mailchimp campaign #%s. Received HTTP error with status code %s. Response body:\n\n%s',
            $mailchimpCampaign->id,
            $statusCode,
            $body
        ));
    }
}
