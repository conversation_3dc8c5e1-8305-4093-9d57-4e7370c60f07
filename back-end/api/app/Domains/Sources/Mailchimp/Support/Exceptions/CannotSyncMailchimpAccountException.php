<?php

declare(strict_types=1);

namespace App\Domains\Sources\Mailchimp\Support\Exceptions;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Exception;

class CannotSyncMailchimpAccountException extends Exception
{
    public static function becauseOfFailingPing(MailchimpAccount $mailchimpAccount): self
    {
        return new self(sprintf(
            'Cannot sync Mailchimp account #%s. Pinging the Mailchimp API has failed.',
            $mailchimpAccount->id
        ));
    }
}
