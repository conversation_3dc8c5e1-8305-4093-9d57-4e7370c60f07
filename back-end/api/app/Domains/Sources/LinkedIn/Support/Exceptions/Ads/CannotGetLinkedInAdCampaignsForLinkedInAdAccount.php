<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Support\Exceptions\Ads;

use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Exception;

class CannotGetLinkedInAdCampaignsForLinkedInAdAccount extends Exception
{
    public static function becauseOfMissingToken(LinkedInAdAccount $linkedInAdAccount): self
    {
        return new self("Cannot get LinkedIn ad campaigns for LinkedIn ad account {$linkedInAdAccount->id} because of missing token");
    }

    public static function becauseOfHttpErrorWithStatusCode(
        LinkedInAdAccount $linkedInAdAccount,
        int $statusCode,
        string $body
    ): self {
        return new self(
            "Cannot get LinkedIn ad campaigns for LinkedIn ad account {$linkedInAdAccount->id}. ".
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
