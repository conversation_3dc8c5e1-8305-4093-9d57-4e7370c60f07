<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Support\Exceptions\Community;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Exception;

class CannotGetLinkedInOrganisationPageStats extends Exception
{
    public static function becauseOfMissingToken(LinkedInOrganisation $organisation): self
    {
        return new self(
            "Cannot get LinkedIn organisation page stats for organisation {$organisation->id} because of missing token"
        );
    }

    public static function becauseOfHttpErrorWithStatusCode(
        LinkedInOrganisation $organisation,
        int $statusCode,
        string $body
    ): self {
        return new self(
            "Cannot get LinkedIn organisation page stats for organisation {$organisation->id}. ".
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
