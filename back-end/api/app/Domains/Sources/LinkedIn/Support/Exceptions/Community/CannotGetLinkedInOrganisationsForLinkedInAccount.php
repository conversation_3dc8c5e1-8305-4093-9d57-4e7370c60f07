<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Support\Exceptions\Community;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use Exception;

class CannotGetLinkedInOrganisationsForLinkedInAccount extends Exception
{
    public static function becauseOfMissingToken(LinkedInAccount $linkedInAccount): self
    {
        return new self(
            "Cannot get LinkedIn organisations for LinkedIn account {$linkedInAccount->id} because of missing token"
        );
    }

    public static function becauseOfHttpErrorWithStatusCode(
        LinkedInAccount $linkedInAccount,
        int $statusCode,
        string $body
    ): self {
        return new self(
            "Cannot get LinkedIn organisations for LinkedIn account {$linkedInAccount->id}. ".
            "Status code: {$statusCode}. Response body: {$body}"
        );
    }
}
