<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Models;

use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class LinkedInAccount extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'token_created_at' => 'datetime',
            'type' => LinkedInAccountType::class,
        ];
    }

    public function adAccounts(): HasMany
    {
        return $this->hasMany(LinkedInAdAccount::class, 'linkedin_account_id');
    }
}
