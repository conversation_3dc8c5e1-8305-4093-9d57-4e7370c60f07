<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class LinkedInOrganisationPost extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function linkedInOrganisation(): BelongsTo
    {
        return $this->belongsTo(LinkedInOrganisation::class, 'linkedin_organisation_id');
    }

    public function dataSource(): BelongsTo
    {
        return $this
            ->belongsTo(DataSource::class, 'linkedin_organisation_id', 'sourceable_id')
            ->where('sourceable_type', LinkedInOrganisation::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn ($query) => $query->whereIn('id', $dataSourceIds)
        );
    }
}
