<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Models;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class LinkedInOrganisationPostInsight extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
        ];
    }

    public function linkedInOrganisationPost(): BelongsTo
    {
        return $this->belongsTo(LinkedInOrganisationPost::class, 'linkedin_organisation_post_id');
    }

    public function dataSource(): HasOneThrough
    {
        return $this
            ->hasOneThrough(
                related: DataSource::class,
                through: LinkedInOrganisationPost::class,
                firstKey: 'id',                         // Foreign key on LinkedInOrganisationPost table
                secondKey: 'sourceable_id',             // Foreign key on DataSource table
                localKey: 'linkedin_organisation_post_id',
                secondLocalKey: 'linkedin_organisation_id'  // Local key on LinkedInOrganisationPost table
            )
            ->where('sourceable_type', LinkedInOrganisation::class);
    }

    public function scopeWithDataSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            'dataSource',
            fn (Builder $query) => $query->whereIn('data_sources.id', $dataSourceIds)
        );
    }
}
