<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class LinkedInAdCampaign extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'last_synced_at' => 'datetime',
        ];
    }

    public function linkedInAdAccount(): BelongsTo
    {
        return $this->belongsTo(LinkedInAdAccount::class, 'linkedin_ad_account_id');
    }

    public function insights(): HasMany
    {
        return $this->hasMany(LinkedInAdInsight::class, 'linkedin_ad_insight_id');
    }
}
