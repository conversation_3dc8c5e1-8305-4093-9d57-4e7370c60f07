<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Community\SyncLinkedInOrganisationFollowers;
use App\Domains\Sources\LinkedIn\Actions\Community\SyncLinkedInOrganisationPageStats;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInOrganisation implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInOrganisationId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInOrganisation = LinkedInOrganisation::query()->findOrFail($this->linkedInOrganisationId);

        app(SyncLinkedInOrganisationPageStats::class)->execute($linkedInOrganisation, $this->startDate, $this->endDate);
        app(SyncLinkedInOrganisationFollowers::class)->execute($linkedInOrganisation);
    }
}
