<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLinkedInAdCampaigns implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInAdAccountId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInAdAccount = LinkedInAdAccount::query()->findOrFail($this->linkedInAdAccountId);

        $this->query(function (Collection $linkedInAdCampaigns) use ($linkedInAdAccount) {
            $job = new SyncLinkedInAdCampaign($linkedInAdAccount->id, $linkedInAdCampaigns->pluck('external_id')->all(), $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LinkedInAdCampaign::query()
            ->whereNotIn('status', ['DRAFT', 'PAUSED', 'REMOVED'])
            ->where(function (Builder $query) {
                $query->whereNull('end_date');
                $query->orWhereDate('end_date', '>=', now()->subDays(14)->startOfDay());
            })
            ->chunk(25, function (Collection $linkedInAdCampaigns) use ($closure) {
                $closure($linkedInAdCampaigns);
            });
    }
}
