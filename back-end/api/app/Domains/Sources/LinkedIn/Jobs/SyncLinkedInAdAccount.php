<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetLinkedInAdCampaigns;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInAdAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInAdAccountId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInAdAccount = LinkedInAdAccount::query()->findOrFail($this->linkedInAdAccountId);

        app(GetLinkedInAdCampaigns::class)->execute($linkedInAdAccount);

        $this->dispatchSyncCampaignsJob($linkedInAdAccount);
    }

    protected function dispatchSyncCampaignsJob(LinkedInAdAccount $linkedInAdAccount): void
    {
        $job = new SyncLinkedInAdCampaigns($linkedInAdAccount->id, $this->startDate, $this->endDate);
        dispatch($job);
    }
}
