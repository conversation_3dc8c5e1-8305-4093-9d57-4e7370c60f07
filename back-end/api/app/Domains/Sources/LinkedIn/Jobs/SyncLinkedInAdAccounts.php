<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLinkedInAdAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (LinkedInAdAccount $linkedInAccount) {
            $job = new SyncLinkedInAdAccount($linkedInAccount->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LinkedInAdAccount::query()
            ->chunk(100, function (Collection $linkedInAdAccounts) use ($closure) {
                foreach ($linkedInAdAccounts as $linkedInAdAccount) {
                    $closure($linkedInAdAccount);
                }
            });
    }
}
