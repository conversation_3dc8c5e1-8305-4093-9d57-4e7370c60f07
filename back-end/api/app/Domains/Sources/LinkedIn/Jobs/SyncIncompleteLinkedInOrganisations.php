<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationDetails;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Support\Scopes\SyncDeactivatedScope;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncIncompleteLinkedInOrganisations implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (LinkedInOrganisation $linkedInOrganisation) {
            app(GetLinkedInOrganisationDetails::class)->execute($linkedInOrganisation->linkedInAccount, $linkedInOrganisation->external_id);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LinkedInOrganisation::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->whereNull('name')
            ->whereDate('created_at', '>=', now()->subWeek())
            ->chunk(100, function (Collection $linkedInOrganisations) use ($closure) {
                foreach ($linkedInOrganisations as $linkedInOrganisation) {
                    $closure($linkedInOrganisation);
                }
            });
    }
}
