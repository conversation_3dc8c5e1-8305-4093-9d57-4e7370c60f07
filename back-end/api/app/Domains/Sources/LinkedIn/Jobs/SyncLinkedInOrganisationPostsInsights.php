<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLinkedInOrganisationPostsInsights implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInOrganisationId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->postQuery(function (Collection $posts) {
            $job = new SyncLinkedInOrganisationPostInsights($this->linkedInOrganisationId, $posts->pluck('external_id')->all(), $this->startDate, $this->endDate);
            dispatch($job);
        });
        $this->shareQuery(function (Collection $posts) {
            $job = new SyncLinkedInOrganisationShareInsights($this->linkedInOrganisationId, $posts->pluck('external_id')->all(), $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function postQuery(Closure $closure): bool
    {
        return LinkedInOrganisationPost::query()
            ->where('linkedin_organisation_id', $this->linkedInOrganisationId)
            ->whereLike('external_id', 'urn:li:ugcPost:%')
            ->chunk(50, function (Collection $posts) use ($closure) {
                $closure($posts);
            });
    }

    protected function shareQuery(Closure $closure): bool
    {
        return LinkedInOrganisationPost::query()
            ->where('linkedin_organisation_id', $this->linkedInOrganisationId)
            ->whereLike('external_id', 'urn:li:share:%')
            ->chunk(50, function (Collection $posts) use ($closure) {
                $closure($posts);
            });
    }
}
