<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLinkedInOrganisations implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (LinkedInOrganisation $linkedInOrganisation) {
            $job = new SyncLinkedInOrganisation($linkedInOrganisation->id, $this->startDate, $this->endDate);
            dispatch($job);

            $job = new SyncLinkedInOrganisationPosts($linkedInOrganisation->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LinkedInOrganisation::query()
            ->chunk(100, function (Collection $linkedInOrganisations) use ($closure) {
                foreach ($linkedInOrganisations as $linkedInOrganisation) {
                    $closure($linkedInOrganisation);
                }
            });
    }
}
