<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationPosts;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInOrganisationPosts implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInOrganisationId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInOrganisation = LinkedInOrganisation::query()->findOrFail($this->linkedInOrganisationId);

        app(GetLinkedInOrganisationPosts::class)->execute($linkedInOrganisation);

        $job = new SyncLinkedInOrganisationPostsInsights($linkedInOrganisation->id, $this->startDate, $this->endDate);
        dispatch($job);
    }
}
