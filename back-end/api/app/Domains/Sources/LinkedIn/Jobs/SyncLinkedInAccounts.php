<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncLinkedInAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (LinkedInAccount $linkedInAccount) {
            $job = new SyncLinkedInAccount($linkedInAccount->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return LinkedInAccount::query()
            ->chunk(100, function (Collection $linkedInAccounts) use ($closure) {
                foreach ($linkedInAccounts as $linkedInAccount) {
                    $closure($linkedInAccount);
                }
            });
    }
}
