<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Community\GetLinkedInOrganisationShareInsights;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInOrganisationShareInsights implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInOrganisationId, public array $externalPostIds, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInOrganisation = LinkedInOrganisation::query()->findOrFail($this->linkedInOrganisationId);

        app(GetLinkedInOrganisationShareInsights::class)->execute(
            $linkedInOrganisation,
            $this->externalPostIds,
            $this->startDate ?? now()->startOfDay()->subDays(8),
            $this->endDate ?? now()->startOfDay()->subDays(1)
        );
    }
}
