<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetLinkedInAdAnalytics;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInAdCampaign implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInAdAccountId, public array $campaignIds, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $linkedInAdAccount = LinkedInAdAccount::query()->findOrFail($this->linkedInAdAccountId);

        app(GetLinkedInAdAnalytics::class)->execute(
            $this->campaignIds,
            $linkedInAdAccount->linkedInAccount,
            $this->startDate ?? now()->startOfDay()->subDays(8),
            $this->endDate ?? now()->endOfDay()->subDay()
        );
    }
}
