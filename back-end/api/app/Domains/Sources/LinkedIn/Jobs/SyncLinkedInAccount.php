<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Jobs;

use App\Domains\Sources\LinkedIn\Actions\Ads\GetAccessibleLinkedInAdAccounts;
use App\Domains\Sources\LinkedIn\Actions\Community\GetAccessibleLinkedInOrganisations;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncLinkedInAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $linkedInAccountId) {}

    public function handle(): void
    {
        $linkedInAccount = LinkedInAccount::query()->findOrFail($this->linkedInAccountId);

        if ($linkedInAccount->type == LinkedInAccountType::ADS) {
            $this->linkedInAdsSync($linkedInAccount);
        }

        if ($linkedInAccount->type == LinkedInAccountType::COMMUNITY) {
            $this->linkedInCommunitySync($linkedInAccount);
        }
    }

    protected function linkedInAdsSync(LinkedInAccount $linkedInAccount): void
    {
        app(GetAccessibleLinkedInAdAccounts::class)->execute($linkedInAccount);
    }

    protected function linkedInCommunitySync(LinkedInAccount $linkedInAccount): void
    {
        app(GetAccessibleLinkedInOrganisations::class)->execute($linkedInAccount);
    }
}
