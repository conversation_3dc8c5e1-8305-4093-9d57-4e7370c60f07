<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationPageStats;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class SyncLinkedInOrganisationPageStats
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    public function execute(LinkedInOrganisation $organisation, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): void
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($organisation);
        $this->processPageStats($organisation, $response->json());
    }

    protected function processPageStats(LinkedInOrganisation $organisation, array $data): void
    {
        if (empty($data['elements'])) {
            return;
        }

        foreach ($data['elements'] as $element) {
            $timeRange = $element['timeRange'];
            $date = date('Y-m-d', $timeRange['start'] / 1000); // Convert milliseconds to date

            $stats = $element['totalPageStatistics'];

            // Calculate total clicks
            $totalClicks = 0;
            if (isset($stats['clicks'])) {
                foreach ($stats['clicks']['mobileCustomButtonClickCounts'] ?? [] as $click) {
                    $totalClicks += $click['clicks'] ?? 0;
                }
                foreach ($stats['clicks']['desktopCustomButtonClickCounts'] ?? [] as $click) {
                    $totalClicks += $click['clicks'] ?? 0;
                }
            }

            // Get total page views
            $totalViews = $stats['views']['allPageViews']['pageViews'] ?? 0;

            // Update or create insight for this date
            LinkedInOrganisationInsight::updateOrCreate(
                [
                    'linkedin_organisation_id' => $organisation->id,
                    'date' => $date,
                ],
                [
                    'clicks' => $totalClicks,
                    'views' => $totalViews,
                ]
            );
        }
    }

    protected function getResponse(LinkedInOrganisation $organisation): Response
    {
        $token = app(RefreshToken::class)->execute($organisation->linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationPageStats::becauseOfMissingToken($organisation);
        }

        $startTime = ($this->startDate ?? now()->subDays(7))->startOfDay()->timestamp * 1000; // Convert to milliseconds
        $endTime = ($this->endDate ?? now())->endOfDay()->timestamp * 1000;

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get('https://api.linkedin.com/rest/organizationPageStatistics', [
            'q' => 'organization',
            'organization' => "urn:li:organization:{$organisation->external_id}",
            'timeIntervals.timeGranularityType' => 'DAY',
            'timeIntervals.timeRange.start' => $startTime,
            'timeIntervals.timeRange.end' => $endTime,
        ]);

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationPageStats::becauseOfHttpErrorWithStatusCode(
                organisation: $organisation,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
