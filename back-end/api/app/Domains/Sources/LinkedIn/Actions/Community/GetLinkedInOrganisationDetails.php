<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationDetails;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetLinkedInOrganisationDetails
{
    public function execute(LinkedInAccount $linkedInAccount, string $externalId): void
    {
        $response = $this->getResponse($linkedInAccount, $externalId);
        $this->processOrganisationDetails($linkedInAccount, $externalId, $response->json());
    }

    protected function processOrganisationDetails(
        LinkedInAccount $linkedInAccount,
        string $externalId,
        array $organisationData
    ): void {
        LinkedInOrganisation::query()->withoutGlobalScope(SyncDeactivatedScope::class)->where([
            'linkedin_account_id' => $linkedInAccount->id,
            'external_id' => $externalId,
        ])->update([
            'name' => $organisationData['localizedName'] ?? null,
            'website' => $organisationData['website']['localized']['en_US'] ?? null,
            'last_synced_at' => now(),
        ]);
    }

    protected function getResponse(LinkedInAccount $linkedInAccount, string $externalId): Response
    {
        $token = app(RefreshToken::class)->execute($linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationDetails::becauseOfMissingToken($linkedInAccount);
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '2.0.0',
        ])->get("https://api.linkedin.com/rest/organizations/{$externalId}");

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationDetails::becauseOfHttpErrorWithStatusCode(
                linkedInAccount: $linkedInAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
