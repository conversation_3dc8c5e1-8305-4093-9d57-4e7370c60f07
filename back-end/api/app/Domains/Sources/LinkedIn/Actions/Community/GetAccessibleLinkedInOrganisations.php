<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationsForLinkedInAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class GetAccessibleLinkedInOrganisations
{
    public function execute(LinkedInAccount $linkedInAccount, ?int $start = 0): ?int
    {
        $response = $this->getResponse($linkedInAccount, $start);
        $organisations = $response->json('elements', []);

        $this->processOrganisations(
            linkedInAccount: $linkedInAccount,
            organisations: $organisations
        );

        return $this->getNextPageStart($response);
    }

    protected function getNextPageStart(Response $response): ?int
    {
        $paging = $response->json('paging', []);
        $currentStart = $paging['start'] ?? 0;
        $count = $paging['count'] ?? 0;
        $totalElements = count($response->json('elements', []));

        return $totalElements >= $count ? $currentStart + $count : null;
    }

    protected function processOrganisations(
        LinkedInAccount $linkedInAccount,
        array $organisations
    ): void {
        foreach ($organisations as $organisation) {
            $this->processOrganisation($linkedInAccount, $organisation);
        }
    }

    protected function processOrganisation(
        LinkedInAccount $linkedInAccount,
        array $organisation
    ): void {
        $externalId = Str::after($organisation['organization'], 'urn:li:organization:');

        LinkedInOrganisation::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->updateOrCreate(
                [
                    'linkedin_account_id' => $linkedInAccount->id,
                    'external_id' => $externalId,
                ],
                [
                    'state' => $organisation['state'],
                    'last_synced_at' => now(),
                ]
            );
    }

    /**
     * @throws CannotGetLinkedInOrganisationsForLinkedInAccount
     * @throws ConnectionException
     */
    protected function getResponse(LinkedInAccount $linkedInAccount, ?int $start): Response
    {
        $token = app(RefreshToken::class)->execute($linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationsForLinkedInAccount::becauseOfMissingToken(
                $linkedInAccount
            );
        }

        $parameters = [
            'q' => 'roleAssignee',
            'count' => 50, // Request doesn't support pagination according to docs, but does have these properties
        ];

        if (! is_null($start)) {
            $parameters['start'] = $start;
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '2.0.0',
        ])->get('https://api.linkedin.com/rest/organizationAcls', $parameters);

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationsForLinkedInAccount::becauseOfHttpErrorWithStatusCode(
                linkedInAccount: $linkedInAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
