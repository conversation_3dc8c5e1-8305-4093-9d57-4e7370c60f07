<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPostInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationPostInsights;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class GetLinkedInOrganisationPostInsights
{
    protected string $externalIdKey = 'ugcPost';

    protected string $externalIdParameter = 'ugcPosts';

    public function execute(
        LinkedInOrganisation $organisation,
        array $postIds,
        Carbon $startDate,
        Carbon $endDate
    ): array {
        $response = $this->getResponse($organisation, $postIds, $startDate, $endDate);
        $statistics = $response->json('elements', []);

        $this->saveStatistics($organisation, $statistics);

        return $statistics;
    }

    protected function saveStatistics(LinkedInOrganisation $organisation, array $statistics): void
    {
        foreach ($statistics as $statistic) {
            $post = LinkedInOrganisationPost::query()
                ->where('linkedin_organisation_id', $organisation->id)
                ->where('external_id', $statistic[$this->externalIdKey])
                ->first();

            if (! $post) {
                continue;
            }

            $date = Carbon::createFromTimestampMs($statistic['timeRange']['start']);

            LinkedInOrganisationPostInsight::query()->updateOrCreate(
                [
                    'linkedin_organisation_post_id' => $post->id,
                    'date' => $date,
                ],
                [
                    'unique_impressions_count' => $statistic['totalShareStatistics']['uniqueImpressionsCount'] ?? 0,
                    'share_count' => $statistic['totalShareStatistics']['shareCount'] ?? 0,
                    'engagement' => $statistic['totalShareStatistics']['engagement'] ?? 0,
                    'click_count' => $statistic['totalShareStatistics']['clickCount'] ?? 0,
                    'like_count' => $statistic['totalShareStatistics']['likeCount'] ?? 0,
                    'impression_count' => $statistic['totalShareStatistics']['impressionCount'] ?? 0,
                    'comment_count' => $statistic['totalShareStatistics']['commentCount'] ?? 0,
                ]
            );
        }
    }

    /**
     * @throws CannotGetLinkedInOrganisationPostInsights
     * @throws ConnectionException
     */
    protected function getResponse(
        LinkedInOrganisation $organisation,
        array $postIds,
        Carbon $startDate,
        Carbon $endDate
    ): Response {
        $token = app(RefreshToken::class)->execute($organisation->linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationPostInsights::becauseOfMissingToken($organisation);
        }

        $parameters = [
            'q' => 'organizationalEntity',
            'organizationalEntity' => "urn:li:organization:{$organisation->external_id}",
            'timeIntervals.timeGranularityType' => 'DAY',
            'timeIntervals.timeRange.start' => $startDate->getPreciseTimestamp(3),
            'timeIntervals.timeRange.end' => $endDate->getPreciseTimestamp(3),
        ];

        foreach ($postIds as $postId) {
            $parameters[$this->externalIdParameter][] = $postId;
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get('https://api.linkedin.com/rest/organizationalEntityShareStatistics', $parameters);

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationPostInsights::becauseOfHttpErrorWithStatusCode(
                organisation: $organisation,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
