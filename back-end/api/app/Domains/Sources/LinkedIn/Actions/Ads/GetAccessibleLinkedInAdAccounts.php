<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Ads\CannotGetLinkedInAdAccountsForLinkedInAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetAccessibleLinkedInAdAccounts
{
    public function execute(LinkedInAccount $linkedInAccount, ?int $start = 0): ?int
    {
        $response = $this->getResponse($linkedInAccount, $start);
        $accounts = $response->json('elements', []);

        $this->processRows(
            linkedInAccount: $linkedInAccount,
            accounts: $accounts
        );

        return $this->getNextPageStart($response);
    }

    protected function getNextPageStart(Response $response): ?int
    {
        $paging = $response->json('paging', []);
        $currentStart = $paging['start'] ?? 0;
        $count = $paging['count'] ?? 0;
        $totalElements = count($response->json('elements', []));

        // If we received fewer elements than requested, we've reached the end
        return $totalElements >= $count ? $currentStart + $count : null;
    }

    protected function processRows(LinkedInAccount $linkedInAccount, array $accounts): void
    {
        foreach ($accounts as $account) {
            $this->processRow($linkedInAccount, $account);
        }
    }

    protected function processRow(LinkedInAccount $linkedInAccount, array $account): void
    {
        LinkedInAdAccount::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->updateOrCreate(
                [
                    'linkedin_account_id' => $linkedInAccount->id,
                    'external_id' => $account['id'],
                ],
                [
                    'name' => $account['name'],
                    'currency' => $account['currency'],
                    'status' => $account['status'],
                    'type' => $account['type'],
                    'reference' => $account['reference'],
                    'test' => $account['test'],
                    'last_synced_at' => now(),
                ]
            );
    }

    /**
     * @throws CannotGetLinkedInAdAccountsForLinkedInAccount
     * @throws ConnectionException
     */
    protected function getResponse(LinkedInAccount $linkedInAccount, ?int $start): Response
    {
        $token = app(RefreshToken::class)->execute($linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInAdAccountsForLinkedInAccount::becauseOfMissingToken($linkedInAccount);
        }

        $parameters = [
            'q' => 'search',
            'count' => 50, // Request doesn't support pagination according to docs, but does have these properties
        ];

        if (! is_null($start)) {
            $parameters['start'] = $start;
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get('https://api.linkedin.com/rest/adAccounts', $parameters);

        if (! $response->successful()) {
            throw CannotGetLinkedInAdAccountsForLinkedInAccount::becauseOfHttpErrorWithStatusCode(
                linkedInAccount: $linkedInAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
