<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Ads\CannotGetLinkedInAdCampaignsForLinkedInAdAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetLinkedInAdCampaigns
{
    public function execute(LinkedInAdAccount $adAccount, ?int $start = 0): ?int
    {
        $response = $this->getResponse($adAccount, $start);
        $campaigns = $response->json('elements', []);

        $this->processRows(
            adAccount: $adAccount,
            campaigns: $campaigns
        );

        return $this->getNextPageStart($response);
    }

    protected function getNextPageStart(Response $response): ?int
    {
        $paging = $response->json('paging', []);
        $start = $paging['start'] ?? 0;
        $count = $paging['count'] ?? 0;
        $totalElements = count($response->json('elements', []));

        return $totalElements >= $count ? $start + $count : null;
    }

    protected function processRows(LinkedInAdAccount $adAccount, array $campaigns): void
    {
        foreach ($campaigns as $campaign) {
            $this->processRow($adAccount, $campaign);
        }
    }

    protected function processRow(LinkedInAdAccount $adAccount, array $campaign): void
    {
        LinkedInAdCampaign::updateOrCreate(
            [
                'linkedin_ad_account_id' => $adAccount->id,
                'external_id' => $campaign['id'],
            ],
            [
                'name' => $campaign['name'],
                'status' => $campaign['status'],
                'type' => $campaign['type'],
                'objective_type' => $campaign['objectiveType'],
                'total_budget' => $campaign['totalBudget']['amount'] ?? null,
                'currency' => $campaign['totalBudget']['currencyCode'] ??
                        $campaign['dailyBudget']['currencyCode'] ?? null,
                'unit_cost' => $campaign['unitCost']['amount'] ?? null,
                'pacing_strategy' => $campaign['pacingStrategy'],
                'optimization_target_type' => $campaign['optimizationTargetType'],
                'start_date' => isset($campaign['runSchedule']['start'])
                    ? date('Y-m-d H:i:s', (int) ($campaign['runSchedule']['start'] / 1000))
                    : null,
                'end_date' => isset($campaign['runSchedule']['end'])
                    ? date('Y-m-d H:i:s', (int) ($campaign['runSchedule']['end'] / 1000))
                    : null,
                'last_synced_at' => now(),
            ]
        );
    }

    /**
     * @throws CannotGetLinkedInAdCampaignsForLinkedInAdAccount
     * @throws ConnectionException
     */
    protected function getResponse(LinkedInAdAccount $adAccount, ?int $start): Response
    {
        $token = app(RefreshToken::class)->execute($adAccount->linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInAdCampaignsForLinkedInAdAccount::becauseOfMissingToken($adAccount);
        }

        $parameters = [
            'q' => 'search',
            'search.test' => 'False',
        ];

        if (! is_null($start)) {
            $parameters['start'] = $start;
            $parameters['count'] = 1000; // Request doesn't support pagination according to docs, but does have these properties
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get(
            "https://api.linkedin.com/rest/adAccounts/{$adAccount->external_id}/adCampaigns",
            $parameters
        );

        if (! $response->successful()) {
            throw CannotGetLinkedInAdCampaignsForLinkedInAdAccount::becauseOfHttpErrorWithStatusCode(
                linkedInAdAccount: $adAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
