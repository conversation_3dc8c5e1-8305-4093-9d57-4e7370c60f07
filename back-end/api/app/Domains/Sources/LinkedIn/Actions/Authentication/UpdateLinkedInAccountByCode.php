<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Authentication;

use App\Domains\Slack\Messages\NewLinkedinAdsAccountConnectedMessage;
use App\Domains\Slack\Messages\NewLinkedinCommunityAccountConnectedMessage;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use App\Domains\Support\SlackNotification;
use Illuminate\Support\Facades\Http;

class UpdateLinkedInAccountByCode
{
    public function execute(string $code, LinkedInAccountType $accountType): LinkedInAccount
    {
        $tokenConfiguration = $this->getTokenConfiguration($code, $accountType);

        if (! $tokenConfiguration) {
            abort(500);
        }

        $profileConfiguration = $this->getProfile($tokenConfiguration['access_token']);

        if (! $profileConfiguration) {
            abort(500);
        }

        $linkedInAccount = LinkedInAccount::query()
            ->where('type', $accountType->value)
            ->where('external_id', $profileConfiguration['id'])
            ->first();

        if (! $linkedInAccount) {
            $linkedInAccount = new LinkedInAccount;
        }

        $linkedInAccount->type = $accountType->value;
        $linkedInAccount->external_id = $profileConfiguration['id'];
        $linkedInAccount->name = $profileConfiguration['localizedFirstName'].' '.$profileConfiguration['localizedLastName'];
        $linkedInAccount->refresh_token = $tokenConfiguration['refresh_token'];
        $linkedInAccount->token = $tokenConfiguration['access_token'];
        $linkedInAccount->token_created_at = now()->addSeconds($tokenConfiguration['expires_in'] - 60);
        $linkedInAccount->email = $profileConfiguration['email'] ?? null;

        $linkedInAccount->save();

        if ($linkedInAccount->wasRecentlyCreated) {
            $notification = new NewLinkedinCommunityAccountConnectedMessage($linkedInAccount);

            if ($accountType === LinkedInAccountType::ADS) {
                $notification = new NewLinkedinAdsAccountConnectedMessage($linkedInAccount);
            }

            SlackNotification::send($notification);
        }

        return $linkedInAccount;
    }

    protected function getTokenConfiguration(string $code, LinkedInAccountType $accountType): ?array
    {
        $response = Http::asForm()->post(
            'https://www.linkedin.com/oauth/v2/accessToken',
            [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $this->getRedirectUriForAccountType($accountType),
                'client_id' => $this->getClientIdForAccountType($accountType),
                'client_secret' => $this->getClientSecretForAccountType($accountType),
            ]
        );

        if (! $response->successful()) {
            return null;
        }

        return $response->json();
    }

    protected function getRedirectUriForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => route('sources.linkedin.callbackAds'),
            LinkedInAccountType::COMMUNITY => route('sources.linkedin.callbackCommunity'),
        };
    }

    protected function getClientIdForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => config('services.linkedin-ads.client_id'),
            LinkedInAccountType::COMMUNITY => config('services.linkedin-community.client_id'),
        };
    }

    protected function getClientSecretForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => config('services.linkedin-ads.client_secret'),
            LinkedInAccountType::COMMUNITY => config('services.linkedin-community.client_secret'),
        };
    }

    protected function getProfile(string $token): ?array
    {
        $response = Http::withToken($token)
            ->withHeaders([
                'X-Restli-Protocol-Version' => '2.0.0',
            ])
            ->get('https://api.linkedin.com/v2/me');

        if (! $response->successful()) {
            return null;
        }

        return $response->json();
    }
}
