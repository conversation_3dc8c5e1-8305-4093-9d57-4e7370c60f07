<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Authentication;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class RefreshToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(LinkedInAccount $linkedInAccount): ?string
    {
        if (! $this->shouldRefreshToken($linkedInAccount)) {
            return $linkedInAccount->token;
        }

        $tokenDetails = $this->tokenDetails($linkedInAccount);

        if (! $tokenDetails) {
            return null;
        }

        $linkedInAccount->refresh_token = $tokenDetails['refresh_token'];
        $linkedInAccount->token = $tokenDetails['access_token'];
        $linkedInAccount->token_created_at = $tokenDetails['access_token'] ? now() : null;

        $linkedInAccount->save();

        return $tokenDetails['access_token'];
    }

    protected function shouldRefreshToken(LinkedInAccount $linkedInAccount): bool
    {
        if (! $linkedInAccount->token_created_at || ! $linkedInAccount->token) {
            return true;
        }

        $expiryThreshold = now()->subMinutes(55);

        return $linkedInAccount->token_created_at->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function tokenDetails(LinkedInAccount $linkedInAccount): ?array
    {
        $response = Http::asForm()
            ->connectTimeout(10)
            ->post(
                url: 'https://www.linkedin.com/oauth/v2/accessToken',
                data: [
                    'client_id' => $this->getClientIdForAccountType($linkedInAccount->type),
                    'client_secret' => $this->getClientSecretForAccountType($linkedInAccount->type),
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $linkedInAccount->refresh_token,
                ]
            );

        if (! $response->successful()) {
            return null;
        }

        return $response->json();
    }

    protected function getClientIdForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => config('services.linkedin-ads.client_id'),
            LinkedInAccountType::COMMUNITY => config('services.linkedin-community.client_id'),
        };
    }

    protected function getClientSecretForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => config('services.linkedin-ads.client_secret'),
            LinkedInAccountType::COMMUNITY => config('services.linkedin-community.client_secret'),
        };
    }
}
