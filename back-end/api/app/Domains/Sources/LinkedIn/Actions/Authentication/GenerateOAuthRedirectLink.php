<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Authentication;

use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;

class GenerateOAuthRedirectLink
{
    public function execute(LinkedInAccountType $accountType): string
    {
        $params = [
            'response_type' => 'code',
            'client_id' => $this->getClientIdForAccountType($accountType),
            'redirect_uri' => $this->getRedirectUriForAccountType($accountType),
            'state' => csrf_token(),
            'scope' => implode(' ', $this->getScopesForAccountType($accountType)),
        ];

        return 'https://www.linkedin.com/oauth/v2/authorization?'.http_build_query($params);
    }

    protected function getRedirectUriForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => route('sources.linkedin.callbackAds'),
            LinkedInAccountType::COMMUNITY => route('sources.linkedin.callbackCommunity'),
        };
    }

    protected function getClientIdForAccountType(LinkedInAccountType $accountType): string
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => config('services.linkedin-ads.client_id'),
            LinkedInAccountType::COMMUNITY => config('services.linkedin-community.client_id'),
        };
    }

    protected function getScopesForAccountType(LinkedInAccountType $accountType): array
    {
        return match ($accountType) {
            LinkedInAccountType::ADS => ['openid', 'profile', 'r_ads_reporting', 'r_organization_social', 'rw_organization_admin', 'w_member_social', 'r_ads', 'w_organization_social', 'rw_ads', 'r_basicprofile', 'r_organization_admin', 'email'],
            LinkedInAccountType::COMMUNITY => ['r_organization_followers', 'r_organization_social', 'r_organization_social_feed', 'r_basicprofile', 'rw_organization_admin', 'w_organization_social', 'w_organization_social_feed'],
        };
    }
}
