<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncYoutubeGoogleAccounts implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $this->query(function (YoutubeGoogleAccount $youtubeGoogleAccount) {
            $job = new SyncYoutubeGoogleAccount($youtubeGoogleAccount->id, $this->startDate, $this->endDate);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return YoutubeGoogleAccount::query()
            ->chunk(100, function (Collection $youtubeGoogleAccounts) use ($closure) {
                foreach ($youtubeGoogleAccounts as $youtubeGoogleAccount) {
                    $closure($youtubeGoogleAccount);
                }
            });
    }
}
