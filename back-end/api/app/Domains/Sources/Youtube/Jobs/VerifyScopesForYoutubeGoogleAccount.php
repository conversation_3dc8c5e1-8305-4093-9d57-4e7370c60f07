<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\GetScopes;
use App\Domains\Sources\Youtube\Support\Mails\Authentication\YoutubeGoogleAccountMissingRemoteScopesMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class VerifyScopesForYoutubeGoogleAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $youtubeGoogleAccountId) {}

    public function handle(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::query()->findOrFail($this->youtubeGoogleAccountId);

        $localScopes = $youtubeGoogleAccount->scopes ?? [];
        $remoteScopes = app(GetScopes::class)->execute($youtubeGoogleAccount);

        $missingLocalScopes = array_diff($localScopes, $remoteScopes);

        if (empty($missingLocalScopes)) {
            return;
        }

        $youtubeGoogleAccount->deactivated_at = now();
        $youtubeGoogleAccount->save();

        $mail = new YoutubeGoogleAccountMissingRemoteScopesMail($youtubeGoogleAccount->id, $missingLocalScopes);
        Mail::queue($mail);
    }
}
