<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Google\Models\YoutubeVideoInsight;
use App\Domains\Sources\Youtube\Actions\GetChannelVideoDetails;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncYoutubeVideos implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $youtubeChannelId, public array $ids) {}

    public function handle(): void
    {
        $youtubeChannel = YoutubeChannel::query()
            ->findOrFail($this->youtubeChannelId);

        $this->syncVideoDetails($youtubeChannel, $this->ids);
    }

    protected function syncVideoDetails(YoutubeChannel $youtubeChannel, array $ids): void
    {
        $result = app(GetChannelVideoDetails::class)->execute($youtubeChannel, $ids);
        $videos = $result['items'] ?? [];

        foreach ($videos as $video) {
            $this->processVideo($video);
        }
    }

    protected function processVideo(array $data): void
    {
        $youtubeVideo = YoutubeVideo::query()
            ->where('external_id', $data['id'])
            ->firstOrFail();

        $totalRow = YoutubeVideoInsight::query()
            ->where('youtube_video_id', $youtubeVideo->id)
            ->where('date', '<', now()->startOfDay())
            ->selectRaw('SUM(views) as views')
            ->selectRaw('SUM(likes) as likes')
            ->selectRaw('SUM(dislikes) as dislikes')
            ->selectRaw('SUM(favorites) as favorites')
            ->selectRaw('SUM(comments) as comments')
            ->selectRaw('SUM(engagement) as engagement')
            ->first();

        $totalViews = (int) ($totalRow->views ?? 0);
        $totalLikes = (int) ($totalRow->likes ?? 0);
        $totalDislikes = (int) ($totalRow->dislikes ?? 0);
        $totalFavorites = (int) ($totalRow->favorites ?? 0);
        $totalComments = (int) ($totalRow->comments ?? 0);

        $views = (int) ($data['statistics']['viewCount'] ?? 0) - $totalViews;
        $likes = (int) ($data['statistics']['likeCount'] ?? 0) - $totalLikes;
        $dislikes = (int) ($data['statistics']['dislikeCount'] ?? 0) - $totalDislikes;
        $favorites = (int) ($data['statistics']['favoriteCount'] ?? 0) - $totalFavorites;
        $comments = (int) ($data['statistics']['commentCount'] ?? 0) - $totalComments;
        $engagement = $likes + $dislikes + $favorites + $comments;

        YoutubeVideoInsight::query()->updateOrCreate([
            'youtube_video_id' => $youtubeVideo->id,
            'date' => now()->startOfDay(),
        ], [
            'views' => $views,
            'likes' => $likes,
            'dislikes' => $dislikes,
            'favorites' => $favorites,
            'comments' => $comments,
            'engagement' => $engagement,
        ]);
    }
}
