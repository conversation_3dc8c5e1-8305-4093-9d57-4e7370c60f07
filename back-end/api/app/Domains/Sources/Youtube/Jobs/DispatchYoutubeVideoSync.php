<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class DispatchYoutubeVideoSync implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->queryChannels(function (YoutubeChannel $youtubeChannel) {
            $this->queryVideos($youtubeChannel->id, function (Collection $youtubeVideos) use ($youtubeChannel) {
                $job = new SyncYoutubeVideos($youtubeChannel->id, $youtubeVideos->pluck('external_id')->all());
                dispatch($job);
            });
        });
    }

    protected function queryVideos(int $youtubeChannelId, Closure $closure): bool
    {
        return YoutubeVideo::query()
            ->where('youtube_channel_id', $youtubeChannelId)
            ->chunk(25, function (Collection $youtubeVideos) use ($closure) {
                $closure($youtubeVideos);
            });
    }

    protected function queryChannels(Closure $closure): bool
    {
        return YoutubeChannel::query()
            ->chunk(50, function (Collection $youtubeChannels) use ($closure) {
                foreach ($youtubeChannels as $youtubeChannel) {
                    $closure($youtubeChannel);
                }
            });
    }
}
