<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Queue\Queueable;

class VerifyScopesForYoutubeGoogleAccounts implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        YoutubeGoogleAccount::query()
            ->whereNull('deactivated_at')
            ->chunk(100, function (Collection $youtubeGoogleAccounts) {
                foreach ($youtubeGoogleAccounts as $youtubeGoogleAccount) {
                    $job = new VerifyScopesForYoutubeGoogleAccount($youtubeGoogleAccount->id);
                    dispatch($job);
                }
            });
    }
}
