<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeVideo;
use App\Domains\Sources\Youtube\Actions\GetChannelVideos;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncYoutubeChannel implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $youtubeChannelId) {}

    public function handle(): void
    {
        $youtubeChannel = YoutubeChannel::query()
            ->findOrFail($this->youtubeChannelId);

        $this->syncVideos($youtubeChannel);
    }

    protected function syncVideos(YoutubeChannel $youtubeChannel): void
    {
        $result = app(GetChannelVideos::class)->execute($youtubeChannel);
        $videos = $result['items'] ?? [];

        foreach ($videos as $video) {
            $this->processVideo($youtubeChannel, $video);
        }
    }

    protected function processVideo(YoutubeChannel $youtubeChannel, array $data): void
    {
        $publishedAt = null;
        if (! empty($data['snippet']['publishedAt'])) {
            $publishedAt = Carbon::parse($data['snippet']['publishedAt']);
        }

        YoutubeVideo::query()->updateOrCreate([
            'youtube_channel_id' => $youtubeChannel->id,
            'external_id' => $data['id']['videoId'],
        ], [
            'name' => $data['snippet']['title'],
            'thumbnail_url' => $data['snippet']['thumbnails']['high']['url'],
            'published_at' => $publishedAt,
            'last_synced_at' => now(),
        ]);
    }
}
