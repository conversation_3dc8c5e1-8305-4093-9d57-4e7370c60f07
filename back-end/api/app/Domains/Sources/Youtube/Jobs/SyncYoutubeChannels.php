<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use Closure;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class SyncYoutubeChannels implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $this->query(function (YoutubeChannel $youtubeChannel) {
            $job = new SyncYoutubeChannel($youtubeChannel->id);
            dispatch($job);
        });
    }

    protected function query(Closure $closure): bool
    {
        return YoutubeChannel::query()
            ->chunk(50, function (Collection $youtubeChannels) use ($closure) {
                foreach ($youtubeChannels as $youtubeChannel) {
                    $closure($youtubeChannel);
                }
            });
    }
}
