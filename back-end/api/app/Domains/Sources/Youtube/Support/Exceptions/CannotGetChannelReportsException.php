<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Support\Exceptions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use Exception;

class CannotGetChannelReportsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(YoutubeChannel $youtubeChannel, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get channel reports. Received HTTP error with status code %s for Youtube Channel #%s. Response body:\n\n%s',
            $statusCode,
            $youtubeChannel->id,
            $body
        ));
    }

    public static function becauseOfMissingToken(YoutubeChannel $youtubeChannel): self
    {
        return new self(sprintf(
            'Cannot get channel reports. Could not retrieve token for Youtube Channel #%s.',
            $youtubeChannel->id
        ));
    }
}
