<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Support\Exceptions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use Exception;

class CannotGetChannelVideoDetailsException extends Exception
{
    public static function becauseOfMissingToken(
        YoutubeChannel $youtubeChannel,
        array $ids
    ): self {
        return new self(
            message: sprintf(
                'Cannot get video details for channel [%s] and ids [%s] because of missing token',
                $youtubeChannel->id,
                implode(',', $ids)
            )
        );
    }

    public static function becauseOfHttpErrorWithStatusCode(
        YoutubeChannel $youtubeChannel,
        array $ids,
        int $statusCode,
        string $body
    ): self {
        return new self(
            message: sprintf(
                'Cannot get video details for channel [%s] and ids [%s] because of HTTP error [%d] with body [%s]',
                $youtubeChannel->id,
                implode(',', $ids),
                $statusCode,
                $body
            )
        );
    }
}
