<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Support\Exceptions;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Exception;

class CannotGetChannelDetailsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(YoutubeGoogleAccount $youtubeGoogleAccount, int $statusCode, string $body): self
    {
        return new self(sprintf(
            'Cannot get channel details. Received HTTP error with status code %s for Youtube Google Account #%s. Response body:\n\n%s',
            $statusCode,
            $youtubeGoogleAccount->id,
            $body
        ));
    }

    public static function becauseOfMissingToken(YoutubeGoogleAccount $youtubeGoogleAccount): self
    {
        return new self(sprintf(
            'Cannot get channel details. Could not retrieve token for Youtube Google Account #%s.',
            $youtubeGoogleAccount->id
        ));
    }
}
