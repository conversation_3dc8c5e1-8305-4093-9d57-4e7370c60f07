<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Support\Exceptions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use Exception;

class CannotGetChannelVideosException extends Exception
{
    public static function becauseOfMissingToken(
        YoutubeChannel $youtubeChannel
    ): self {
        return new self(
            message: sprintf(
                'Cannot get videos for channel [%s] because of missing token',
                $youtubeChannel->id
            )
        );
    }

    public static function becauseOfHttpErrorWithStatusCode(
        YoutubeChannel $youtubeChannel,
        int $statusCode,
        string $body
    ): self {
        return new self(
            message: sprintf(
                'Cannot get videos for channel [%s] because of HTTP error [%d] with body [%s]',
                $youtubeChannel->id,
                $statusCode,
                $body
            )
        );
    }
}
