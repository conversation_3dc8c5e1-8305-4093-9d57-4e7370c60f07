<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Support\Mails\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class YoutubeGoogleAccountMissingRemoteScopesMail extends Mailable
{
    public function __construct(protected int $youtubeGoogleAccountId, protected array $missingRemoteScopes = []) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Youtube Google Account #%s deactivated because of missing remote scopes', $this->youtubeGoogleAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sources.youtube.authentication.youtube-google-account-missing-remote-scopes',
            with: [
                'youtubeGoogleAccount' => YoutubeGoogleAccount::query()->findOrFail($this->youtubeGoogleAccountId),
                'missingRemoteScopes' => $this->missingRemoteScopes,
            ],
        );
    }
}
