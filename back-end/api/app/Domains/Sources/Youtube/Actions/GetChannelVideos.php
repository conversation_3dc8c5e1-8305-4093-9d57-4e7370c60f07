<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Support\Exceptions\CannotGetChannelVideosException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetChannelVideos
{
    /**
     * @throws CannotGetChannelVideosException
     * @throws ConnectionException
     */
    public function execute(
        YoutubeChannel $youtubeChannel,
        ?string $pageToken = null
    ): array {
        $response = $this->getResponse($youtubeChannel, $pageToken);

        return $response->json();
    }

    /**
     * @throws CannotGetChannelVideosException
     * @throws ConnectionException
     */
    protected function getResponse(
        YoutubeChannel $youtubeChannel,
        ?string $pageToken
    ): Response {
        $token = app(RefreshToken::class)->execute($youtubeChannel->youtubeGoogleAccount);

        if (! $token) {
            throw CannotGetChannelVideosException::becauseOfMissingToken($youtubeChannel);
        }

        $parameters = [
            'part' => implode(',', [
                'id',
                'snippet',
            ]),
            'channelId' => $youtubeChannel->external_id,
            'maxResults' => 500,
            'order' => 'date',
            'type' => 'video',
            'publishedAfter' => now()->subYear()->toRFC3339String(),
        ];

        if ($pageToken) {
            $parameters['pageToken'] = $pageToken;
        }

        $response = Http::withToken($token)->get(
            url: 'https://www.googleapis.com/youtube/v3/search',
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetChannelVideosException::becauseOfHttpErrorWithStatusCode(
                youtubeChannel: $youtubeChannel,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
