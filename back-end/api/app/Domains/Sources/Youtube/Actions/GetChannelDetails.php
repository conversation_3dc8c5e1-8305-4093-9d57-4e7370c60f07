<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Support\Exceptions\CannotGetChannelDetailsException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetChannelDetails
{
    /**
     * @throws CannotGetChannelDetailsException
     * @throws ConnectionException
     */
    public function execute(YoutubeGoogleAccount $youtubeGoogleAccount): array
    {
        $response = $this->getResponse($youtubeGoogleAccount);

        return $response->json('items', []);
    }

    /**
     * @throws CannotGetChannelDetailsException
     * @throws ConnectionException
     */
    protected function getResponse(YoutubeGoogleAccount $youtubeGoogleAccount): Response
    {
        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);

        if (! $token) {
            throw CannotGetChannelDetailsException::becauseOfMissingToken($youtubeGoogleAccount);
        }

        $parameters = [
            'part' => implode(',', [
                'id', 'snippet', 'contentDetails', 'statistics',
            ]),
            'mine' => true,
            'maxResults' => 50,
        ];

        $response = Http::withToken($token)
            ->get(
                url: 'https://www.googleapis.com/youtube/v3/channels',
                query: $parameters
            );

        if (! $response->successful()) {
            throw CannotGetChannelDetailsException::becauseOfHttpErrorWithStatusCode(
                youtubeGoogleAccount: $youtubeGoogleAccount,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
