<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Support\Exceptions\CannotGetChannelReportsException;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetChannelReports
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    /**
     * @throws CannotGetChannelReportsException
     * @throws ConnectionException
     */
    public function execute(YoutubeChannel $youtubeChannel, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): array
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($youtubeChannel);
        $rows = $response->json('rows');

        return array_map(function (array $row) {
            return [
                'date' => $row[0],
                'views' => $row[1],
                'likes' => $row[2],
                'comments' => $row[3],
                'shares' => $row[4],
            ];
        }, $rows ?? []);
    }

    /**
     * @throws CannotGetChannelReportsException
     * @throws ConnectionException
     */
    protected function getResponse(YoutubeChannel $youtubeChannel): Response
    {
        $token = app(RefreshToken::class)->execute($youtubeChannel->youtubeGoogleAccount);

        if (! $token) {
            throw CannotGetChannelReportsException::becauseOfMissingToken($youtubeChannel);
        }

        $parameters = [
            'metrics' => implode(',', [
                'views', 'likes', 'comments', 'shares',
            ]),
            'maxResults' => 50,
            'dimensions' => 'day',
            'startDate' => ($this->startDate ?? now()->subWeek())->toDateString(),
            'endDate' => ($this->endDate ?? now())->toDateString(),
            'ids' => sprintf('channel==%s', $youtubeChannel->external_id),
        ];

        $response = Http::withToken($token)
            ->get(
                url: 'https://youtubeanalytics.googleapis.com/v2/reports',
                query: $parameters
            );

        if (! $response->successful()) {
            throw CannotGetChannelReportsException::becauseOfHttpErrorWithStatusCode(
                youtubeChannel: $youtubeChannel,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
