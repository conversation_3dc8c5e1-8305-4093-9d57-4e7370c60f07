<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Youtube\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Youtube\Support\Exceptions\CannotGetChannelVideoDetailsException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetChannelVideoDetails
{
    /**
     * @throws CannotGetChannelVideoDetailsException
     * @throws ConnectionException
     */
    public function execute(
        YoutubeChannel $youtubeChannel,
        array $ids
    ): array {
        $response = $this->getResponse($youtubeChannel, $ids);

        return $response->json();
    }

    /**
     * @throws CannotGetChannelVideoDetailsException
     * @throws ConnectionException
     */
    protected function getResponse(
        YoutubeChannel $youtubeChannel,
        array $ids
    ): Response {
        $token = app(RefreshToken::class)->execute($youtubeChannel->youtubeGoogleAccount);

        if (! $token) {
            throw CannotGetChannelVideoDetailsException::becauseOfMissingToken($youtubeChannel, $ids);
        }

        $parameters = [
            'part' => implode(',', [
                'id',
                'statistics',
            ]),
            'id' => implode(',', $ids),
            'maxResults' => 50,
        ];

        $response = Http::withToken($token)->get(
            url: 'https://www.googleapis.com/youtube/v3/videos',
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetChannelVideoDetailsException::becauseOfHttpErrorWithStatusCode(
                youtubeChannel: $youtubeChannel,
                ids: $ids,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
