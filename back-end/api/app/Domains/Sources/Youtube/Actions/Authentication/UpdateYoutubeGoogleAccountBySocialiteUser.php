<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Slack\Messages\NewYoutubeAccountConnectedMessage;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Support\SlackNotification;
use Laravel\Socialite\Two\User;

class UpdateYoutubeGoogleAccountBySocialiteUser
{
    public function execute(User $user, ?array $scopes = null): YoutubeGoogleAccount
    {
        $youtubeAccount = YoutubeGoogleAccount::where('external_id', $user->getId())->first();

        if (! $youtubeAccount) {
            $youtubeAccount = new YoutubeGoogleAccount;
        }

        $youtubeAccount->external_id = $user->getId();
        $youtubeAccount->email = $user->getEmail();
        $youtubeAccount->name = $user->getName();
        $youtubeAccount->image_url = $user->getAvatar();
        $youtubeAccount->refresh_token = $user->refreshToken;
        $youtubeAccount->token = $user->token;
        $youtubeAccount->token_created_at = now();
        $youtubeAccount->scopes = $scopes;

        $youtubeAccount->save();

        if ($youtubeAccount->wasRecentlyCreated) {
            SlackNotification::send(new NewYoutubeAccountConnectedMessage($youtubeAccount));
        }

        return $youtubeAccount;
    }
}
