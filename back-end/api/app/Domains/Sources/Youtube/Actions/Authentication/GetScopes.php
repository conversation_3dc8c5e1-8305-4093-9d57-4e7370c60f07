<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Support\Facades\Http;

class GetScopes
{
    public const ENDPOINT = 'https://www.googleapis.com/oauth2/v1/tokeninfo';

    public function execute(YoutubeGoogleAccount $youtubeGoogleAccount): array
    {
        $token = app(RefreshToken::class)->execute($youtubeGoogleAccount);

        if (! $token) {
            return [];
        }

        $response = Http::post(self::ENDPOINT, [
            'access_token' => $token,
        ]);

        if (! $response->successful()) {
            return [];
        }

        return explode(' ', $response->json('scope') ?? '');
    }
}
