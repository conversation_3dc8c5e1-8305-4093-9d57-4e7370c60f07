<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions\Authentication;

use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class RefreshToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(YoutubeGoogleAccount $youtubeAccount): ?string
    {
        if (! $this->shouldRefreshToken($youtubeAccount)) {
            return $youtubeAccount->token;
        }

        $token = $this->refreshToken($youtubeAccount);

        $youtubeAccount->token = $token;
        $youtubeAccount->token_created_at = $token ? now() : null;

        $youtubeAccount->save();

        return $token;
    }

    protected function shouldRefreshToken(YoutubeGoogleAccount $youtubeAccount): bool
    {
        if (! $youtubeAccount->token_created_at || ! $youtubeAccount->token) {
            return true;
        }

        $expiryThreshold = now()->subMinutes(55);

        return $youtubeAccount->token_created_at->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function refreshToken(YoutubeGoogleAccount $youtubeAccount): ?string
    {
        $response = Http::asForm()
            ->connectTimeout(10)
            ->post(
                url: 'https://oauth2.googleapis.com/token',
                data: [
                    'client_id' => config('services.youtube.client_id'),
                    'client_secret' => config('services.youtube.client_secret'),
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $youtubeAccount->refresh_token,
                ]
            );

        if (! $response->successful()) {
            return null;
        }

        return $response->json('access_token');
    }
}
