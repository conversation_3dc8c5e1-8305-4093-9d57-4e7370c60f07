<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Actions\Authentication;

use Lara<PERSON>\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\GoogleProvider;

class GenerateOAuthRedirectLink
{
    private string $redirectUrl;

    public function execute(string $redirectUrl): string
    {
        $this->redirectUrl = $redirectUrl;

        return $this
            ->getGoogleProvider()
            ->redirect()
            ->getTargetUrl();
    }

    protected function getGoogleProvider(): GoogleProvider
    {
        /** @var GoogleProvider $provider */
        $provider = Socialite::buildProvider(\Laravel\Socialite\Two\GoogleProvider::class, config('services.youtube'));

        $provider->scopes([
            'https://www.googleapis.com/auth/youtube.readonly',
        ]);
        $provider->with([
            'access_type' => 'offline',
            'prompt' => 'consent select_account',
            'state' => [],
        ]);
        $provider->redirectUrl($this->redirectUrl);
        $provider->stateless();

        return $provider;
    }
}
