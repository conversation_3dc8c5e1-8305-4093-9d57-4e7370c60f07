<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class MailchimpAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $mailchimpAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Mailchimp Account #%s deletion request', $this->mailchimpAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.mailchimp-account-deletion-request-mail',
            with: [
                'mailchimpAccount' => MailchimpAccount::query()->findOrFail($this->mailchimpAccountId),
            ],
        );
    }
}
