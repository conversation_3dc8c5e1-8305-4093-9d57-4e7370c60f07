<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class FacebookAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $facebookAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Facebook Account #%s deletion request', $this->facebookAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.facebook-account-deletion-request-mail',
            with: [
                'facebookAccount' => FacebookAccount::query()->findOrFail($this->facebookAccountId),
            ],
        );
    }
}
