<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class TikTokAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $tikTokAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('TikTok Account #%s deletion request', $this->tikTokAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.tiktok-account-deletion-request-mail',
            with: [
                'tikTokAccount' => TikTokAccount::query()
                    ->withoutGlobalScope(SyncDeactivatedScope::class)
                    ->findOrFail($this->tikTokAccountId),
            ],
        );
    }
}
