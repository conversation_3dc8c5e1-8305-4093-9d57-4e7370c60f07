<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\TikTokBusiness\Models\TikTokBusinessAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class TikTokBusinessAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $tikTokBusinessAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('TikTok Business Account #%s deletion request', $this->tikTokBusinessAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.tiktok-business-account-deletion-request-mail',
            with: [
                'tikTokBusinessAccount' => TikTokBusinessAccount::query()
                    ->withoutGlobalScope(SyncDeactivatedScope::class)
                    ->findOrFail($this->tikTokBusinessAccountId),
            ],
        );
    }
}
