<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class YoutubeGoogleAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $youtubeGoogleAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Youtube Google Account #%s deletion request', $this->youtubeGoogleAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.youtube-google-account-deletion-request-mail',
            with: [
                'youtubeGoogleAccount' => GoogleAccount::query()->findOrFail($this->youtubeGoogleAccountId),
            ],
        );
    }
}
