<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Authentication\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class HelpdeskRequestMail extends Mailable
{
    public function __construct(public User $user, public string $messageSubject, public string $messageContent) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            replyTo: $this->user->email,
            subject: sprintf('Helpdesk request: %s', $this->messageSubject)
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.helpdesk-request-mail',
            with: [
                'user' => $this->user,
                'subject' => $this->messageSubject,
                'content' => $this->messageContent,
            ],
        );
    }
}
