<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class LinkedInAdsAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $linkedInAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('LinkedIn Account #%s deletion request for Ads', $this->linkedInAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.linked-in-ads-account-deletion-request-mail',
            with: [
                'linkedInAccount' => LinkedInAccount::query()->findOrFail($this->linkedInAccountId),
            ],
        );
    }
}
