<?php

declare(strict_types=1);

namespace App\Domains\Settings\Support\Mails;

use App\Domains\Sources\Google\Models\GoogleAccount;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class GoogleAccountDeleteRequestMail extends Mailable
{
    public function __construct(public int $googleAccountId) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            to: explode(',', config('lkq.support.emails') ?? null),
            subject: sprintf('Google Account #%s deletion request', $this->googleAccountId),
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.settings.google-account-deletion-request-mail',
            with: [
                'googleAccount' => GoogleAccount::query()->findOrFail($this->googleAccountId),
            ],
        );
    }
}
