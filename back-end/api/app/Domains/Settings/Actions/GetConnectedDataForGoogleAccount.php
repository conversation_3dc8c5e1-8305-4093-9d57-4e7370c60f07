<?php

declare(strict_types=1);

namespace App\Domains\Settings\Actions;

use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Database\Eloquent\Builder;

class GetConnectedDataForGoogleAccount
{
    public function execute(GoogleAccount $googleAccount): array
    {
        return [
            'google_ad_accounts' => $this->getGoogleAdAccounts($googleAccount),
            'google_analytics_properties' => $this->getGoogleAnalyticsProperties($googleAccount),
        ];
    }

    protected function getGoogleAdAccounts(GoogleAccount $googleAccount): array
    {
        $googleAdAccounts = GoogleAdAccount::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('google_account_id', $googleAccount->id)
            ->whereHas('dataSource')
            ->get();

        return $googleAdAccounts->map(function (GoogleAdAccount $googleAdAccount) use ($googleAccount) {
            return [
                'name' => $googleAdAccount->name,
                'connection_type' => 'Google Account',
                'connection_name' => $googleAccount->name,
                'last_synced_at' => $googleAdAccount->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }

    protected function getGoogleAnalyticsProperties(GoogleAccount $googleAccount): array
    {
        $googleAnalyticsProperties = GoogleAnalyticsProperty::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->whereHas('dataSource')
            ->whereHas('googleAnalyticsAccount', function (Builder $builder) use ($googleAccount) {
                $builder->where('google_account_id', $googleAccount->id);
            })
            ->with(['googleAnalyticsAccount'])
            ->get();

        return $googleAnalyticsProperties->map(function (GoogleAnalyticsProperty $googleAnalyticsProperty) {
            return [
                'name' => $googleAnalyticsProperty->name,
                'connection_type' => 'Google Analytics Account',
                'connection_name' => $googleAnalyticsProperty->googleAnalyticsAccount->name,
                'last_synced_at' => $googleAnalyticsProperty->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }
}
