<?php

declare(strict_types=1);

namespace App\Domains\Settings\Actions;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;

class GetConnectedDataForYoutubeGoogleAccount
{
    public function execute(YoutubeGoogleAccount $youtubeGoogleAccount): array
    {
        return [
            'youtube_channels' => $this->getYoutubeChannels($youtubeGoogleAccount),
        ];
    }

    protected function getYoutubeChannels(YoutubeGoogleAccount $youtubeGoogleAccount): array
    {
        $youtubeChannels = YoutubeChannel::query()
            ->where('youtube_google_account_id', $youtubeGoogleAccount->id)
            ->whereHas('dataSource')
            ->get();

        return $youtubeChannels->map(function (YoutubeChannel $youtubeChannel) use ($youtubeGoogleAccount) {
            return [
                'name' => $youtubeChannel->name,
                'connection_type' => 'Google Account',
                'connection_name' => $youtubeGoogleAccount->name,
                'last_synced_at' => $youtubeChannel->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }
}
