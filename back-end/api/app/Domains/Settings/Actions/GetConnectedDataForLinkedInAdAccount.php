<?php

declare(strict_types=1);

namespace App\Domains\Settings\Actions;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Support\Scopes\SyncDeactivatedScope;

class GetConnectedDataForLinkedInAdAccount
{
    public function execute(LinkedInAccount $linkedInAccount): array
    {
        return [
            'linked_in_ad_accounts' => $this->getLinkedInAdAccounts($linkedInAccount),
        ];
    }

    protected function getLinkedInAdAccounts(LinkedInAccount $linkedInAccount): array
    {
        $linkedInAdAccounts = LinkedInAdAccount::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('linkedin_account_id', $linkedInAccount->id)
            ->whereHas('dataSource')
            ->get();

        return $linkedInAdAccounts->map(function (LinkedInAdAccount $linkedInAdAccount) use ($linkedInAccount) {
            return [
                'name' => $linkedInAdAccount->name,
                'connection_type' => 'LinkedIn Account',
                'connection_name' => $linkedInAccount->name,
                'last_synced_at' => $linkedInAdAccount->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }
}
