<?php

declare(strict_types=1);

namespace App\Domains\Settings\Actions;

use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Support\Scopes\SyncDeactivatedScope;

class GetConnectedDataForLinkedInCommunityAccount
{
    public function execute(LinkedInAccount $linkedInAccount): array
    {
        return [
            'linked_in_organisations' => $this->getLinkedInOrganisations($linkedInAccount),
        ];
    }

    protected function getLinkedInOrganisations(LinkedInAccount $linkedInAccount): array
    {
        $linkedInOrganisations = LinkedInOrganisation::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->where('linkedin_account_id', $linkedInAccount->id)
            ->whereHas('dataSource')
            ->get();

        return $linkedInOrganisations->map(function (LinkedInOrganisation $linkedInOrganisation) use ($linkedInAccount) {
            return [
                'name' => $linkedInOrganisation->name,
                'connection_type' => 'LinkedIn Account',
                'connection_name' => $linkedInAccount->name,
                'last_synced_at' => $linkedInOrganisation->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }
}
