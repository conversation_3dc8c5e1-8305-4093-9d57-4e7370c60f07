<?php

declare(strict_types=1);

namespace App\Domains\Settings\Actions;

use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookPage;
use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Database\Eloquent\Builder;

class GetConnectedDataForFacebookAccount
{
    public function execute(FacebookAccount $facebookAccount): array
    {
        return [
            'facebook_ad_accounts' => $this->getFacebookAdAccounts($facebookAccount),
            'facebook_pages' => $this->getFacebookPages($facebookAccount),
            'instagram_accounts' => $this->getInstagramAccounts($facebookAccount),
        ];
    }

    protected function getFacebookAdAccounts(FacebookAccount $facebookAccount): array
    {
        $facebookAdAccounts = FacebookAdAccount::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->whereHas('dataSource')
            ->with(['facebookAccount', 'facebookBusiness'])
            ->where(function (Builder $builder) use ($facebookAccount) {
                $builder
                    ->whereHas('facebookAccount', function (Builder $builder) use ($facebookAccount) {
                        $builder->where('id', $facebookAccount->id);
                    })
                    ->orWhereHas('facebookBusiness', function ($builder) use ($facebookAccount) {
                        $builder->where('facebook_account_id', $facebookAccount->id);
                    });
            })
            ->get();

        return $facebookAdAccounts->map(function (FacebookAdAccount $facebookAdAccount) {
            $connectionType = $facebookAdAccount->facebook_account_id !== null
                ? 'Facebook Account'
                : 'Facebook Business';

            return [
                'name' => $facebookAdAccount->name,
                'connection_type' => $connectionType,
                'connection_name' => $facebookAdAccount->facebookAccount?->name ?? $facebookAdAccount->facebookBusiness?->name,
                'last_synced_at' => $facebookAdAccount->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }

    protected function getFacebookPages(FacebookAccount $facebookAccount): array
    {
        $facebookPages = FacebookPage::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->whereHas('dataSource')
            ->with(['facebookAccount', 'facebookBusiness'])
            ->where(function (Builder $builder) use ($facebookAccount) {
                $builder
                    ->whereHas('facebookAccount', function (Builder $builder) use ($facebookAccount) {
                        $builder->where('id', $facebookAccount->id);
                    })
                    ->orWhereHas('facebookBusiness', function ($builder) use ($facebookAccount) {
                        $builder->where('facebook_account_id', $facebookAccount->id);
                    });
            })
            ->get();

        return $facebookPages->map(function (FacebookPage $facebookPage) {
            $connectionType = $facebookPage->facebook_account_id !== null
                ? 'Facebook Account'
                : 'Facebook Business';

            return [
                'name' => $facebookPage->name,
                'connection_type' => $connectionType,
                'connection_name' => $facebookPage->facebookAccount?->name ?? $facebookPage->facebookBusiness?->name,
                'last_synced_at' => $facebookPage->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }

    protected function getInstagramAccounts(FacebookAccount $facebookAccount): array
    {
        $instagramAccounts = InstagramAccount::query()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->with(['facebookPage'])
            ->whereHas('dataSource')
            ->whereHas('facebookPage', function (Builder $builder) use ($facebookAccount) {
                $builder
                    ->whereHas('facebookAccount', function (Builder $builder) use ($facebookAccount) {
                        $builder->where('id', $facebookAccount->id);
                    })
                    ->orWhereHas('facebookBusiness', function ($builder) use ($facebookAccount) {
                        $builder->where('facebook_account_id', $facebookAccount->id);
                    });
            })
            ->get();

        return $instagramAccounts->map(function (InstagramAccount $instagramAccount) {
            return [
                'username' => $instagramAccount->username,
                'connection_type' => 'Facebook Page',
                'connection_name' => $instagramAccount->facebookPage->name,
                'last_synced_at' => $instagramAccount->facebookPage->last_synced_at?->toDateTimeString(),
            ];
        })->all();
    }
}
