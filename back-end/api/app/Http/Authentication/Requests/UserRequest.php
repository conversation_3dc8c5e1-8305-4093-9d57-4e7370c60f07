<?php

declare(strict_types=1);

namespace App\Http\Authentication\Requests;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Enums\Role;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    public function rules(): array
    {
        $uniqueEmailRule = Rule::unique('users', 'email');

        if ($this->route('user')) {
            /** @var User $user */
            $user = $this->route('user');
            $uniqueEmailRule->ignoreModel($user);
        }

        return [
            'name' => ['required', 'string'],
            'email' => ['required', 'email', $uniqueEmailRule],
            'role' => ['required', Rule::enum(Role::class)],
            'dashboards' => ['sometimes', 'array'],
            'dashboards.*' => ['int', Rule::exists('dashboards', 'id')],
        ];
    }

    public function getName(): string
    {
        return $this->input('name');
    }

    public function getEmail(): string
    {
        return $this->input('email');
    }

    public function getRole(): Role
    {
        return Role::from($this->input('role'));
    }

    public function getDashboards(): array
    {
        return $this->input('dashboards') ?? [];
    }
}
