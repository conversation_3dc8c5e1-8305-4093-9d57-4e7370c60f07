<?php

declare(strict_types=1);

namespace App\Http\Authentication\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VerifyTwoFactorRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'size:8'],
        ];
    }

    public function getCode(): string
    {
        return $this->input('code');
    }
}
