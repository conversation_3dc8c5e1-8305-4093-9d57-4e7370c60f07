<?php

declare(strict_types=1);

namespace App\Http\Authentication\Resources;

use App\Domains\Authentication\Models\User;
use App\Domains\Dashboard\Models\Dashboard;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /** @var User */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false,
        protected bool $allDashboards = true
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
            'role' => $this->resource->role,
            'dashboards' => $this->resource->dashboards,
        ];
    }

    protected function getMetadata(): array
    {
        $scopes = [];
        if ($this->allDashboards) {
            $scopes = ['user_dashboards'];
        }

        $dashboards = Dashboard::query()
            ->withoutGlobalScopes($scopes)
            ->orderBy('title')
            ->get()
            ->map(fn (Dashboard $dashboard) => [
                'id' => $dashboard->id,
                'title' => $dashboard->title,
                'sort' => $dashboard->sort,
            ]);

        return [
            'metadata' => [
                'dashboards' => $dashboards,
            ],
        ];
    }
}
