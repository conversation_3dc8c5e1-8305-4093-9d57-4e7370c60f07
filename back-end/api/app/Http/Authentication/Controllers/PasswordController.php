<?php

declare(strict_types=1);

namespace App\Http\Authentication\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Mails\PasswordResetMail;
use App\Http\Authentication\Requests\PasswordResetLinkRequest;
use App\Http\Authentication\Requests\PasswordResetRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;

class PasswordController extends Controller
{
    public function request(PasswordResetLinkRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = User::query()
            ->where('email', $request->getEmail())
            ->first();

        if (! $user) {
            return response()->json();
        }

        $token = Password::createToken($user);

        $mail = new PasswordResetMail($user->email, $token);
        Mail::queue($mail);

        return response()->json();
    }

    public function reset(PasswordResetRequest $request): JsonResponse
    {
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($request->password),
                ])->save();
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json();
        }

        return response()->json(status: 422);
    }
}
