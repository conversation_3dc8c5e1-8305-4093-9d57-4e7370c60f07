<?php

declare(strict_types=1);

namespace App\Http\Authentication\Controllers;

use App\Domains\Authentication\Models\User;
use App\Domains\Authentication\Support\Mails\AccountCreatedMail;
use App\Http\Authentication\Requests\UserRequest;
use App\Http\Authentication\Resources\UserResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class UserController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $users = User::query()->paginate();

        return UserResource::collection($users);
    }

    public function show(User $user): UserResource
    {
        return new UserResource($user, withMetadata: true);
    }

    public function store(UserRequest $userRequest): UserResource
    {
        $user = new User;

        $user->name = $userRequest->getName();
        $user->email = $userRequest->getEmail();
        $user->role = $userRequest->getRole();

        $user->save();

        $mail = new AccountCreatedMail($user->email);
        Mail::queue($mail);

        return new UserResource($user);
    }

    public function update(User $user, UserRequest $userRequest): UserResource
    {
        $user->name = $userRequest->getName();
        $user->email = $userRequest->getEmail();
        $user->role = $userRequest->getRole();
        $user->dashboards = $userRequest->getDashboards();

        $user->save();

        return new UserResource($user);
    }

    public function destroy(User $user): JsonResponse
    {
        $user->delete();

        return response()->json(null, 204);
    }
}
