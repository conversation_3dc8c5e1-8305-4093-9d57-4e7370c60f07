<?php

declare(strict_types=1);

namespace App\Http\Authentication\Controllers;

use App\Domains\Authentication\Actions\GetUserFromCredentials;
use App\Domains\Authentication\Actions\SendTwoFactorCode;
use App\Http\Authentication\Requests\LoginRequest;
use App\Http\Authentication\Resources\UserResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        $user = app(GetUserFromCredentials::class)->execute(
            $request->getEmail(),
            $request->getPassword(),
        );

        if (! $user) {
            abort(401);
        }

        Auth::login($user);

        session()->regenerate();

        if ($user->two_factor_enabled) {
            app(SendTwoFactorCode::class)->execute($user);

            return response()->json([
                'two_factor_required' => true,
            ]);
        }

        Session::put('two_factor_authenticated', true);

        return response()->json(['two_factor_required' => false]);
    }

    public function logout(): JsonResponse
    {
        session()->invalidate();

        return response()->json();
    }

    public function me(): UserResource
    {
        return new UserResource(auth()->user(), withMetadata: true, allDashboards: false);
    }
}
