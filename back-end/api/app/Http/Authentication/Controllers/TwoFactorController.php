<?php

declare(strict_types=1);

namespace App\Http\Authentication\Controllers;

use App\Http\Authentication\Requests\VerifyTwoFactorRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;

class TwoFactorController extends Controller
{
    public function verify(VerifyTwoFactorRequest $request): JsonResponse
    {
        $user = auth()->user();

        $code = $user->two_factor_code;
        $expiresAt = $user->two_factor_expires_at;

        if (! $user->two_factor_enabled) {
            return response()->json(['message' => '2FA not enabled for this user'], 400);
        }

        $user->two_factor_code = null;
        $user->two_factor_expires_at = null;
        $user->save();

        if ($code !== $request->getCode() || now()->greaterThan($expiresAt)) {
            return response()->json(['message' => 'Invalid or expired verification code'], 401);
        }

        Session::put('two_factor_authenticated', true);

        return response()->json(['message' => 'Two-factor authentication successful']);
    }
}
