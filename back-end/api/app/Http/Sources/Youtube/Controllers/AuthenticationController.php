<?php

declare(strict_types=1);

namespace App\Http\Sources\Youtube\Controllers;

use App\Domains\Sources\Youtube\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\Youtube\Actions\Authentication\UpdateYoutubeGoogleAccountBySocialiteUser;
use App\Http\Sources\Youtube\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connect(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            redirectUrl: route('sources.youtube.callback')
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callback(AuthenticationCallbackRequest $request): RedirectResponse
    {
        $requiredScopes = [
            'https://www.googleapis.com/auth/youtube.readonly',
            'https://www.googleapis.com/auth/userinfo.profile',
            'https://www.googleapis.com/auth/userinfo.email',
        ];

        if (! empty(array_diff($requiredScopes, $request->getScopes()))) {
            return response()
                ->redirectTo(config('app.frontend_url').'/connect?provider=youtube&state=missing_scopes');
        }

        app(UpdateYoutubeGoogleAccountBySocialiteUser::class)->execute(
            user: $request->getSocialiteUser(),
            scopes: $request->getScopes()
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=youtube&state=success');
    }
}
