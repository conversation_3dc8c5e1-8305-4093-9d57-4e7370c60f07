<?php

declare(strict_types=1);

namespace App\Http\Sources\TikTokBusiness\Requests;

use App\Domains\Sources\TikTokBusiness\Support\Providers\TikTokBusinessProvider;
use Illuminate\Foundation\Http\FormRequest;
use Laravel\Socialite\Facades\Socialite;
use SocialiteProviders\Manager\OAuth2\User;

class AuthenticationCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [];
    }

    public function getSocialite(): TikTokBusinessProvider
    {
        /** @var TikTokBusinessProvider $driver */
        $driver = Socialite::driver('tiktok-business');
        $driver->stateless();

        return $driver;
    }

    public function getSocialiteUser(): User
    {
        return $this->getSocialite()->user();
    }
}
