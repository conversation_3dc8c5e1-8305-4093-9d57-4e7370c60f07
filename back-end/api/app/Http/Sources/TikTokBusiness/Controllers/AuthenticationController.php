<?php

declare(strict_types=1);

namespace App\Http\Sources\TikTokBusiness\Controllers;

use App\Domains\Sources\TikTokBusiness\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\TikTokBusiness\Actions\Authentication\UpdateTikTokBusinessAccountBySocialiteUser;
use App\Http\Sources\TikTokBusiness\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connect(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            redirectUrl: config('services.tiktok-business.redirect')
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callback(AuthenticationCallbackRequest $request): RedirectResponse
    {
        app(UpdateTikTokBusinessAccountBySocialiteUser::class)->execute(
            user: $request->getSocialiteUser(),
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=tiktok-business&state=success');
    }
}
