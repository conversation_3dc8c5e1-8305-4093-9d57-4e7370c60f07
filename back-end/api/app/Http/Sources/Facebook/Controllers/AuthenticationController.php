<?php

declare(strict_types=1);

namespace App\Http\Sources\Facebook\Controllers;

use App\Domains\Sources\Meta\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\Meta\Actions\Authentication\UpdateFacebookAccountBySocialiteUser;
use App\Http\Sources\Facebook\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connect(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            redirectUrl: route('sources.facebook.callback')
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callback(AuthenticationCallbackRequest $request): RedirectResponse
    {
        app(UpdateFacebookAccountBySocialiteUser::class)->execute(
            user: $request->getSocialiteUser()
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=facebook&state=success');
    }
}
