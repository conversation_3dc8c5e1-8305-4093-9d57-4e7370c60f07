<?php

declare(strict_types=1);

namespace App\Http\Sources\Facebook\Requests;

use Illuminate\Foundation\Http\FormRequest;
use <PERSON>vel\Socialite\Facades\Socialite;
use <PERSON>vel\Socialite\Two\FacebookProvider;
use <PERSON>vel\Socialite\Two\User;

class AuthenticationCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'code' => ['required', 'string'],
        ];
    }

    public function getSocialite(): FacebookProvider
    {
        /** @var FacebookProvider $driver */
        $driver = Socialite::driver('facebook');
        $driver->stateless();

        return $driver;
    }

    public function getSocialiteUser(): User
    {
        return $this->getSocialite()->user();
    }
}
