<?php

declare(strict_types=1);

namespace App\Http\Sources\LinkedIn\Controllers;

use App\Domains\Sources\LinkedIn\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\LinkedIn\Actions\Authentication\UpdateLinkedInAccountByCode;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use App\Http\Sources\LinkedIn\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connectCommunity(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            accountType: LinkedInAccountType::COMMUNITY
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callbackCommunity(AuthenticationCallbackRequest $request): RedirectResponse
    {
        app(UpdateLinkedInAccountByCode::class)->execute(
            code: $request->getCode(),
            accountType: LinkedInAccountType::COMMUNITY
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=linkedin-community&state=success');
    }

    public function connectAds(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            accountType: LinkedInAccountType::ADS
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callbackAds(AuthenticationCallbackRequest $request): RedirectResponse
    {
        app(UpdateLinkedInAccountByCode::class)->execute(
            code: $request->getCode(),
            accountType: LinkedInAccountType::ADS
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=linkedin-ads&state=success');
    }
}
