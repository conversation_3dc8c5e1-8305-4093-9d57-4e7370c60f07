<?php

declare(strict_types=1);

namespace App\Http\Sources\Google\Controllers;

use App\Domains\Sources\Google\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\Google\Actions\Authentication\UpdateGoogleAccountBySocialiteUser;
use App\Http\Sources\Google\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connect(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            redirectUrl: route('sources.google.callback')
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callback(AuthenticationCallbackRequest $request): RedirectResponse
    {
        $requiredScopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/userinfo.profile',
            'https://www.googleapis.com/auth/userinfo.email',
        ];

        if (! empty(array_diff($requiredScopes, $request->getScopes()))) {
            return response()
                ->redirectTo(config('app.frontend_url').'/connect?provider=google&state=missing_scopes');
        }

        app(UpdateGoogleAccountBySocialiteUser::class)->execute(
            user: $request->getSocialiteUser(),
            scopes: $request->getScopes()
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=google&state=success');
    }
}
