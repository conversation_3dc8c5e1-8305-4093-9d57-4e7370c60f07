<?php

declare(strict_types=1);

namespace App\Http\Sources\Google\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Socialite\Facades\Socialite;
use <PERSON>vel\Socialite\Two\GoogleProvider;
use <PERSON>vel\Socialite\Two\User;

class AuthenticationCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'authuser' => ['required', 'string'],
            'scope' => ['required', 'string'],
        ];
    }

    public function getSocialite(): GoogleProvider
    {
        /** @var GoogleProvider $driver */
        $driver = Socialite::driver('google');
        $driver->stateless();

        return $driver;
    }

    public function getSocialiteUser(): User
    {
        return $this->getSocialite()->user();
    }

    public function getScopes(): array
    {
        $scopes = explode(' ', request()->input('scope'));

        return array_values(
            array_filter($scopes, function ($item) {
                return Str::startsWith($item, 'https://');
            }),
        );
    }
}
