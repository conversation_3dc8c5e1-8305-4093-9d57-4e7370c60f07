<?php

declare(strict_types=1);

namespace App\Http\Sources\TikTok\Controllers;

use App\Domains\Sources\TikTok\Actions\Authentication\GenerateOAuthRedirectLink;
use App\Domains\Sources\TikTok\Actions\Authentication\UpdateTikTokAccountBySocialiteUser;
use App\Http\Sources\TikTok\Requests\AuthenticationCallbackRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AuthenticationController extends Controller
{
    public function connect(): JsonResponse
    {
        $url = app(GenerateOAuthRedirectLink::class)->execute(
            redirectUrl: route('sources.tiktok.callback')
        );

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    public function callback(AuthenticationCallbackRequest $request): RedirectResponse
    {
        $requiredScopes = [
            'user.info.basic',
            'user.info.profile',
            'user.info.stats',
            'video.list',
        ];

        if (! empty(array_diff($requiredScopes, $request->getScopes()))) {
            return response()
                ->redirectTo(config('app.frontend_url').'/connect?provider=tiktok-organic&state=missing_scopes');
        }

        app(UpdateTikTokAccountBySocialiteUser::class)->execute(
            user: $request->getSocialiteUser(),
            scopes: $request->getScopes()
        );

        return response()
            ->redirectTo(config('app.frontend_url').'/connect?provider=tiktok-organic&state=success');
    }
}
