<?php

declare(strict_types=1);

namespace App\Http\Sources\TikTok\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Laravel\Socialite\Facades\Socialite;
use SocialiteProviders\Manager\OAuth2\User;
use SocialiteProviders\TikTok\Provider as TikTokProvider;

class AuthenticationCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [];
    }

    public function getSocialite(): TikTokProvider
    {
        /** @var TikTokProvider $driver */
        $driver = Socialite::driver('tiktok');
        $driver->stateless();

        return $driver;
    }

    public function getSocialiteUser(): User
    {
        return $this->getSocialite()->user();
    }

    public function getScopes(): array
    {
        return explode(',', request()->input('scopes'));
    }
}
