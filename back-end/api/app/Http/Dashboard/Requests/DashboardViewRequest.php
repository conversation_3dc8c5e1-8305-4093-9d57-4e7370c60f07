<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Requests;

class DashboardViewRequest extends WidgetStatisticsRequest
{
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => ['required', 'string'],
            ]
        );
    }

    public function getName(): string
    {
        return $this->input('name');
    }
}
