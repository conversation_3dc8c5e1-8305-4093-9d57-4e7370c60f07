<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Requests;

use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use Carbon\CarbonInterface;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class WidgetStatisticsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'start_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'end_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'channels' => ['sometimes', 'nullable', 'string'],
            'regions' => ['sometimes', 'nullable', 'string'],
            'business_units' => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function toWidgetAnalyticsFilter(): WidgetAnalyticsFilter
    {
        $filter = new WidgetAnalyticsFilter;

        $filter->setStartDate($this->getStartDate());
        $filter->setEndDate($this->getEndDate()?->endOfDay());
        $filter->setChannels($this->getChannels());
        $filter->setRegions($this->getRegions());
        $filter->setBusinessUnits($this->getBusinessUnits());

        return $filter;
    }

    public function getStartDate(): ?CarbonInterface
    {
        $startDate = $this->input('start_date');

        if (! $startDate) {
            return null;
        }

        return Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
    }

    public function getEndDate(): ?CarbonInterface
    {
        $endDate = $this->input('end_date');

        if (! $endDate) {
            return null;
        }

        return Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
    }

    public function getChannels(): ?array
    {
        $value = $this->input('channels');

        if (! $value) {
            return null;
        }

        return explode(',', $value);
    }

    public function getRegions(): ?array
    {
        $value = $this->input('regions');

        if (! $value) {
            return null;
        }

        return explode(',', $value);
    }

    public function getBusinessUnits(): ?array
    {
        $value = $this->input('business_units');

        if (! $value) {
            return null;
        }

        return explode(',', $value);
    }
}
