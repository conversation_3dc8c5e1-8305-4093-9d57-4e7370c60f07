<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Support\Controller;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class FileController extends Controller
{
    public function show(string $path): BinaryFileResponse
    {
        $path = ltrim(str_replace('..', '', $path), '/');

        // Check if file exists
        if (! Storage::exists($path)) {
            throw new NotFoundHttpException('File not found.');
        }

        return response()->file(Storage::path($path));
    }
}
