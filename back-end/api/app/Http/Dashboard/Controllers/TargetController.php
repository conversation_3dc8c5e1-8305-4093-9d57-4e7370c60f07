<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Models\Widget;
use App\Http\Dashboard\Requests\UpdateWidgetTargetRequest;
use App\Http\Dashboard\Resources\WidgetResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;

class TargetController extends Controller
{
    public function update(Widget $widget, UpdateWidgetTargetRequest $request): WidgetResource
    {
        $widget->update([
            'target' => $request->getTarget(),
        ]);

        return new WidgetResource($widget->fresh());
    }

    public function destroy(Widget $widget): JsonResponse
    {
        $widget->update([
            'target' => null,
        ]);

        return response()->json(['message' => 'Target value cleared successfully']);
    }
}
