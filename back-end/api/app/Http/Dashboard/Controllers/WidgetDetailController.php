<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Actions\Widgets\GetStatisticsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Http\Dashboard\Requests\WidgetStatisticsRequest;
use App\Http\Dashboard\Resources\WidgetAnalyticsResource;
use App\Http\Dashboard\Resources\WidgetPieAnalyticsResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;

class WidgetDetailController extends Controller
{
    public function statistics(Widget $widget, WidgetDetailScope $widgetDetailScope, WidgetStatisticsRequest $request): JsonResponse
    {
        $dataSet = app(GetStatisticsForWidget::class)->detail(
            widget: $widget,
            widgetAnalyticsFilter: $request->toWidgetAnalyticsFilter(),
            widgetDetailScope: $widgetDetailScope
        );

        return response()->json([
            'data' => array_map(function (WidgetAnalytics|WidgetPieAnalytics $widgetAnalytics) {
                if ($widgetAnalytics instanceof WidgetPieAnalytics) {
                    return new WidgetPieAnalyticsResource($widgetAnalytics);
                }

                return new WidgetAnalyticsResource($widgetAnalytics);
            }, $dataSet),
        ]);
    }
}
