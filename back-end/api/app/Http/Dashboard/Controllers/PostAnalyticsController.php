<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Actions\Posts\GetStatisticsForPost;
use App\Domains\Dashboard\Models\Widget;
use App\Http\Dashboard\Requests\WidgetStatisticsRequest;
use App\Http\Dashboard\Resources\PostAnalyticsResource;
use App\Support\Controller;

class PostAnalyticsController extends Controller
{
    public function statistics(Widget $widget, int $postId, WidgetStatisticsRequest $request): PostAnalyticsResource
    {
        $postAnalytics = app(GetStatisticsForPost::class)->execute($widget, $postId, $request->toWidgetAnalyticsFilter());

        return PostAnalyticsResource::make($postAnalytics);
    }
}
