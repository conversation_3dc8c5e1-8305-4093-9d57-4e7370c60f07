<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Actions\Widgets\GetStatisticsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use App\Http\Dashboard\Requests\WidgetStatisticsRequest;
use App\Http\Dashboard\Resources\WidgetAnalyticsResource;
use App\Http\Dashboard\Resources\WidgetPieAnalyticsResource;
use App\Http\Dashboard\Resources\WidgetPostAnalyticsResource;
use App\Support\Controller;

class WidgetController extends Controller
{
    public function statistics(Widget $widget, WidgetStatisticsRequest $request): WidgetAnalyticsResource|WidgetPostAnalyticsResource|WidgetPieAnalyticsResource
    {
        $widgetAnalytics = app(GetStatisticsForWidget::class)->single(
            widget: $widget,
            widgetAnalyticsFilter: $request->toWidgetAnalyticsFilter()
        );

        if ($widgetAnalytics instanceof WidgetPostAnalytics) {
            return new WidgetPostAnalyticsResource($widgetAnalytics);
        }

        if ($widgetAnalytics instanceof WidgetPieAnalytics) {
            return new WidgetPieAnalyticsResource($widgetAnalytics);
        }

        return new WidgetAnalyticsResource($widgetAnalytics);
    }
}
