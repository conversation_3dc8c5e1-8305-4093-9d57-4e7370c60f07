<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Actions\Dashboard\StoreDashboardViewForDashboard;
use App\Domains\Dashboard\Models\Dashboard;
use App\Domains\Dashboard\Models\DashboardView;
use App\Http\Dashboard\Requests\DashboardViewRequest;
use App\Http\Dashboard\Resources\DashboardViewResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;

class DashboardViewController extends Controller
{
    public function store(Dashboard $dashboard, DashboardViewRequest $request): DashboardViewResource
    {
        $view = app(StoreDashboardViewForDashboard::class)->execute(
            dashboard: $dashboard,
            name: $request->getName(),
            widgetAnalyticsFilter: $request->toWidgetAnalyticsFilter()
        );

        return DashboardViewResource::make($view);
    }

    public function destroy(Dashboard $dashboard, DashboardView $dashboardView): JsonResponse
    {
        if ($dashboardView->dashboard_id !== $dashboard->id) {
            abort(403);
        }

        $dashboardView->forceDelete();

        return response()->json(null, 204);
    }
}
