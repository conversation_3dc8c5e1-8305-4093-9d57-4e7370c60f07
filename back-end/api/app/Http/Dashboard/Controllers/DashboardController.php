<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Models\Dashboard;
use App\Http\Dashboard\Resources\DashboardResource;
use App\Support\Controller;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DashboardController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $dashboards = Dashboard::query()->get();

        return DashboardResource::collection($dashboards);
    }

    public function show(Dashboard $dashboard): DashboardResource
    {
        $dashboard->loadMissing([
            'dataSources',
            'dashboardViews',
            'sections',
            'sections.widgets',
        ]);

        return new DashboardResource($dashboard, withMetadata: true);
    }
}
