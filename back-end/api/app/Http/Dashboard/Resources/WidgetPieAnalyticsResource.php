<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Support\Dto\WidgetPieAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetPieAnalyticsDataPoint;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetPieAnalyticsResource extends JsonResource
{
    /** @var WidgetPieAnalytics */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'values' => array_map(function (WidgetPieAnalyticsDataPoint $dataPoint) {
                return [
                    'value' => $dataPoint->getValue(),
                    'valueFormatted' => $dataPoint->getValueFormatted(),
                    'name' => $dataPoint->getName(),
                    'comparison' => $dataPoint->getComparison(),
                    'comparisonFormatted' => $dataPoint->getComparisonFormatted(),
                ];
            }, $this->resource->getData()),
            'total' => $this->resource->getTotal(),
            'totalFormatted' => $this->resource->getTotalFormatted(),
            'comparison' => $this->resource->getComparison(),
            'comparisonFormatted' => $this->resource->getComparisonFormatted(),
            'accuracy' => $this->resource->getWidgetAccuracy()->value,
        ];
    }
}
