<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Support\Dto\PostAnalytics;
use App\Domains\Dashboard\Support\Dto\PostAnalyticsEntry;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostAnalyticsResource extends JsonResource
{
    /** @var PostAnalytics */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'url' => $this->resource->getUrl(),
            'text' => $this->resource->getText(),
            'channel' => $this->resource->getChannel(),
            'entries' => array_map(function (PostAnalyticsEntry $entry) {
                return [
                    'name' => $entry->getName(),
                    'value' => $entry->getValue(),
                    'valueFormatted' => $entry->getValueFormatted(),
                    'comparison' => $entry->getComparison(),
                    'comparisonFormatted' => $entry->getComparisonFormatted(),
                ];
            }, $this->resource->getEntries()),
        ];
    }
}
