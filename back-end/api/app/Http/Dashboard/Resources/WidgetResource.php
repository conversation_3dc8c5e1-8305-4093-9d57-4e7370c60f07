<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetResource extends JsonResource
{
    /** @var Widget */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'title' => $this->resource->title,
            'data_type' => $this->resource->data_type,
            'type' => $this->resource->type,
            'color' => $this->resource->color,
            'target' => $this->resource->target,
            'inverse_comparison_indicator' => $this->resource->inverse_comparison_indicator,
            'section' => SectionResource::make($this->whenLoaded('section')),
        ];
    }
}
