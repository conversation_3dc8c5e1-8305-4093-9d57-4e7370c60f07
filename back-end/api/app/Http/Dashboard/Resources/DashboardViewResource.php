<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\DashboardView;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardViewResource extends JsonResource
{
    /** @var DashboardView */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'dashboard_id' => $this->resource->dashboard_id,
            'name' => $this->resource->name,
            'start_date' => $this->resource->start_date?->toDateString(),
            'end_date' => $this->resource->end_date?->toDateString(),
            'channels' => $this->resource->channels,
            'regions' => $this->resource->regions,
            'business_units' => $this->resource->business_units,
        ];
    }
}
