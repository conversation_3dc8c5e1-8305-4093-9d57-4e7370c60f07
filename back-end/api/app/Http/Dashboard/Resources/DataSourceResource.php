<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\DataSource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DataSourceResource extends JsonResource
{
    /** @var DataSource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'dashboards' => DashboardResource::collection($this->whenLoaded('dashboards')),
        ];
    }
}
