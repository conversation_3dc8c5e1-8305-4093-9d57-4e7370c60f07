<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\Dashboard;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardResource extends JsonResource
{
    /** @var Dashboard */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'title' => $this->resource->title,
            'sections' => SectionResource::collection($this->whenLoaded('sections')),
            'data_sources' => DataSourceResource::collection($this->whenLoaded('dataSources')),
        ];
    }

    protected function getMetadata(): array
    {
        $regions = [];
        $businessUnits = [];
        $channels = [];

        foreach ($this->resource->dataSources as $dataSource) {
            $regions[] = $dataSource->region;
            $businessUnits[] = $dataSource->business_unit;
            $channels[] = $dataSource->channel;
        }

        return [
            'metadata' => [
                'regions' => array_values(array_unique($regions)),
                'business_units' => array_values(array_unique($businessUnits)),
                'channels' => array_values(array_unique($channels)),
                'views' => DashboardViewResource::collection($this->resource->dashboardViews),
            ],
        ];
    }
}
