<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SectionResource extends JsonResource
{
    /** @var Section */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'title' => $this->resource->title,
            'icon' => $this->resource->icon,
            'widgets' => WidgetResource::collection($this->whenLoaded('widgets')),
            'dashboard' => DashboardResource::make($this->whenLoaded('dashboard')),
        ];
    }
}
