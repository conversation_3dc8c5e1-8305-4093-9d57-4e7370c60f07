<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Support\Dto\WidgetPostAnalytics;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetPostAnalyticsResource extends JsonResource
{
    /** @var WidgetPostAnalytics */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'post' => [
                'url' => $this->resource->getUrl(),
                'text' => $this->resource->getText(),
            ],
            'total' => $this->resource->getTotal(),
            'totalFormatted' => $this->resource->getTotalFormatted(),
            'comparison' => $this->resource->getComparison(),
            'comparisonFormatted' => $this->resource->getComparisonFormatted(),
        ];
    }
}
