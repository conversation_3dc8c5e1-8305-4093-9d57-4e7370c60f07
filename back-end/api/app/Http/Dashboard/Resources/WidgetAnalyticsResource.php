<?php

declare(strict_types=1);

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetAnalyticsResource extends JsonResource
{
    /** @var WidgetAnalytics */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'values' => array_map(function (WidgetAnalyticsDataPoint $dataPoint) {
                return [
                    'date' => $dataPoint->getDate()?->toDateTimeString(),
                    'value' => $dataPoint->getValue(),
                    'valueFormatted' => $dataPoint->getValueFormatted(),
                ];
            }, $this->resource->getData()),
            'total' => $this->resource->getTotal(),
            'totalFormatted' => $this->resource->getTotalFormatted(),
            'comparison' => $this->resource->getComparison(),
            'comparisonFormatted' => $this->resource->getComparisonFormatted(),
            'accuracy' => $this->resource->getWidgetAccuracy()->value,
        ];
    }
}
