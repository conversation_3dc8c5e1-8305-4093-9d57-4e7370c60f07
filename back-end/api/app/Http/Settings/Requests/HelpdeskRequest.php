<?php

declare(strict_types=1);

namespace App\Http\Settings\Requests;

use Illuminate\Foundation\Http\FormRequest;

class HelpdeskRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'subject' => ['required', 'string'],
            'message' => ['required', 'string'],
        ];
    }

    public function getSubject(): string
    {
        return $this->input('subject');
    }

    public function getMessage(): string
    {
        return $this->input('message');
    }
}
