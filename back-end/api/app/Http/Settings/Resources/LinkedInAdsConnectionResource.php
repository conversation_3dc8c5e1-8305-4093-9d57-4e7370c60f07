<?php

declare(strict_types=1);

namespace App\Http\Settings\Resources;

use App\Domains\Settings\Actions\GetConnectedDataForLinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LinkedInAdsConnectionResource extends JsonResource
{
    /** @var LinkedInAccount */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
        ];
    }

    protected function getMetadata(): array
    {
        return [
            'metadata' => [
                'connected_data' => app(GetConnectedDataForLinkedInAdAccount::class)->execute($this->resource),
            ],
        ];
    }
}
