<?php

declare(strict_types=1);

namespace App\Http\Settings\Resources;

use App\Domains\Sources\TikTok\Models\TikTokAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TikTokConnectionResource extends JsonResource
{
    /** @var TikTokAccount */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
        ];
    }

    protected function getMetadata(): array
    {
        return [
            'metadata' => [],
        ];
    }
}
