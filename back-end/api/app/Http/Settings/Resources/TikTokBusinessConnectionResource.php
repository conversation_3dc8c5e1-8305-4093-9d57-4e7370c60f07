<?php

declare(strict_types=1);

namespace App\Http\Settings\Resources;

use App\Domains\Sources\TikTokBusiness\Models\TikTokBusinessAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TikTokBusinessConnectionResource extends JsonResource
{
    /** @var TikTokBusinessAccount */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
        ];
    }

    protected function getMetadata(): array
    {
        return [
            'metadata' => [],
        ];
    }
}
