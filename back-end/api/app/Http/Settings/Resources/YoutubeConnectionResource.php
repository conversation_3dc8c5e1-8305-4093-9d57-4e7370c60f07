<?php

declare(strict_types=1);

namespace App\Http\Settings\Resources;

use App\Domains\Settings\Actions\GetConnectedDataForYoutubeGoogleAccount;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class YoutubeConnectionResource extends JsonResource
{
    /** @var YoutubeGoogleAccount */
    public $resource;

    public function __construct(
        $resource,
        protected $iterationCount = null,
        protected bool $withMetadata = false
    ) {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        if ($this->withMetadata) {
            $this->with = $this->getMetadata();
        }

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
        ];
    }

    protected function getMetadata(): array
    {
        return [
            'metadata' => [
                'connected_data' => app(GetConnectedDataForYoutubeGoogleAccount::class)->execute($this->resource),
            ],
        ];
    }
}
