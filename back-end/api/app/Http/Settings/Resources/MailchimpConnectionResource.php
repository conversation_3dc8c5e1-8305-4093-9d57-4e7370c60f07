<?php

declare(strict_types=1);

namespace App\Http\Settings\Resources;

use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MailchimpConnectionResource extends JsonResource
{
    /** @var MailchimpAccount */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'region' => $this->resource->region,
        ];
    }
}
