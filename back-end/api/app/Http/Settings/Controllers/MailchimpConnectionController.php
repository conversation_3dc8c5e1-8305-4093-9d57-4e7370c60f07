<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\MailchimpAccountDeleteRequestMail;
use App\Domains\Sources\Mailchimp\Models\MailchimpAccount;
use App\Http\Settings\Resources\MailchimpConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class MailchimpConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $mailchimpAccounts = MailchimpAccount::query()->paginate();

        return MailchimpConnectionResource::collection($mailchimpAccounts);
    }

    public function destroy(MailchimpAccount $mailchimpAccount): JsonResponse
    {
        $mail = new MailchimpAccountDeleteRequestMail($mailchimpAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
