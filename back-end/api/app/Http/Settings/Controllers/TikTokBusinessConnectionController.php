<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\TikTokBusinessAccountDeleteRequestMail;
use App\Domains\Sources\TikTokBusiness\Models\TikTokBusinessAccount;
use App\Http\Settings\Resources\TikTokBusinessConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class TikTokBusinessConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $tikTokBusinessAccounts = TikTokBusinessAccount::query()->paginate();

        return TikTokBusinessConnectionResource::collection($tikTokBusinessAccounts);
    }

    public function destroy(TikTokBusinessAccount $tikTokBusinessAccount): JsonResponse
    {
        $mail = new TikTokBusinessAccountDeleteRequestMail($tikTokBusinessAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
