<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\LinkedInAdsAccountDeleteRequestMail;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use App\Http\Settings\Resources\LinkedInAdsConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class LinkedInAdsConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $linkedInAccounts = LinkedInAccount::query()->where('type', LinkedInAccountType::ADS)->paginate();

        return LinkedInAdsConnectionResource::collection($linkedInAccounts);
    }

    public function show(LinkedInAccount $linkedInAccount): LinkedInAdsConnectionResource
    {
        return new LinkedInAdsConnectionResource($linkedInAccount, withMetadata: true);
    }

    public function destroy(LinkedInAccount $linkedInAccount): JsonResponse
    {
        $mail = new LinkedInAdsAccountDeleteRequestMail($linkedInAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
