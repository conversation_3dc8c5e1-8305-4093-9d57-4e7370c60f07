<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\GoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\GoogleAccount;
use App\Http\Settings\Resources\GoogleConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class GoogleConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $googleAccounts = GoogleAccount::query()->paginate();

        return GoogleConnectionResource::collection($googleAccounts);
    }

    public function show(GoogleAccount $googleAccount): GoogleConnectionResource
    {
        return new GoogleConnectionResource($googleAccount, withMetadata: true);
    }

    public function destroy(GoogleAccount $googleAccount): JsonResponse
    {
        $mail = new GoogleAccountDeleteRequestMail($googleAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
