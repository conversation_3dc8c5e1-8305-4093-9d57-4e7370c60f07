<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\YoutubeGoogleAccountDeleteRequestMail;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Http\Settings\Resources\YoutubeConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class YoutubeConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $youtubeGoogleAccounts = YoutubeGoogleAccount::query()->paginate();

        return YoutubeConnectionResource::collection($youtubeGoogleAccounts);
    }

    public function show(YoutubeGoogleAccount $youtubeGoogleAccount): YoutubeConnectionResource
    {
        return new YoutubeConnectionResource($youtubeGoogleAccount, withMetadata: true);
    }

    public function destroy(YoutubeGoogleAccount $youtubeGoogleAccount): JsonResponse
    {
        $mail = new YoutubeGoogleAccountDeleteRequestMail($youtubeGoogleAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
