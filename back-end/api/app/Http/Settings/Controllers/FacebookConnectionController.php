<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\FacebookAccountDeleteRequestMail;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Http\Settings\Resources\FacebookConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class FacebookConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $facebookAccounts = FacebookAccount::query()->paginate();

        return FacebookConnectionResource::collection($facebookAccounts);
    }

    public function show(FacebookAccount $facebookAccount): FacebookConnectionResource
    {
        return new FacebookConnectionResource($facebookAccount, withMetadata: true);
    }

    public function destroy(FacebookAccount $facebookAccount): JsonResponse
    {
        $mail = new FacebookAccountDeleteRequestMail($facebookAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
