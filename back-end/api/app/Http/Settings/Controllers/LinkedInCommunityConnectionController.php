<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\LinkedInCommunityAccountDeleteRequestMail;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Support\Enums\LinkedInAccountType;
use App\Http\Settings\Resources\LinkedInCommunityConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class LinkedInCommunityConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $linkedInAccounts = LinkedInAccount::query()->where('type', LinkedInAccountType::COMMUNITY)->paginate();

        return LinkedInCommunityConnectionResource::collection($linkedInAccounts);
    }

    public function show(LinkedInAccount $linkedInAccount): LinkedInCommunityConnectionResource
    {
        return new LinkedInCommunityConnectionResource($linkedInAccount, withMetadata: true);
    }

    public function destroy(LinkedInAccount $linkedInAccount): JsonResponse
    {
        $mail = new LinkedInCommunityAccountDeleteRequestMail($linkedInAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
