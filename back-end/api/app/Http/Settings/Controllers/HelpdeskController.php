<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\HelpdeskRequestMail;
use App\Http\Settings\Requests\HelpdeskRequest;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;

class HelpdeskController extends Controller
{
    public function store(HelpdeskRequest $helpdeskRequest): JsonResponse
    {
        $mail = new HelpdeskRequestMail(
            user: auth()->user(),
            messageSubject: $helpdeskRequest->getSubject(),
            messageContent: $helpdeskRequest->getMessage()
        );
        Mail::queue($mail);

        return response()->json(null);
    }
}
