<?php

declare(strict_types=1);

namespace App\Http\Settings\Controllers;

use App\Domains\Settings\Support\Mails\TikTokAccountDeleteRequestMail;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Http\Settings\Resources\TikTokConnectionResource;
use App\Support\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Mail;

class TikTokConnectionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $tikTokAccounts = TikTokAccount::query()->paginate();

        return TikTokConnectionResource::collection($tikTokAccounts);
    }

    public function destroy(TikTokAccount $tiktokAccount): JsonResponse
    {
        $mail = new TikTokAccountDeleteRequestMail($tiktokAccount->id);
        Mail::queue($mail);

        return response()->json(null);
    }
}
