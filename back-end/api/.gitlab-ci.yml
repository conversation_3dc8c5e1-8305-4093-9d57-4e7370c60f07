.php_image: &php_image registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.7

# Variables
variables:
  MYSQL_ROOT_PASSWORD: password
  MYSQL_DATABASE: laravel
  MYSQL_USER: laravel
  MYSQL_PASSWORD: laravel

back-end:api:lint:
  stage: lint
  image: *php_image
  extends:
    - .back-end:api:working-directory
  script:
    # vendor files
    - composer install --prefer-dist --no-ansi --no-interaction --no-progress
    # Pint
    - ./vendor/bin/pint --bail
  artifacts:
    expire_in: 1 days
    paths:
      - ./back-end/api/vendor/

back-end:api:build:
  stage: build
  when: on_success
  image: *php_image
  extends:
    - .back-end:api:working-directory
  script:
    - cp .env.example .env
  artifacts:
    expire_in: 1 days
    paths:
      - ./back-end/api/app
      - ./back-end/api/bootstrap
      - ./back-end/api/config
      - ./back-end/api/database
      - ./back-end/api/resources
      - ./back-end/api/public
      - ./back-end/api/routes
      - ./back-end/api/vendor
      - ./back-end/api/artisan

back-end:api:test:unit:
  stage: test
  image: *php_image
  when: on_success
  extends:
    - .back-end:api:working-directory
  script:
    # Setup
    - composer install --no-ansi --no-interaction --no-progress
    - cp .env.testing.example .env.testing
    - service mariadb start
    - mysql --user root --password="${MYSQL_ROOT_PASSWORD}" --host=127.0.0.1 --execute="GRANT ALL PRIVILEGES ON *.* TO '${MYSQL_USER}'@'%'; FLUSH PRIVILEGES;"
    # Testing
    - php artisan test --stop-on-failure --display-incomplete --display-skipped --display-deprecations --display-errors --display-notices --display-warnings
  except:
    - tags

back-end:api:deploy:develop:
  variables: {
    ENV: "$DEVELOP_ENV",
    USER: runcloud,
    HOST: ************,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/fource-api/current",
    STORAGE_PATH: "/home/<USER>/webapps/fource-api/storage",
    RELEASE_PATH: "/home/<USER>/webapps/fource-api/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: *php_image
  only:
    - /^release\//
  when: manual
  environment:
    name: develop
    url: $DEVELOP_URL
  extends: .back-end:api:deployment
  script:
    - echo 'deployment successful!'

back-end:api:deploy:toads:
  variables: {
    ENV: "$TOADS_ENV",
    USER: runcloud,
    HOST: ***********,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/toads-api/current",
    STORAGE_PATH: "/home/<USER>/webapps/toads-api/storage",
    RELEASE_PATH: "/home/<USER>/webapps/toads-api/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: *php_image
  only:
    - /^release\//
  when: manual
  environment:
    name: develop
    url: $DEVELOP_URL
  extends: .back-end:api:deployment
  script:
    - echo 'deployment successful!'

back-end:api:deploy:main:
  variables: {
    ENV: "$MAIN_V2_ENV",
    USER: runcloud,
    HOST: ***********,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/fource-api/current",
    STORAGE_PATH: "/home/<USER>/webapps/fource-api/storage",
    RELEASE_PATH: "/home/<USER>/webapps/fource-api/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: *php_image
  only:
    - tags
  when: manual
  environment:
    name: main
    url: https://marketingdashboard.lkqeurope.nl
  extends: .back-end:api:deployment
  script:
    - echo 'deployment successful!'

.back-end:api:deployment:
  before_script:
    # Add SSH
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo "$SSH_KEY" | tr -d '\r' | ssh-add - > /dev/null
    # Create release directory
    - $SSH "mkdir -p $RELEASE_PATH"
    # Create storage directories
    - $SSH "mkdir -p -m 775 $STORAGE_PATH/app/public"
    - $SSH "mkdir -p -m 775 $STORAGE_PATH/framework/cache"
    - $SSH "mkdir -p -m 775 $STORAGE_PATH/framework/views"
    - $SSH "mkdir -p -m 775 $STORAGE_PATH/framework/testing"
    - $SSH "mkdir -p -m 775 $STORAGE_PATH/framework/sessions"
    # Link storage directory to release
    - $SSH "ln -nfs $STORAGE_PATH $RELEASE_PATH/storage"
    # Create .env
    - $SSH "echo '$ENV' > $RELEASE_PATH/.env";
    # Delete default files
    - rm -rf ./.idea
    - rm -rf ./node_modules
    - rm -rf ./tests
    - rm -rf ./storage
    - rm -rf ./package-lock.json
    - rm -rf ./package.json
    - rm -rf ./tailwind.config.js
    - rm -rf ./webpack.mix.js
    - rm -rf ./phpunit.dusk.xml
    - rm -rf ./phpunit.xml
    - rm -rf ./.prettierrc
    - rm -rf ./.prettierignore
    - rm -rf ./.gitignore
    - rm -rf ./.gitattributes
    - rm -rf ./.env.example
    - rm -rf ./.editorconfig
    # Compress files
    - tar -czf artifacts.tar.gz -C ./back-end/api .
    # Upload files
    - scp -P$PORT -r artifacts.tar.gz $USER@$HOST:$RELEASE_PATH
    # Uncompress files
    - $SSH "tar -xzf $RELEASE_PATH/artifacts.tar.gz -C $RELEASE_PATH"
    # Delete archive
    - $SSH "rm -rf $RELEASE_PATH/artifacts.tar.gz"
    # Prepare activation
    - $SSH "/RunCloud/Packages/php83rc/bin/php $RELEASE_PATH/artisan migrate --force"
    - $SSH "/RunCloud/Packages/php83rc/bin/php $RELEASE_PATH/artisan storage:link"
    - $SSH "/RunCloud/Packages/php83rc/bin/php $RELEASE_PATH/artisan cache:clear"
    - $SSH "/RunCloud/Packages/php83rc/bin/php $RELEASE_PATH/artisan route:cache";
    - $SSH "/RunCloud/Packages/php83rc/bin/php $RELEASE_PATH/artisan config:cache";
    # Activate release
    - $SSH "ln -nfs $RELEASE_PATH $CURRENT_PATH";
    # restart runcloud supervisors
    - $SSH "cd ${CURRENT_PATH} && /RunCloud/Packages/php83rc/bin/php artisan runcloud:restart_supervisors";

.back-end:api:working-directory:
  before_script:
    - cd ./back-end/api
