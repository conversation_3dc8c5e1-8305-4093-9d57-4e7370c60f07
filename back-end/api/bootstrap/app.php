<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withCommands([
        __DIR__.'/../app/Domains/Sources/Google/Commands',
        __DIR__.'/../app/Domains/Sources/Mailchimp/Commands',
        __DIR__.'/../app/Domains/Sources/Meta/Commands',
        __DIR__.'/../app/Domains/Dashboard/Commands',
        __DIR__.'/../app/Domains/Support/Commands',
    ])
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->statefulApi();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->create();
